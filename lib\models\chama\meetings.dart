// To parse this JSON data, do
//
//     final meetingModel = meetingModelFromJson(jsonString);

import 'dart:convert';

MeetingModel meetingModelFromJson(String str) => MeetingModel.fromJson(json.decode(str));

String meetingModelToJson(MeetingModel data) => json.encode(data.toJson());

class MeetingModel {
    bool? status;
    String? message;
    List<Meeting>? data;

    MeetingModel({
        this.status,
        this.message,
        this.data,
    });

    factory MeetingModel.fromJson(Map<String, dynamic> json) => MeetingModel(
        status: json["status"],
        message: json["message"],
        data: List<Meeting>.from(json["data"].map((x) => Meeting.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
    };
}

class Meeting {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    String? title;
    String? description;
    String? email;
    String? locationTip;
    String? venue;
    int? memberId;
    int? chamaId;
    double? latitude;
    double? longitude;
    String? frequency;
    String? calendarLink;
    String? status;
    String? eventType;
    DateTime? startDate;
    DateTime? endDate;

    Meeting({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.title,
        this.description,
        this.email,
        this.locationTip,
        this.venue,
        this.memberId,
        this.chamaId,
        this.latitude,
        this.longitude,
        this.frequency,
        this.calendarLink,
        this.status,
        this.eventType,
        this.startDate,
        this.endDate,
    });

    factory Meeting.fromJson(Map<String, dynamic> json) => Meeting(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        description: json["description"],
        email: json["email"],
        locationTip: json["location_tip"],
        venue: json["venue"],
        memberId: json["member_id"],
        chamaId: json["chama_id"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        frequency: json["frequency"],
        calendarLink: json["calendar_link"],
        status: json["status"],
        eventType: json["event_type"],
        startDate: DateTime.parse(json["start_date"]),
        endDate: DateTime.parse(json["end_date"]),
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "email": email,
        "location_tip": locationTip,
        "venue": venue,
        "member_id": memberId,
        "chama_id": chamaId,
        "latitude": latitude,
        "longitude": longitude,
        "frequency": frequency,
        "calendar_link": calendarLink,
        "status": status,
        "event_type": eventType,
        "start_date": startDate!.toIso8601String(),
        "end_date": endDate!.toIso8601String(),
    };
}
