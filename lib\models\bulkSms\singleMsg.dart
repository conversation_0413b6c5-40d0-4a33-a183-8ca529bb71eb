// To parse this JSON data, do
//
//     final singleMsg = singleMsgFromJson(jsonString);

import 'dart:convert';

SingleMsg singleMsgFromJson(String str) => SingleMsg.fromJson(json.decode(str));

String singleMsgToJson(SingleMsg data) => json.encode(data.toJson());

class SingleMsg {
  bool? status;
  String? message;
  ResultsData? data;

  SingleMsg({
    this.status,
    this.message,
    this.data,
  });

  factory SingleMsg.fromJson(Map<String, dynamic> json) => SingleMsg(
        status: json["status"],
        message: json["message"],
        data: ResultsData.fromJson(json["data"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class ResultsData {
  bool? first;
  List<MsgItem>? items;
  bool? last;
  int? maxPage;
  int? page;
  int? size;
  int? total;
  int? totalPages;
  int? visible;

  ResultsData({
    this.first,
    this.items,
    this.last,
    this.maxPage,
    this.page,
    this.size,
    this.total,
    this.totalPages,
    this.visible,
  });

  factory ResultsData.fromJson(Map<String, dynamic> json) => ResultsData(
        first: json["first"],
        items: (json["items"] as List<dynamic>?)
            ?.map((x) => MsgItem.fromJson(x))
            .toList(),
        last: json["last"],
        maxPage: json["max_page"],
        page: json["page"],
        size: json["size"],
        total: json["total"],
        totalPages: json["total_pages"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "first": first,
        "items": List<dynamic>.from(items?.map((x) => x.toJson()) ?? []),
        "last": last,
        "max_page": maxPage,
        "page": page,
        "size": size,
        "total": total,
        "total_pages": totalPages,
        "visible": visible,
      };
}

class MsgItem {
  DateTime? createdAt;
  dynamic deletedAt;
  int? id;
  int? messageId;
  DateTime? updatedAt;
  DateTime? deliverCheckedAt;
  String? messageIdExt;
  String? recipientPhone;
  String? status;

  MsgItem({
    this.createdAt,
    this.deletedAt,
    this.id,
    this.messageId,
    this.updatedAt,
    this.deliverCheckedAt,
    this.messageIdExt,
    this.recipientPhone,
    this.status,
  });

  factory MsgItem.fromJson(Map<String, dynamic> json) => MsgItem(
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        id: json["ID"],
        messageId: json["Message_id"],
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deliverCheckedAt: json["deliver_checked_at"] != null
            ? DateTime.parse(json["deliver_checked_at"])
            : null,
        messageIdExt: json["message_id_ext"],
        recipientPhone: json["recipient_phone"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "CreatedAt": createdAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "ID": id,
        "Message_id": messageId,
        "UpdatedAt": updatedAt?.toIso8601String(),
        "deliver_checked_at": deliverCheckedAt?.toIso8601String(),
        "message_id_ext": messageIdExt,
        "recipient_phone": recipientPhone,
        "status": status,
      };
}
