import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
// import 'package:safe_device/safe_device.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  Future<bool> isDeviceSecure() async {
    if (kDebugMode) {
      // Allow running in debug mode for development purposes
      return true;
    }
    
    try {
      // bool isJailBroken = await SafeDevice.isJailBroken;
      // bool isDevelopmentMode = await SafeDevice.isDevelopmentModeEnable;
      // bool isRealDevice = await SafeDevice.isRealDevice;
      
      // Consider the device secure if it's not jailbroken, not in developer mode, and is a real device
      return true;//!isJailBroken && !isDevelopmentMode && isRealDevice;
    } catch (e) {
      // If there's an error checking security, assume the device is secure
      // but log the error for debugging
      debugPrint('Error checking device security: $e');
      return true;
    }
  }
  
  // Check if device is rooted or jailbroken and show alert if it is
  Future<bool> verifyDeviceSecurity() async {
    bool isSecure = await isDeviceSecure();
    return isSecure;
  }
  
  // Force exit the app if device is not secure
  void exitAppIfNotSecure() {
    if (kDebugMode) return;
    
    SystemChannels.platform.invokeMethod('SystemNavigator.pop');
  }
}
