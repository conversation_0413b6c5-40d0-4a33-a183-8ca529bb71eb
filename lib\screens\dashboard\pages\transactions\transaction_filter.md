# Transaction Filtering System Guide

## Overview
This guide covers the complete implementation of transaction filtering functionality including search by name, filter by phone number, transaction code, date range filtering, and dropdown-based filter selection.

## Dependencies Required

```yaml
dependencies:
  flutter_screenutil: ^5.9.0    # Responsive design
  get: ^4.6.6                   # State management
  intl: ^0.19.0                 # Date formatting
  grouped_list: ^5.1.2          # Grouped list display
```

## Core Filter Architecture

### 1. Filter State Management

```dart
class _AllTransactionsScreenState extends State<AllTransactionsScreen> {
  // Controllers for different filter types
  TextEditingController searchController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  
  // Filter options and state
  List<String> dropdownItemList = ["code", "Date", "Account No"];
  String selectedFilter = "";
  List<TransactionModel> filterbyname = [];
  
  @override
  void initState() {
    super.initState();
    filterbyname = controller.transactionsKitty;
  }
}
```

### 2. Search Bar Implementation

```dart
// Main search bar in app bar
Expanded(
  child: TextField(
    controller: searchController,
    onChanged: (value) {
      // Real-time search filtering
      if (value.isEmpty) {
        filterbyname = List.from(controller.transactionsKitty);
      } else {
        filterbyname = controller.transactionsKitty
            .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                .contains(value.toLowerCase()))
            .toList();
      }
      setState(() {});
    },
    decoration: InputDecoration(
      hintText: "Search transactions...",
      hintStyle: TextStyle(
        color: Theme.of(context).hintColor,
        fontSize: 14.sp,
      ),
      filled: true,
      fillColor: Theme.of(context).scaffoldBackgroundColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(width: 1),
      ),
      prefixIcon: Icon(
        Icons.search_rounded,
        color: Theme.of(context).hintColor,
        size: 24.h,
      ),
      suffixIcon: searchController.text.isNotEmpty
          ? IconButton(
              icon: Icon(Icons.clear_rounded,
                  color: Theme.of(context).hintColor),
              onPressed: () => searchController.clear(),
            )
          : null,
      contentPadding: EdgeInsets.symmetric(vertical: 12.h),
    ),
    style: TextStyle(
      fontSize: 14.sp,
      color: Theme.of(context).colorScheme.onSurface,
    ),
  ),
),
```

### 3. Filter Dropdown Implementation

```dart
// Filter dropdown selector
CustomDropDown(
  width: 150.w,
  hintText: "Filter",
  hintStyle: TextStyle(fontSize: 12.h),
  items: dropdownItemList,
  prefix: Icon(
    Icons.filter_alt_rounded,
    size: 20.h,
  ),
  onChanged: (value) {
    setState(() => selectedFilter = value);
  },
),
```

## Filter UI Components

### 1. Phone Number Filter UI

```dart
Widget _buildPhoneNoFilterUI(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 12.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomSearchView(
          width: 170.w,
          controller: phoneController,
          hintText: "Filter phone number",
          contentPadding: EdgeInsets.all(5.h),
          onChanged: (p0) {
            if (p0.length == 10) {
              String newPhoneNumber = p0.substring(1);
              phoneController.text = newPhoneNumber;
              _updateFilter();
            }
          },
        ),
        InkWell(
          onTap: () {
            setState(() {
              selectedFilter = "Filter";
              phoneController.clear();
            });
          },
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
          ),
        ),
      ],
    ),
  );
}
```

### 2. Transaction Code Filter UI

```dart
Widget _buildCodeFilterUI(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 12.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomSearchView(
          width: 170.w,
          controller: codeController,
          hintText: "Filter with code",
          contentPadding: EdgeInsets.all(5.h),
          onChanged: (p0) {
            _updateFilter();
          },
        ),
        InkWell(
          onTap: () {
            setState(() {
              selectedFilter = "Filter";
              codeController.clear();
            });
          },
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
          ),
        ),
      ],
    ),
  );
}
```

### 3. Date Range Filter UI

```dart
Widget _buildDateFilterUI(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.only(left: 12.0, right: 8.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Start Date Field
        SizedBox(
          width: 120.w,
          height: 35.h,
          child: TextFormField(
            controller: startDate,
            style: const TextStyle(fontSize: 15),
            readOnly: false,
            onTap: () async {
              final DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );

              if (pickedDate != null) {
                final formattedDate =
                    DateFormat('yyyy-MM-dd').format(pickedDate);
                startDate.text = formattedDate;
              }
            },
            decoration: InputDecoration(
              labelText: 'Start Date',
              border: const OutlineInputBorder(
                borderSide: BorderSide(color: Colors.black87),
              ),
              focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87)),
              suffixIcon: Icon(
                Icons.calendar_today,
                size: 18.h,
              ),
            ),
          ),
        ),
        SizedBox(width: 8.w),
        
        // End Date Field
        SizedBox(
          width: 120.w,
          height: 35.h,
          child: TextFormField(
            controller: endDate,
            style: const TextStyle(fontSize: 15),
            readOnly: false,
            onTap: () async {
              final DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );

              if (pickedDate != null) {
                final formattedDate =
                    DateFormat('yyyy-MM-dd').format(pickedDate);
                endDate.text = formattedDate;
                _updateFilter();
              }
            },
            decoration: InputDecoration(
              labelText: 'End Date',
              border: const OutlineInputBorder(
                borderSide: BorderSide(color: Colors.black87),
              ),
              focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87)),
              suffixIcon: Icon(
                Icons.calendar_today,
                size: 18.h,
              ),
            ),
          ),
        ),
        SizedBox(width: 8.w),
        
        // Clear Filter Button
        InkWell(
          onTap: () {
            setState(() {
              selectedFilter = "Filter";
              startDate.clear();
              endDate.clear();
            });
          },
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
          ),
        ),
      ],
    ),
  );
}
```

## Filter Logic Implementation

### 1. Filter Update Method

```dart
void _updateFilter() {
  setState(() {
    _fetchFilteredTransactions();
  });
}
```

### 2. API Filter Request

```dart
void _fetchFilteredTransactions() async {
  try {
    await Get.find<KittyController>().getKittyFiltrContributions(
      eventId: widget.eventId,
      kittyId: dataController.kitty.value.kitty?.id ?? 0,
      startDate: startDate.text,
      endDate: endDate.text,
      phoneNumber: phoneController.text,
      code: codeController.text,
    );

    setState(() {
      Get.find<KittyController>().loadingfiltrTransactions(false);
    });
  } catch (e) {
    throw e;
  }
}
```

### 3. Controller Filter Method

```dart
// In KittyController
getKittyFiltrContributions({
  required int kittyId,
  int? eventId,
  int? page = 1,
  int? size = 100,
  String? startDate,
  String? endDate,
  String? code,
  String? search,
  String? phoneNumber,
}) async {
  update();
  loadingfiltrTransactions(true);
  try {
    update();
    // Build URL based on context
    String url = eventId != null
        ? "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId"
        : "${ApiUrls.filterContribs}?kitty_id=$kittyId";

    // Add filter parameters
    if (startDate?.isNotEmpty == true && endDate?.isNotEmpty == true) {
      url += "&start-date=$startDate&end-date=$endDate";
    }
    if (phoneNumber?.isNotEmpty == true) {
      url += "&phone_number=$phoneNumber";
    }
    if (code?.isNotEmpty == true) {
      url += "&transaction_code=$code";
    }
    if (search?.isNotEmpty == true) {
      url += "&search=$search";
    }

    var resp = await apiProvider.request(
      url: url,
      method: Method.GET,
    );

    if (resp.statusCode == 200) {
      filtrtransactions.clear();
      for (var element in resp.data["data"]["items"] ?? []) {
        filtrtransactions.add(TransactionModel.fromJson(element));
      }
    }
    
    loadingfiltrTransactions(false);
  } catch (e) {
    logger.e(e);
    loadingfiltrTransactions(false);
  }
  update();
}
```

## Main UI Layout with Conditional Filters

```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      // Search bar and filter dropdown in app bar
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(90.h),
        child: ColoredBox(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                SizedBox(width: 12.w),
                // Search TextField
                Expanded(child: /* Search implementation */),
                SizedBox(width: 12.w),
                // Filter Dropdown
                CustomDropDown(/* Dropdown implementation */),
                SizedBox(width: 12.w),
              ],
            ),
          ),
        ),
      ),
    ),
    body: Container(
      child: Column(
        children: [
          // Conditional filter UI based on selected filter
          if (selectedFilter == "Account No") _buildPhoneNoFilterUI(context),
          if (selectedFilter == "code") _buildCodeFilterUI(context),
          if (selectedFilter == "Date") _buildDateFilterUI(context),
          if (selectedFilter == "") SizedBox(height: 3.h),
          
          // Transaction list
          Expanded(
            child: Container(
              child: Column(
                children: [
                  // Show filtered or normal transactions
                  startDate.text.isNotEmpty && endDate.text.isNotEmpty ||
                          phoneController.text.isNotEmpty ||
                          codeController.text.isNotEmpty
                      ? _buildFilteredTransactionsList(context)
                      : _buildTransactionsList(context)
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
```

## Key Features

1. **Real-time Search**: Instant filtering as user types
2. **Multiple Filter Types**: Phone, code, date range
3. **Conditional UI**: Shows relevant filter UI based on selection
4. **API Integration**: Server-side filtering for performance
5. **Clear Functionality**: Easy filter reset with cancel buttons
6. **Date Picker Integration**: Native date selection
7. **Responsive Design**: Uses flutter_screenutil for scaling
8. **Loading States**: Shows loading indicators during API calls

## Filter Types Supported

- **Name Search**: Real-time search by first name
- **Phone Number**: Filter by phone number (10 digits)
- **Transaction Code**: Filter by transaction reference code
- **Date Range**: Filter by start and end date
- **Account Number**: Filter by account number

This implementation provides a complete, robust transaction filtering system with multiple filter options, real-time search, and proper state management.
