// To parse this JSON data, do
//
//     final penalizeMemberRequest = penalizeMemberRequestFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/chama/chama_memebers_model.dart';

PenalizeMemberRequest penalizeMemberRequestFromJson(String str) =>
    PenalizeMemberRequest.fromJson(json.decode(str));

String penalizeMemberRequestToJson(PenalizeMemberRequest data) =>
    json.encode(data.toJson());

class PenalizeMemberRequest {
  int? chamaId;
  int? memberId;
  int? amount;
  int? penaltyId;
  String? reason;

  PenalizeMemberRequest({
    this.chamaId,
    this.memberId,
    this.amount,
    this.penaltyId,
    this.reason,
  });

  factory PenalizeMemberRequest.fromJson(Map<String, dynamic> json) =>
      PenalizeMemberRequest(
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        amount: json["amount"],
        penaltyId: json["penalty_id"],
        reason: json["reason"],
      );

  Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "member_id": memberId,
        "amount": amount,
        "penalty_id": penaltyId,
        "reason": reason,
      };
}

// To parse this JSON data, do
//
//     final multiplePenaltyRequest = multiplePenaltyRequestFromJson(jsonString);

MultiplePenaltyRequest multiplePenaltyRequestFromJson(String str) =>
    MultiplePenaltyRequest.fromJson(json.decode(str));

String multiplePenaltyRequestToJson(MultiplePenaltyRequest data) =>
    json.encode(data.toJson());

class MultiplePenaltyRequest {
  int? chamaId;
  int? penaltyId;
  bool? isAll;
  List<MemberBeingPenalized>? members;

  MultiplePenaltyRequest({
    this.chamaId,
    this.penaltyId,
    this.isAll,
    this.members,
  });

  factory MultiplePenaltyRequest.fromJson(Map<String, dynamic> json) =>
      MultiplePenaltyRequest(
        chamaId: json["chama_id"],
        penaltyId: json["penalty_id"],
        isAll: json["is_all"],
        members: List<MemberBeingPenalized>.from(
            json["members"].map((x) => MemberBeingPenalized.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "penalty_id": penaltyId,
        "is_all": isAll,
        "members": List<dynamic>.from(members!.map((x) => x.toJson())),
      };
}

class MemberBeingPenalized {
  int? memberId;
  int? amount;
  String? reason;

  MemberBeingPenalized({
    this.memberId,
    this.amount,
    this.reason,
  });

  factory MemberBeingPenalized.fromJson(Map<String, dynamic> json) =>
      MemberBeingPenalized(
        memberId: json["member_id"],
        amount: json["amount"],
        reason: json["reason"],
      );

  Map<String, dynamic> toJson() => {
        "member_id": memberId,
        "amount": amount,
        "reason": reason,
      };
}
extension ChamaMembersExtension on ChamaMembers {
  MemberBeingPenalized toMemberBeingPenalized({int? amount, String? reason}) {
    return MemberBeingPenalized(
      memberId: id,
      amount: amount,
      reason: reason,
    );
  }
}