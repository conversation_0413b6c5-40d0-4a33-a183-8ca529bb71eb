// To parse this JSON data, do
//
//     final profileRequest = profileRequestFromJson(jsonString);

import 'dart:convert';

ProfileRequest profileRequestFromJson(String str) =>
    ProfileRequest.fromJson(json.decode(str));

String profileRequestToJson(ProfileRequest data) => json.encode(data.toJson());

class ProfileRequest {
  int? id;
  String? firstName;
  String? secondName;
  String? email;
  DateTime? birthDate;
  String? county;
  String? latitude;
  String? longitude;
  String? profileUrl;
  String? phoneNumber;

  ProfileRequest({
    this.id,
    this.firstName,
    this.secondName,
    this.email,
    this.birthDate,
    this.county,
    this.latitude,
    this.longitude,
    this.profileUrl,
    this.phoneNumber,
  });

  factory ProfileRequest.fromJson(Map<String, dynamic> json) => ProfileRequest(
        id: json["ID"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        email: json["email"],
        birthDate: DateTime.parse(json["birth_date"]),
        county: json["county"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        profileUrl: json["profile_url"],
        phoneNumber: json["phone_number"] ?? ""
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "first_name": firstName,
        "second_name": secondName,
        "email": email,
        "birth_date":
            "${birthDate?.year.toString().padLeft(4, '0')}-${birthDate?.month.toString().padLeft(2, '0')}-${birthDate?.day.toString().padLeft(2, '0')}",
        "county": county,
        "latitude": latitude,
        "longitude": longitude,
        "profile_url": profileUrl,
        "phone_number": phoneNumber
      };
}
