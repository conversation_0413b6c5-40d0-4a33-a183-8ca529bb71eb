# Design Document

## Overview

The transaction details editing feature will enable users to modify transaction information across all transaction types (chama, kitty, and events) in the OneKitty mobile application. The design leverages the existing transaction infrastructure and introduces a unified editing interface with role-based permissions for enhanced functionality.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Transaction List UI] --> B[Edit Transaction Dialog]
    B --> C[Transaction Edit Service]
    C --> D[HTTP Service]
    D --> E[API Endpoint: kitty/transaction-details/]
    E --> F[Backend Processing]
    F --> G[Database Update]
    G --> H[Response]
    H --> D
    D --> C
    C --> I[UI Update]
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant TL as Transaction List
    participant ED as Edit Dialog
    participant TS as Transaction Service
    participant API as HTTP Service
    participant BE as Backend

    U->>TL: Tap Edit Transaction
    TL->>ED: Open Edit Dialog
    ED->>ED: Pre-populate Fields
    U->>ED: Modify Fields
    U->>ED: Submit Changes
    ED->>TS: Process Edit Request
    TS->>API: PUT Request
    API->>BE: Update Transaction
    BE-->>API: Success/Error Response
    API-->>TS: Response
    TS-->>ED: Update Result
    ED-->>TL: Refresh Transaction List
    TL-->>U: Show Updated Transaction
```

## Components and Interfaces

### 1. Transaction Edit Service

**Purpose**: Handle all transaction editing operations and API communication.

**Key Methods**:
- `updateTransactionDetails(TransactionEditRequest request)`: Main method for updating transaction details
- `validateEditPermissions(String userId, TransactionModel transaction)`: Check if user can edit the transaction
- `buildEditPayload(TransactionModel transaction, EditFormData formData)`: Construct API payload

**Dependencies**:
- HttpService for API communication
- TransactionModel for data structure
- User authentication service for permissions

### 2. Transaction Edit Dialog Widget

**Purpose**: Provide a consistent UI for editing transaction details across all transaction types.

**Key Features**:
- Pre-populated form fields with current transaction data
- Conditional field visibility based on user role and transaction type
- Form validation and error handling
- Loading states during API calls

**Input Parameters**:
- `TransactionModel transaction`: The transaction to edit
- `String transactionType`: Type of transaction (kitty, chama, event)
- `bool isAdmin`: Whether user has admin privileges for this transaction

### 3. Transaction Edit Request Model

**Purpose**: Structure the data for API requests.

```dart
class TransactionEditRequest {
  final String internalId;
  final String? newFirstName;
  final String? newSecondName;
  final String? newPaymentRef;
  final bool? showNames;
  final String reason;
  
  TransactionEditRequest({
    required this.internalId,
    this.newFirstName,
    this.newSecondName,
    this.newPaymentRef,
    this.showNames,
    this.reason = "user request",
  });
}
```

### 4. Enhanced Transaction List Components

**Purpose**: Add edit functionality to existing transaction list widgets.

**Modifications Required**:
- Add edit button/icon to transaction items
- Integrate edit dialog launching
- Handle transaction list refresh after edits
- Show edit permissions based on user role

## Data Models

### Transaction Edit Form Data

```dart
class TransactionEditFormData {
  String firstName;
  String secondName;
  String? paymentRef; // Only for kitty admin
  bool? showNames; // Only for kitty admin
  
  TransactionEditFormData({
    required this.firstName,
    required this.secondName,
    this.paymentRef,
    this.showNames,
  });
}
```

### API Response Model

```dart
class TransactionEditResponse {
  final bool success;
  final String message;
  final TransactionModel? updatedTransaction;
  final Map<String, String>? errors;
  
  TransactionEditResponse({
    required this.success,
    required this.message,
    this.updatedTransaction,
    this.errors,
  });
}
```

## Error Handling

### Client-Side Validation

1. **Field Validation**:
   - First name and second name cannot be empty
   - Names must contain only valid characters
   - Payment reference format validation (if applicable)

2. **Permission Validation**:
   - Check user permissions before showing edit options
   - Validate admin status for kitty transactions

### API Error Handling

1. **Network Errors**:
   - Connection timeout handling
   - Retry mechanism for failed requests
   - Offline state management

2. **Server Errors**:
   - 400: Validation errors - show field-specific messages
   - 401: Unauthorized - redirect to login
   - 403: Forbidden - show permission denied message
   - 404: Transaction not found - show appropriate error
   - 500: Server error - show generic error with retry option

### Error Display Strategy

```dart
class TransactionEditErrorHandler {
  static void handleError(dynamic error, BuildContext context) {
    if (error is DioError) {
      switch (error.response?.statusCode) {
        case 400:
          _showValidationErrors(error.response?.data, context);
          break;
        case 401:
          _redirectToLogin(context);
          break;
        case 403:
          _showPermissionError(context);
          break;
        default:
          _showGenericError(context);
      }
    } else {
      _showGenericError(context);
    }
  }
}
```

## Testing Strategy

### Unit Tests

1. **Transaction Edit Service Tests**:
   - Test API payload construction
   - Test permission validation logic
   - Test error handling scenarios
   - Mock HTTP service responses

2. **Form Validation Tests**:
   - Test field validation rules
   - Test form state management
   - Test conditional field visibility

### Integration Tests

1. **API Integration Tests**:
   - Test successful transaction updates
   - Test various error scenarios
   - Test network failure handling

2. **UI Integration Tests**:
   - Test edit dialog functionality
   - Test transaction list refresh
   - Test permission-based UI changes

### Widget Tests

1. **Edit Dialog Tests**:
   - Test form rendering with pre-populated data
   - Test user interactions and form submission
   - Test loading states and error displays

2. **Transaction List Tests**:
   - Test edit button visibility
   - Test dialog launching
   - Test list refresh after edits

## Security Considerations

### Authentication and Authorization

1. **User Authentication**:
   - Verify user is authenticated before allowing edits
   - Use Firebase token for API authentication
   - Handle token expiration gracefully

2. **Permission Checks**:
   - Validate user permissions on client side
   - Server-side permission validation as final authority
   - Role-based access control for admin features

### Data Validation

1. **Input Sanitization**:
   - Sanitize all user inputs before API calls
   - Prevent injection attacks through form fields
   - Validate data types and formats

2. **API Security**:
   - Use HTTPS for all API communications
   - Include proper headers and authentication tokens
   - Implement request rate limiting considerations

## Performance Considerations

### Optimization Strategies

1. **Efficient API Calls**:
   - Only send changed fields in API payload
   - Implement request debouncing for rapid edits
   - Cache transaction data to reduce API calls

2. **UI Performance**:
   - Use efficient state management for form data
   - Implement proper widget disposal
   - Optimize list rendering after updates

### Caching Strategy

1. **Transaction Data Caching**:
   - Cache updated transaction data locally
   - Implement cache invalidation on successful edits
   - Handle cache consistency across app sessions

## Implementation Phases

### Phase 1: Core Infrastructure
- Create transaction edit service
- Implement API integration
- Create basic edit dialog widget

### Phase 2: UI Integration
- Integrate edit functionality into existing transaction lists
- Implement permission-based UI changes
- Add loading states and error handling

### Phase 3: Enhanced Features
- Add admin-specific features for kitty transactions
- Implement advanced validation
- Add comprehensive error handling

### Phase 4: Testing and Polish
- Comprehensive testing across all transaction types
- Performance optimization
- UI/UX refinements