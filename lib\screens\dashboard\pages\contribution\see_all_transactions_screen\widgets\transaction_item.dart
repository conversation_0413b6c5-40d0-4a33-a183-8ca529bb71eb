// import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/colors.dart';

import 'package:onekitty/models/transaction_edit_models.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/widgets/transaction_edit_dialog.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';

import '../../../../../../controllers/user_ktty_controller.dart';
import '../../../../../../utils/utils_exports.dart';

class TransactionItem extends StatelessWidget {
  final TransactionModel item;
  final int? eventId;
  const TransactionItem({
    super.key,
    required this.item,
    this.eventId,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showTransactionDialog(
          context: context,
          details: item,
          eventId: eventId,
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 16.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.circleBorder22),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadiusStyle.roundedBorder6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 8.h),
                      child: Container(
                        margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                        padding: EdgeInsets.all(7.h),
                        decoration: AppDecoration.fillAGray
                            .copyWith(shape: BoxShape.circle),
                        child: Padding(
                          padding: const EdgeInsets.all(6.0),
                          child: Text(
                            '${item.firstName?.isNotEmpty ?? false ? item.firstName![0] : ' '}${item.secondName?.isNotEmpty ?? false ? item.secondName![0] : ' '}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 14.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                '${item.firstName ?? ""} ${item.secondName ?? ""}',
                                style: CustomTextStyles.titleSmallIndigo500),
                                if(item.email != null)
                                Text('${item.email}'),
                            SizedBox(height: 7.h),
                            Opacity(
                                opacity: 0.4,
                                child: Text(
                                    '${item.phoneNumber ?? item.transactionCode}',
                                    style: theme.textTheme.bodySmall))
                          ],
                        ),
                      ),
                    ),
                    Column(
                      children: [
                        Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              '${item.typeInOut == "OUT" ? '-' : '+'} ${FormattedCurrency.getFormattedCurrency(item.amount.toString())}',
                              style: TextStyle(
                                color: '${item.typeInOut}' == "OUT"
                                    ? Colors.red
                                    : Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            )

                            // style: TextStyle(
                            //     color: item.transactionType == "Contribution"
                            //         ? Colors.green
                            //         : Colors.red,
                            //     fontWeight: FontWeight.bold,
                            //     fontSize: 12),
                            ),
                        SizedBox(height: 2.h),
                        Opacity(
                          opacity: 0.4,
                          child: Text(
                              DateFormat.jm().format(item.createdAt!.toLocal()),
                              style: theme.textTheme.bodySmall),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

//Sigle transaction dialog

showTransactionDialog({
  required BuildContext context,
  required TransactionModel details,
  bool singleTrans = true,
  int? eventId,
}) {
  return showAnimatedDialog(
    barrierDismissible: true,
    animationType: DialogTransitionType.sizeFade,
    curve: Curves.fastOutSlowIn,
    duration: const Duration(milliseconds: 900),
    context: context,
    builder: (BuildContext context) {
      final DateFormat format = DateFormat.MMMEd().add_jms();
      DateTime createdAt = details.createdAt!.toLocal();
      final DataController dataController = Get.find<DataController>();
      return Dialog(
        child: SizedBox(
          // height: SizeConfig.screenHeight * .50,
          width: SizeConfig.screenWidth * .8,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: getProportionalScreenWidth(10)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                    padding: EdgeInsets.all(7.h),
                    decoration: AppDecoration.fillAGray
                        .copyWith(shape: BoxShape.circle),
                    child: Center(
                      child: Text(
                        '${details.firstName?.isNotEmpty ?? false ? details.firstName![0] : '  '}${details.secondName?.isNotEmpty ?? false ? details.secondName![0] : '  '}',
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold),
                      ),
                    )),
                SizedBox(height: 9.h),
                Text(dataController.kitty.value.kitty?.title ?? '',
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: CustomTextStyles.titleSmallIndigo500),
                SizedBox(height: 7.h),
                Text(
                  '${details.typeInOut == "OUT" ? '-' : '+'} ${details.currencyCode ?? "KES"} ${details.amount.toString()}',
                  style: TextStyle(
                    color: '${details.typeInOut}' == "OUT"
                        ? Colors.red
                        : Colors.green,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                SizedBox(height: 7.h),
                Text("${details.firstName ?? ""} ${details.secondName ?? ""}",
                    style: const TextStyle()),
                    if(details.email!= null)
                    Text(details.email.toString(), style: const TextStyle()),
                SizedBox(height: 5.h),
                Text(details.phoneNumber.toString(), style: const TextStyle()),
                SizedBox(height: 7.h),
                Text("Transaction ID: ${details.transactionCode ?? ''}",
                    style: const TextStyle()),
                SizedBox(height: 7.h),
                Text(
                  '${details.status}',
                  style: TextStyle(
                      color: details.status == "SUCCESS"
                          ? Colors.green
                          : details.status?.toUpperCase().contains("FAILED") ??
                                  false
                              ? Colors.red
                              : const Color(0xFF95730C)),
                ),
                SizedBox(height: 8.h),
                Padding(
                  padding: EdgeInsets.only(bottom: 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).canvasColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.sp),
                            side: BorderSide(
                                width: 2.sp,
                                color: Theme.of(context).primaryColor),
                          ),
                        ),
                        onPressed: () async {
                          String shareMsg =
                              "Title: ${dataController.kitty.value.kitty?.title}\nPhone Number: ${details.phoneNumber}\nAmount: KSH ${details.amount}\nName: ${details.firstName ?? ""} ${details.secondName ?? ""}\nTransaction Code: ${details.transactionCode}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${details.kittyId}";
                          await Share.share(shareMsg,
                              subject: 'Transaction details');
                        },
                        child: const Text(
                          "Share",
                          style: TextStyle(color: Colors.black),
                        ),
                      ),
                      const Spacer(),
                       if (details.product == "CONTRIBUTIONS" && details.typeInOut?.toLowerCase() == "in")
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.sp),
                          ),
                        ),
                     
                        onPressed: () {
                            Navigator.of(context).pop(); // Close current dialog
                            _showEditTransactionDialog(context, details);
                          },
                        child: const Text(
                          "Edit",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      const Spacer(),
                      CustomElevatedButton(
                        buttonStyle: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all<Color>(
                              const Color.fromARGB(255, 184, 129, 57)),
                        ),
                        width: 90.w,
                        height: 30.h,
                        text: "Export",
                        buttonTextStyle: TextStyle(
                            fontSize: 12.h, fontWeight: FontWeight.bold),
                        leftIcon: Container(
                            margin: EdgeInsets.only(right: 1.w),
                            child: CustomImageView(
                                imagePath: AssetUrl.expIcon,
                                height: 12.h,
                                width: 12.w)),
                        onPressed: () async {
                          showModalBottomSheet(
                            context: context,
                            builder: (BuildContext context) {
                              return ExportContentWidget2(
                                eventId: eventId,
                                details: details,
                                singleTrans: singleTrans,
                              );
                            },
                          );
                        },
                      ),
                      SizedBox(
                        height: 10.sp,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}
// Function to show edit transaction dialog
void _showEditTransactionDialog(
    BuildContext context, TransactionModel transaction) {
  final UserKittyController controller = Get.find<UserKittyController>();
  final transactionModel = transaction;

  showAnimatedDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return TransactionEditDialog(
        transaction: transactionModel,
        transactionType: TransactionType.kitty, // Default to kitty type
        isAdmin: false, // Default to false for user transactions
        onSave: (formData) async {
          // Create the edit request
          final editRequest = TransactionEditRequest(
            internalId: transaction.internalId ?? '',
            newFirstName: formData.firstName,
            newSecondName: formData.secondName,
            newPaymentRef: formData.paymentRef,
            showNames: formData.showNames,
            reason: 'User requested edit',
          );

          // Call the edit transaction method
          final response = await controller.editTransaction(editRequest);

          if (response.success) {
            Navigator.of(context).pop(); // Close the edit dialog
            Get.snackbar(
              'Success',
              'Transaction updated successfully',
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          } else {
            Get.snackbar(
              'Error',
              response.message,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        },
      );
    },
    animationType: DialogTransitionType.slideFromBottom,
    curve: Curves.fastOutSlowIn,
    duration: const Duration(milliseconds: 300),
  );
}

