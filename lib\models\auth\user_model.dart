import 'dart:convert';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
  bool? status;
  String? message;
  Data? data;

  UserModel({
    this.status,
    this.message,
    this.data,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        status: json["status"],
        message: json["message"],
        data: Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
      };
}

class Data {
  MerchantModel? merchant;
  UserModelLatest? user;

  Data({
    this.merchant,
    this.user,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        merchant: MerchantModel.fromJson(json["merchant"]),
        user: UserModelLatest.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "merchant": merchant?.toJson(),
        "user": user?.toJson(),
      };
}

class MerchantModel {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? merchantName;
  int? merchantCode;
  double? merchantPercent;
  double? balance;

  MerchantModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.merchantName,
    this.merchantCode,
    this.merchantPercent,
    this.balance,
  });

  factory MerchantModel.fromJson(Map<String, dynamic> json) => MerchantModel(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["UserID"],
        merchantName: json["merchant_name"],
        merchantCode: json["merchant_code"],
        merchantPercent: json["merchant_percent"]?.toDouble(),
        balance: double.tryParse(json["balance"]?.toString() ?? "0.0"),
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "UserID": userId,
        "merchant_name": merchantName,
        "merchant_code": merchantCode,
        "merchant_percent": merchantPercent,
        "balance": balance
      };
}

class UserModelLatest {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? phoneNumber;
  String? firstName;
  String? secondName;
  String? email;
  int? idNumber;
  double? balance;
  String? birthDate;
  String? countryCode;
  String? county;
  String? subCounty;
  String? latitude;
  String? longitude;
  String? secondaryNumber;
  String? profileUrl;
  int? status;
  int? kycStatus;

  UserModelLatest({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.email,
    this.idNumber,
    this.balance,
    this.birthDate,
    this.countryCode,
    this.county,
    this.subCounty,
    this.latitude,
    this.longitude,
    this.secondaryNumber,
    this.profileUrl,
    this.status,
    this.kycStatus,
  });

  factory UserModelLatest.fromJson(Map<String, dynamic> json) =>
      UserModelLatest(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        phoneNumber: json["phone_number"] ?? "",
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        email: json["email"],
        idNumber: json["id_number"],
        balance: json["balance"]?.toDouble(),
        birthDate: json["birth_date"],
        countryCode: json["country_code"],
        county: json["county"],
        subCounty: json["sub_county"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        secondaryNumber: json["secondary_number"],
        profileUrl: json["profile_url"],
        status: json["status"],
        kycStatus: json['kyc_status'] ?? 0
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber,
        "first_name": firstName,
        "second_name": secondName,
        "email": email,
        "id_number": idNumber,
        "balance": balance,
        "birth_date": birthDate,
        "country_code": countryCode,
        "county": county,
        "sub_county": subCounty,
        "latitude": latitude,
        "longitude": longitude,
        "secondary_number": secondaryNumber,
        "profile_url": profileUrl,
        "status": status,
        'kyc_status' : kycStatus
      };
}

class SetMrchtDto {
  final String? merchantName;
  final int? merchantCode;
  final int userId;
  final String? phoneNumber;

  SetMrchtDto({
    this.merchantName,
    this.merchantCode,
    required this.userId,
    this.phoneNumber,
  });

  factory SetMrchtDto.fromJson(Map<String, dynamic> json) {
    return SetMrchtDto(
      merchantName: json['merchant_name'],
      merchantCode: json['merchant_code'],
      userId: json['user_id'],
      phoneNumber: json['phone_number'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'merchant_name': merchantName,
      'merchant_code': merchantCode,
      'user_id': userId,
      'phone_number': phoneNumber,
    };
  }
}
