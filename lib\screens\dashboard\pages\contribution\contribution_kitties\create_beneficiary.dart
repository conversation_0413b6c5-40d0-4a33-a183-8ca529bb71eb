import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/models/auth/payments_channels.dart' as pm;
import 'package:onekitty/models/kitty/beneficiary_model.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/main.dart' show isLight;

class CreateBeneficiary extends StatefulWidget {
  final BeneficiaryModel? edit;
  final int kittyId;
  const CreateBeneficiary({super.key, required this.kittyId, this.edit});

  @override
  State<CreateBeneficiary> createState() => _CreateBeneficiaryState();
}

class _CreateBeneficiaryState extends State<CreateBeneficiary> {
  late final PageController pageController;
  final controller = Get.find<BeneficiaryController>();
  final accountNameController = TextEditingController();
  final phoneNumber = TextEditingController();
  final percentageAmountController = TextEditingController();
  final endDateController = TextEditingController();
  final accountNumber = TextEditingController();
  final accountRef = TextEditingController();
  final includeEndDate = false.obs;
  String transferMode = 'WALLET';
  double? amount = 0;
  final Rx<double?> percentage = 0.0.obs;
  final splitConfig = 'Amount'.obs;
  PhoneNumber number = PhoneNumber(isoCode: 'KE', dialCode: '+254');
  final accNumber = PhoneNumber(isoCode: 'KE', dialCode: '+254').obs;
  bool firstTime = false;
  final maxPercentage = 0.0.obs;

  void loadValues() async {
    final beneficiary = widget.edit!;
    accountNameController.text = beneficiary.accountName ?? '';
    if (beneficiary.phoneNumber.isNotEmpty) {
      number =
          PhoneNumber(phoneNumber: beneficiary.phoneNumber.replaceAll(' ', ''));
      phoneNumber.text =
          beneficiary.phoneNumber.replaceAll(' ', '').replaceAll('+', '');
    }

    transferMode = beneficiary.transferMode;

    // Set payment-specific information based on transfer mode
    if (transferMode == 'WALLET') {
      // Mobile Money
      controller.page.value = 0;
      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;

      // Select the correct channel radio button
      if (beneficiary.channelName == 'AirtelMoney') {
        controller.setChannel('AirtelMoney', 63903);
      } else if (beneficiary.channelName == 'SasaPay') {
        controller.setChannel('SasaPay', 0);
      } else {
        controller.setChannel('M-PESA', 63902);
      }
    } else if (transferMode == 'PAYBILL') {
      // Paybill
      controller.page.value = 1;

      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;
      accountNumber.text = beneficiary.accountNumber;
      accountRef.text = beneficiary.accountNumberRef;
    } else if (transferMode == 'TILL') {
      // Till
      controller.page.value = 2;
      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;
      accountNumber.text = beneficiary.accountNumber;
    } else if (transferMode == 'BANK') {
      // Bank
      controller.page.value = 3;
      controller.channelName.value = 'BANK';

      // Find and set the selected bank
      try {
        final banksList = Get.find<GlobalControllers>()
            .paymentChannels
            .where((e) => e.category == pm.Category.BANK)
            .toList();

        if (banksList.isNotEmpty) {
          var matchingBank = banksList
              .where((e) => e.channelCode == beneficiary.channel)
              .toList();
          if (matchingBank.isNotEmpty) {
            controller.selectedBank.value = matchingBank.first;
          } else {
            controller.selectedBank.value = banksList.first;
          }

          if (controller.selectedBank.value != null) {
            controller.channel = controller.selectedBank.value!.channelCode;
          }
        }

        accountNumber.text = beneficiary.accountNumber;
      } catch (e) {
        print("Error setting bank: $e");
      }
    }

    percentageAmountController.text = beneficiary.splitConfig == "PERCENTAGE"
        ? ((beneficiary.percentage ?? 0) * 100).toInt().toString()
        : beneficiary.amount.toString();

    includeEndDate(beneficiary.endDate != null);
    if (beneficiary.endDate != null) {
      endDateController.text = DateFormat('d MMM yyyy HH : mm a')
          .format(beneficiary.endDate?.toLocal() ?? DateTime.now());
    }

    amount = double.tryParse(beneficiary.amount.toString());
    percentage.value = (beneficiary.percentage?.toDouble() ??
            controller.remainingPercentage.value) *
        100;

    print(
        'percentage :${beneficiary.percentage?.toDouble()}\nremaining:${controller.remainingPercentage.value}');
    if (percentage.value != null &&
        percentage.value! > controller.remainingPercentage.value) {
      maxPercentage.value =
          percentage.value! + (controller.remainingPercentage.value * 100);
    } else {
      maxPercentage.value = controller.remainingPercentage.value * 100;
    }

    splitConfig(
        capitalizeFirstLetter(beneficiary.splitConfig?.toLowerCase() ?? ''));

    // // Important: Update the UI after setting values
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (pageController.hasClients) {
    //     print(
    //         'PageController has clients, jumping to page: ${controller.page.value}');
    //     pageController.jumpToPage(controller.page.value);
    //     // Force a UI refresh
    //     setState(() {});
    //   } else {
    //     print('PageController has no clients yet');
    //   }
    // });
  }

  clearText() {
    accountNumber.clear();
    accountRef.clear();
  }

  String capitalizeFirstLetter(String text) {
    if (text.isEmpty) {
      return text;
    }
    return text[0].toUpperCase() + text.substring(1);
  }

  @override
  void initState() {
    
      if (widget.edit != null) {
        loadValues();
      } else {
        // Set default values only for new beneficiary
        controller.page.value = 0;

        maxPercentage.value = controller.remainingPercentage.value * 100;
      }
      pageController = PageController(initialPage: controller.page.value);
    
    
    super.initState();
  }

  @override
  void dispose() {
    accountNameController.dispose();
    phoneNumber.dispose();
    percentageAmountController.dispose();
    endDateController.dispose();
    accountNumber.dispose();
    accountRef.dispose();
    includeEndDate(false);
    transferMode = 'WALLET';
    amount = 0;
    percentage.value = 0.0;
    splitConfig.value = 'Amount';
    number = PhoneNumber(isoCode: 'KE', dialCode: '+254');
    accNumber.value = PhoneNumber(isoCode: 'KE', dialCode: '+254');
    controller.selectedBank.value = null;
    controller.page.value = 0;
    controller.channelName.value = 'M-PESA';
    controller.channel = 63902;

    // Always dispose controllers
    pageController.dispose();
    // accountNameController.dispose();
    // phoneNumber.dispose();
    // percentageAmountController.dispose();
    // endDateController.dispose();
    // accountNumber.dispose();
    // accountRef.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.edit != null ? "Edit" : "Add"} Beneficiary'),
      ),
      body: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Account Name is required';
                      }
                      return null;
                    },
                    controller: accountNameController,
                    title: 'Account Name',
                  ),
                ),
                SizedBox(height: 8.h),
                splitConfig.value == "Percentage"
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          children: [
                            Text(
                              'Split config:',
                              style: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Obx(
                                () => DropdownButton<String>(
                                  value: splitConfig.value,
                                  hint: const Text('Select Option'),
                                  items: <String>['Percentage', 'Amount']
                                      .map<DropdownMenuItem<String>>(
                                          (String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    if (newValue != null) {
                                      splitConfig.value = newValue;

                                      percentageAmountController.clear();
                                    }
                                  },
                                ),
                              ),
                            ),
                            const Tooltip(
                              triggerMode: TooltipTriggerMode.tap,
                              message:
                                  'Split config determines how the funds will be divided. You can choose to split by Percentage or by a fixed Amount.',
                              child: Icon(Icons.info_outline, size: 18),
                            ),
                          ],
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(children: [
                          Text(
                            'Amount',
                            style: TextStyle(
                                fontSize: 14.spMin,
                                // color: isLight.value ?  Colors.black : Colors.white,
                                fontWeight: FontWeight.w600),
                          ),
                        ])),
                Obx(
                  () => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: splitConfig.value == "Percentage"
                        ? Obx(() {
                            print('maxPercentage : $maxPercentage');

                            return Slider(
                              value: percentage.value != null
                                  ? percentage.value!
                                  : controller.remainingPercentage.value,
                              min: 0,
                              max: maxPercentage.value,
                              divisions: 100,
                              label:
                                  '${(percentage.value != null ? percentage.value! : 0).round()}%',
                              onChanged: (double newValue) {
                                percentage.value = newValue;
                                amount = null;
                                percentageAmountController.text =
                                    newValue.toStringAsFixed(0);
                              },
                            );
                          })
                        : MyTextFieldwValidator(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Amount is required';
                              }
                              return null;
                            },
                            maxLength:
                                splitConfig.value == 'Percentage' ? 3 : 7,
                            controller: percentageAmountController,
                            onChanged: (val) {
                              if (splitConfig.value == "Percentage") {
                                percentage.value =
                                    (double.tryParse(val) ?? 0) / 100;
                                amount = null;
                              } else {
                                percentage(null);
                                amount = double.tryParse(val) ?? 0;
                              }
                            },
                            hint: splitConfig.value,
                            keyboardType: TextInputType.number,
                          ),
                  ),
                ),
                Obx(
                  () => Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 6, horizontal: 8),
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: primaryColor.withOpacity(0.15),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                clearText(),
                                transferMode = 'WALLET',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 0,
                                pageController.jumpToPage(0)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 0
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Mobile Money',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 0
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                clearText(),
                                transferMode = 'PAYBILL',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 1,
                                pageController.jumpToPage(1)
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 1
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Paybill',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 1
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                clearText(),
                                transferMode = 'TILL',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 2,
                                pageController.jumpToPage(2)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 2
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Till Number',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 2
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                clearText(),
                                transferMode = 'BANK',
                                controller.channelName.value = 'BANK',
                                controller.page.value = 3,
                                pageController.jumpToPage(3)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 3
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Bank',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 3
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                        ],
                      )),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: EdgeInsets.all(8.0.spMin),
                  child: Obx(
                    () => SizedBox(
                      height: controller.page.value == 1 ||
                              controller.page.value == 3
                          ? 195.h
                          : controller.page.value == 0
                              ? 100.h
                              : 90.h,
                      width: 520,
                      child: Obx(() {
                        return PageView(
                          controller: pageController,
                          onPageChanged: (page) => {
                            controller.page(page),
                            if (firstTime) {clearText()},
                            firstTime = true
                          },
                          children: [
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        controller.setChannel('M-PESA', 63902);
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/mpesa.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'M-PESA',
                                            groupValue:
                                                controller.channelName.value,
                                            onChanged: (value) {
                                              controller.setChannel(
                                                  value.toString(), 63902);
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        controller.setChannel('SasaPay', 0);
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/sasapay.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'SasaPay',
                                            groupValue:
                                                controller.channelName.value,
                                            onChanged: (value) {
                                              controller.setChannel(
                                                  value.toString(), 0);
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        controller.setChannel(
                                            'AirtelMoney', 63903);
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/airtelmoney.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'AirtelMoney',
                                            groupValue:
                                                controller.channelName.value,
                                            onChanged: (value) {
                                              controller.setChannel(
                                                  value.toString(), 63903);
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                MyTextFieldwValidator(
                                  keyboardType: TextInputType.text,
                                  controller: accountNumber,
                                  title: 'Mpesa Paybill',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Paybill is required';
                                    }
                                    // Allow alphanumeric characters for paybill
                                    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
                                      return 'Paybill can only contain letters and numbers';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                MyTextFieldwValidator(
                                  keyboardType: TextInputType.text,
                                  controller: accountRef,
                                  title: 'Account Number',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Account Number is required';
                                    }
                                    // Allow alphanumeric characters for account numbers
                                    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
                                      return 'Account Number can only contain letters and numbers';
                                    }
                                    return null;
                                  },
                                )
                              ],
                            ),
                            Column(
                              children: [
                                MyTextFieldwValidator(
                                  onChanged: (_) {
                                    accountRef.text = "";
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Till Number is required';
                                    }
                                    return null;
                                  },
                                  controller: accountNumber,
                                  keyboardType: TextInputType.number,
                                  title: 'Mpesa Till number',
                                ),
                              ],
                            ),
                            Obx(
                              () => Column(
                                children: [
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      'Select Bank',
                                      style: TextStyle(
                                          fontSize: 14.spMin,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                  GestureDetector(
                                      onTap: () {
                                        showModalBottomSheet(
                                            context: context,
                                            isScrollControlled: true,
                                            builder: (_) {
                                              return DraggableScrollableSheet(
                                                  maxChildSize: 0.97,
                                                  initialChildSize: 0.7,
                                                  expand: false,
                                                  builder: (context,
                                                      scrollController) {
                                                    final GlobalControllers
                                                        globalController =
                                                        Get.find<
                                                            GlobalControllers>();
                                                    final RxString searchText =
                                                        ''.obs;

                                                    final banksList =
                                                        globalController
                                                            .paymentChannels
                                                            .where((e) =>
                                                                e.category ==
                                                                pm.Category
                                                                    .BANK)
                                                            .toList();

                                                    return Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              12.0),
                                                      child: Column(
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(8.0),
                                                            child:
                                                                CupertinoSearchTextField(
                                                              onChanged:
                                                                  (value) {
                                                                searchText
                                                                        .value =
                                                                    value;
                                                              },
                                                            ),
                                                          ),
                                                          Expanded(
                                                            child: Obx(() {
                                                              final filteredBanks =
                                                                  banksList.where(
                                                                      (bank) {
                                                                return bank.name
                                                                    .toLowerCase()
                                                                    .contains(searchText
                                                                        .value
                                                                        .toLowerCase());
                                                              }).toList();

                                                              return ListView
                                                                  .builder(
                                                                      itemCount:
                                                                          filteredBanks
                                                                              .length,
                                                                      itemBuilder:
                                                                          (context,
                                                                              index) {
                                                                        return GestureDetector(
                                                                            onTap:
                                                                                () {
                                                                              controller.selectedBank.value = filteredBanks[index];
                                                                              accountRef.text = "";
                                                                              controller.channelName.value = 'BANK';
                                                                              controller.channel = filteredBanks[index].channelCode;
                                                                              Navigator.of(context).pop();
                                                                            },
                                                                            child:
                                                                                ListTile(
                                                                              leading: ShowCachedNetworkImage(
                                                                                imageurl: filteredBanks[index].imageUrl,
                                                                                height: 30,
                                                                                width: 30,
                                                                                errorWidget: const Icon(Icons.account_balance),
                                                                              ),
                                                                              title: Text(filteredBanks[index].name),
                                                                            ));
                                                                      });
                                                            }),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  });
                                            });
                                      },
                                      child: Container(
                                          height: 60.h,
                                          padding: const EdgeInsets.all(8),
                                          alignment: Alignment.center,
                                          width: 390.w,
                                          decoration: BoxDecoration(
                                            border:
                                                Border.all(color: Colors.grey),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Obx(
                                            () => ListTile(
                                              leading: ShowCachedNetworkImage(
                                                imageurl: controller
                                                        .selectedBank
                                                        .value
                                                        ?.imageUrl ??
                                                    '',
                                                height: 30,
                                                width: 30,
                                                errorWidget: const Icon(
                                                    Icons.account_balance),
                                              ),
                                              title: Text(controller
                                                      .selectedBank
                                                      .value
                                                      ?.name ??
                                                  ''),
                                            ),
                                          ))),
                                  SizedBox(height: 12.h),
                                  if (controller.selectedBank.value != null)
                                    MyTextFieldwValidator(
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter your bank account number';
                                        }
                                        return null;
                                      },
                                      controller: accountNumber,
                                      title: 'Bank Account',
                                      onChanged: (val) {
                                        accountRef.text = "";
                                      },
                                    )
                                ],
                              ),
                            ),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
                SizedBox(height: 8.h),
                Obx(
                  () => controller.page.value == 0
                      ? Column(
                          children: [
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                'Enter phone number',
                                style: TextStyle(
                                    fontSize: 14.spMin,
                                    color: isLight.value
                                        ? Colors.black
                                        : Colors.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            InternationalPhoneNumberInput(
                              onInputChanged: (PhoneNumber no) {
                                phoneNumber.text = no.phoneNumber
                                    .toString()
                                    .replaceAll("+", '');
                              },
                              onInputValidated: (bool value) {},
                              selectorConfig: const SelectorConfig(
                                selectorType:
                                    PhoneInputSelectorType.BOTTOM_SHEET,
                                useBottomSheetSafeArea: true,
                              ),
                              ignoreBlank: false,
                              autoValidateMode: AutovalidateMode.disabled,
                              initialValue: PhoneNumber(
                                  phoneNumber: number.phoneNumber ?? '',
                                  isoCode: number.isoCode ?? 'KE',
                                  dialCode: number.dialCode ?? "+254"),
                              validator: (value) {
                                if (value == null ||
                                    value.isEmpty ||
                                    value.length < 9) {
                                  return 'Phone Number is required';
                                }
                                return null;
                              },

                              // textFieldController: phoneNumber,
                              formatInput: true,
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                              inputBorder: const OutlineInputBorder(),
                              inputDecoration: InputDecoration(
                                hintText: "0712345678",
                                filled: true,
                                fillColor: isLight.value
                                    ? Colors.white70
                                    : Colors.transparent,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                    width: 0.5,
                                    color: Colors.grey,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                    width: 0.5,
                                    color: Colors.grey,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox(),
                ),
                Obx(
                  () => CheckboxListTile(
                    contentPadding: const EdgeInsets.all(2),
                    value: includeEndDate.value,
                    onChanged: (val) {
                      includeEndDate.value = val ?? false;
                    },
                    title: const Text('Include End Date'),
                  ),
                ),
                Obx(() => includeEndDate.value
                    ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: MyTextFieldwValidator(
                          title: 'End Date',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'End Date is required';
                            }
                            return null;
                          },
                          controller: endDateController,
                          hint: 'click to select end Date',
                          readOnly: true,
                          onTap: () async {
                            DateTime? pickedDateTime = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now().toLocal(),
                              firstDate: DateTime.now().toLocal(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );

                            if (pickedDateTime != null) {
                              TimeOfDay? pickedTime = await showTimePicker(
                                context: context,
                                initialTime: TimeOfDay.now(),
                              );

                              if (pickedTime != null) {
                                DateTime finalDateTime = DateTime(
                                  pickedDateTime.year,
                                  pickedDateTime.month,
                                  pickedDateTime.day,
                                  pickedTime.hour,
                                  pickedTime.minute,
                                );

                                String formattedDateTime =
                                    DateFormat('d MMM yyyy HH : mm a')
                                        .format(finalDateTime);
                                endDateController.text = formattedDateTime;
                              }
                            }
                          },
                        ),
                      )
                    : const SizedBox()),
                const SizedBox(height: 200)
              ],
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Obx(
        () => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4),
          child: MyButton(
            showLoading: controller.isAddingBeneficiaries.value,
            onClick: () async {
              if (formKey.currentState!.validate()) {
                if (transferMode != "PAYBILL") {
                  accountRef.clear();
                }
                if (widget.edit == null) {
                  print('endDate : ${includeEndDate.value}');
                  if (await controller.addBeneficiaries({
                    "account_name": accountNameController.text,
                    "percentage": percentage.value == null
                        ? null
                        : (percentage.value! / 100),
                    "user_id": null,
                    "kitty_id": widget.kittyId,
                    "transfer_mode": transferMode,
                    "amount": amount,
                    "channel_name": controller.channelName.value,
                    "channel": controller.channel,
                    "status": "ACTIVE",
                    "account_number": transferMode == "WALLET"
                        ? phoneNumber.text
                        : accountNumber.text,
                    "account_number_ref": accountRef.text,
                    "split_config": splitConfig.value.toUpperCase(),
                    "phone_number": phoneNumber.text,
                    "role": "SECONDARY",
                    "end_date": includeEndDate.value
                        ? DateFormat('d MMM yyyy HH : mm a')
                            .parse(endDateController.text)
                            .toUtc()
                            .toIso8601String()
                        : null
                  }, widget.kittyId)) {
                    Navigator.pop(context);
                  }
                } else {
                  print('endDate : ${includeEndDate.value}');
                  if (await controller.updateBeneficiary({
                    "ID": widget.edit!.id,
                    // "CreatedAt": widget.edit!.createdAt.toUtc().toIso8601String(),
                    // "UpdatedAt": DateTime.now().toUtc().toIso8601String(),
                    "DeletedAt": null,
                    "account_name": accountNameController.text,
                    "percentage": percentage.value == null
                        ? null
                        : (percentage.value! / 100),
                    "user_id": null,
                    "kitty_id": widget.kittyId,
                    "transfer_mode": transferMode,
                    "amount": amount,
                    "channel_name": controller.channelName.value,
                    "channel": controller.channel,
                    "status": "ACTIVE",
                    "account_number": transferMode == "WALLET"
                        ? phoneNumber.text
                        : accountNumber.text,
                    "account_number_ref": accountRef.text,
                    "split_config": splitConfig.value.toUpperCase(),
                    "phone_number": phoneNumber.text,
                    "role": widget.edit!.role,
                    "end_date": includeEndDate.value
                        ? DateFormat('d MMM yyyy HH : mm a')
                            .parse(endDateController.text)
                            .toUtc()
                            .toIso8601String()
                        : null
                  }, widget.kittyId)) {
                    Navigator.pop(context);
                  }
                }
              }
            },
            label: "${widget.edit != null ? "Edit" : "Add"} Beneficiary",
          ),
        ),
      ),
    );
  }
}
