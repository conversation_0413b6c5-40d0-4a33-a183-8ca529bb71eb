// To parse this JSON data, do
//
//     final enumFreqModel = enumFreqModelFromJson(jsonString);

import 'dart:convert';

EnumFreqModel enumFreqModelFromJson(String str) =>
    EnumFreqModel.fromJson(json.decode(str));

String enumFreqModelToJson(EnumFreqModel data) => json.encode(data.toJson());

class EnumFreqModel {
  bool status;
  String message;
  Data data;

  EnumFreqModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory EnumFreqModel.fromJson(Map<String, dynamic> json) => EnumFreqModel(
        status: json["status"],
        message: json["message"],
        data: Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data.toJson(),
      };
}

class Data {
  List<ChamaRole>? chamaRoles;
  List<Frequency>? frequencies;

  Data({
    this.chamaRoles,
    this.frequencies,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        chamaRoles: List<ChamaRole>.from(
            json["chama_roles"].map((x) => ChamaRole.fromJson(x))),
        frequencies: List<Frequency>.from(
            json["frequencies"].map((x) => Frequency.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "chama_roles": List<dynamic>.from(chamaRoles!.map((x) => x.toJson())),
        "frequencies": List<dynamic>.from(frequencies!.map((x) => x.toJson())),
      };
}

class ChamaRole {
  int id;
  DateTime createdAt;
  DateTime updatedAt;
  dynamic deletedAt;
  String role;
  String description;
  int sortOrder;
  String permissions;

  ChamaRole({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.role,
    required this.description,
    required this.sortOrder,
    required this.permissions,
  });

  factory ChamaRole.fromJson(Map<String, dynamic> json) => ChamaRole(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        role: json["role"],
        description: json["description"],
        sortOrder: json["sort_order"],
        permissions: json["permissions"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt.toIso8601String(),
        "UpdatedAt": updatedAt.toIso8601String(),
        "DeletedAt": deletedAt,
        "role": role,
        "description": description,
        "sort_order": sortOrder,
        "permissions": permissions,
      };
}

class Frequency {
  String frequency;
  String description;

  Frequency({
    required this.frequency,
    required this.description,
  });

  factory Frequency.fromJson(Map<String, dynamic> json) => Frequency(
        frequency: json["frequency"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "description": description,
      };
}
