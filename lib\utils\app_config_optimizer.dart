import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

/// A utility class for optimizing app configuration settings
class AppConfigOptimizer {
  /// Configure the app with optimized settings based on build mode
  static void configureApp() {
    if (kReleaseMode) {
      // Disable debug prints in release mode
      debugPrint = (String? message, {int? wrapWidth}) {};
            
      // Disable unnecessary animations or features in release mode
      // that might impact performance
    }

    // Configure error handling
    ErrorWidget.builder = (FlutterErrorDetails details) {
      // In debug mode, show the standard error widget
      if (kDebugMode) {
        return ErrorWidget(details.exception);
      }
      
      // In release mode, log to Crashlytics
      if (!kIsWeb) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(details);
      }
      
      // Show a more user-friendly error widget
      return Container(
        padding: const EdgeInsets.all(16.0),
        alignment: Alignment.center,
        child: const Text(
          'Something went wrong.',
          style: TextStyle(color: Colors.red),
        ),
      );
    };
  }
  
  /// Optimize Flutter rendering settings
  static void optimizeRendering() {
    // Set image cache size to reasonable values
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB
    
    // Only print in debug mode
    if (kDebugMode) {
      print('Rendering optimization applied');
    }
  }
} 