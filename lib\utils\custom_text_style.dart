import 'package:flutter/material.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:onekitty/main.dart' show isLight;

/// A collection of pre-defined text styles for customizing text appearance,
/// categorized by different font families and weights.
/// Additionally, this class includes extensions on [TextStyle] to easily apply specific font families to text.

class CustomTextStyles {
  // Body text style
  static get bodyLargeIndigo50 => theme.textTheme.bodyLarge!.copyWith(
        color: isLight.value ? appTheme.indigo50 : appTheme.whiteA700,
      );

  static get bodyMediumff545963 => theme.textTheme.bodyMedium!.copyWith(
        color:
            isLight.value ? const Color(0XFF545963) : appTheme.whiteA700,
      );
  static get bodySmallBluegray700 => theme.textTheme.bodySmall!.copyWith(
        color: isLight.value ? appTheme.blueGray700 : appTheme.whiteA700,
      );
  static get bodySmallOnPrimary => theme.textTheme.bodySmall!.copyWith(
        color: theme.colorScheme.onPrimary,
      );
  static get bodyLargeSenGray600 => theme.textTheme.bodyLarge!.Sen.copyWith(
        color: isLight.value ? appTheme.gray600 : appTheme.whiteA700,
      );
  static get titleMediumBlack900 => theme.textTheme.titleMedium!.copyWith(
        color: isLight.value ? appTheme.black900 : appTheme.whiteA700,
      );
  static get titleMediumff545963 => theme.textTheme.titleMedium!.copyWith(
        color:
            isLight.value ? const Color(0XFF545963) : appTheme.whiteA700,
      );
  static get titleSmallyellow => theme.textTheme.titleSmall!.copyWith(
        color: const Color.fromARGB(255, 212, 189, 43),
      );
  static get bodyLargeInterBluegray200 =>
      theme.textTheme.bodyLarge!.inter.copyWith(
        color: isLight.value ? appTheme.blueGray200 : appTheme.whiteA700,
      );
  static get bodyLargeOnPrimaryContainer => theme.textTheme.bodyLarge!.copyWith(
        color: theme.colorScheme.onPrimaryContainer,
      );
  static get bodyLargeSecondaryContainer => theme.textTheme.bodyLarge!.copyWith(
        color: theme.colorScheme.secondaryContainer,
      );
  static get titleSmallDeeppurple500 => theme.textTheme.titleSmall!.copyWith(
        color: appTheme.deepPurple500,
      );

  static get bodySmallff545963 => theme.textTheme.bodySmall!.copyWith(
        color:
            isLight.value ? const Color(0XFF545963) : appTheme.whiteA700,
      );
  static get bodyLargeSenWhiteA700 => theme.textTheme.bodyLarge!.sen.copyWith(
        color: appTheme.whiteA700,
      );
  static get bodyLargeff3d4eb0 => theme.textTheme.bodyLarge!.copyWith(
        color: const Color(0XFF3D4EB0),
      );
  static get bodyLargeff4355b7 => theme.textTheme.bodyLarge!.copyWith(
        color: const Color(0XFF4355B7),
      );
  static get bodyLargeff6e6f79 => theme.textTheme.bodyLarge!.copyWith(
        color:
            isLight.value ? const Color(0XFF6E6F79) : appTheme.whiteA700,
      );
  static get bodyMediumBluegray400 => theme.textTheme.bodyMedium!.copyWith(
        color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
      );
  static get bodyMediumGray600 => theme.textTheme.bodyMedium!.copyWith(
        color: isLight.value ? appTheme.gray600 : appTheme.whiteA700,
      );
  static get bodyMediumBluegray700 => theme.textTheme.bodyMedium!.copyWith(
        color: isLight.value ? appTheme.blueGray400 : appTheme.whiteA700,
      );
  // Title text style
  static get titleLargeGray900 => theme.textTheme.titleLarge!.copyWith(
        color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
      );
  static get titleSmallIndigo500 => theme.textTheme.titleSmall!.copyWith(
        color: appTheme.indigo500,
      );

  static get titleLargeBlack900 => theme.textTheme.titleLarge!.copyWith(
        color: isLight.value ? appTheme.black900 : appTheme.whiteA700,
      );
  static get titleSmallGray900 => theme.textTheme.titleSmall!.copyWith(
        color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
      );
  static get titleSmallIndigo => theme.textTheme.titleSmall!.copyWith(
        color: appTheme.indigo500,
      );

  static get titleSmallPrimary => theme.textTheme.titleSmall!.copyWith(
        color: theme.colorScheme.primary,
      );
  static get titleSmallWhiteA700 => theme.textTheme.titleSmall!.copyWith(
        color: appTheme.whiteA700,
      );
  static get bodyLargeBluegray700 => theme.textTheme.bodyLarge!.copyWith(
        color: isLight.value ? appTheme.blueGray700 : appTheme.whiteA700,
      );
  static get bodySmallGray900 => theme.textTheme.bodySmall!.copyWith(
        color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
      );
  static get bodySmallGray600 => theme.textTheme.bodySmall!.copyWith(
        color: isLight.value ? appTheme.gray600 : appTheme.whiteA700,
      );
  static get bodySmallGray900_1 => theme.textTheme.bodySmall!.copyWith(
        color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
      );

  static get titleSmallRobotoIndigo500 =>
      theme.textTheme.titleSmall!.roboto.copyWith(
        color: appTheme.indigo500,
        fontWeight: FontWeight.w500,
      );

  static get bodySmallIndigo => theme.textTheme.bodySmall!.copyWith(
        color: const Color(0XFF3D4EB0),
        fontSize: 10,
      );
  static get bodySmallgreen => theme.textTheme.bodySmall!.copyWith(
        color: const Color.fromARGB(255, 37, 204, 25),
      );

  // Label text style
  static get labelLargeff545963 => theme.textTheme.labelLarge!.copyWith(
        color:
            isLight.value ? const Color(0XFF545963) : appTheme.whiteA700,
        fontWeight: FontWeight.w600,
      );

  static get labelMediumff545963 => theme.textTheme.labelLarge!.copyWith(
      color: isLight.value
          ? const Color.fromARGB(255, 27, 28, 29)
          : appTheme.whiteA700,
      fontWeight: FontWeight.bold,
      fontSize: 15);

  static get titleMediumSemiBold => theme.textTheme.titleMedium!.copyWith(
        fontWeight: FontWeight.w600,
        color: isLight.value
            ? const Color.fromARGB(255, 27, 28, 29)
            : appTheme.whiteA700,
      );

  // Title text style
  static get titleSmallGray90001 => theme.textTheme.titleSmall!.copyWith(
        color: isLight.value ? appTheme.gray90001 : appTheme.whiteA700,
      );
  static get titleSmallGreen800 => theme.textTheme.titleSmall!.copyWith(
        color: appTheme.green800,
      );
  static get titleSmallOnPrimary => theme.textTheme.titleSmall!.copyWith(
        color: theme.colorScheme.onPrimary,
      );
  static get titleSmallMedium => theme.textTheme.titleSmall!.copyWith(
        fontWeight: FontWeight.w500,
      );

  // Hint text style - faint grey color for helper/hint text
  static TextStyle get hintTextStyle => theme.textTheme.bodyMedium!.copyWith(
        color: isLight.value ? Colors.grey.shade400 : Colors.grey.shade600,
        fontWeight: FontWeight.w400,
      );

  // Helper text style - even fainter grey for helper text
  static TextStyle get helperTextStyle => theme.textTheme.bodySmall!.copyWith(
        color: isLight.value ? Colors.grey.shade300 : Colors.grey.shade700,
        fontWeight: FontWeight.w300,
        fontSize: 12,
      );

  // Label text style with faint grey
  static TextStyle get labelTextStyle => theme.textTheme.bodyMedium!.copyWith(
        color: isLight.value ? Colors.grey.shade500 : Colors.grey.shade500,
        fontWeight: FontWeight.w500,
      );
}

extension on TextStyle {
  TextStyle get inter {
    return copyWith(
      fontFamily: 'Inter',
    );
  }

  TextStyle get Sen {
    return copyWith(
      fontFamily: 'Sen',
    );
  }

  TextStyle get sen {
    return copyWith(
      fontFamily: 'Sen',
    );
  }

  TextStyle get roboto {
    return copyWith(
      fontFamily: 'Roboto',
    );
  }
}
