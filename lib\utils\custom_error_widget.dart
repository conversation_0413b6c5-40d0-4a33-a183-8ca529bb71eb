import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A custom widget to display when errors occur in the app
/// This prevents the app from showing the default Flutter "red screen of death"
class CustomErrorWidget extends StatelessWidget {
  final String errorMessage;
  final dynamic error;
  final dynamic stackTrace;
  final VoidCallback? onRetry;

  const CustomErrorWidget({
    super.key,
    required this.errorMessage,
    this.error,
    this.stackTrace,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    // Log the error
    _logError();

    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Theme.of(context).colorScheme.error,
              size: 48.h,
            ),
            SizedBox(height: 16.h),
            Text(
              'Something went wrong',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              errorMessage,
              style: TextStyle(
                fontSize: 14.sp,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _logError() {
    try {
      print('CustomErrorWidget: $errorMessage');
      if (error != null) {
        print('Error: $error');
      }
      if (stackTrace != null) {
        print('Stack trace: $stackTrace');
      }
      
      // Here you would send the error to your error reporting service
      // like Firebase Crashlytics
      // FirebaseCrashlytics.instance.recordError(error, stackTrace);
    } catch (e) {
      print('Error logging error: $e');
    }
  }
}

/// A helper class to override Flutter's default error widget
class CustomErrorWidgetBuilder {
  /// Initialize the custom error widget as the global error widget
  static void init(Function method) {
    ErrorWidget.builder = (FlutterErrorDetails details) {
      return CustomErrorWidget(
        errorMessage: 'An error occurred in the UI rendering',
        error: details.exception,
        stackTrace: details.stack,
        onRetry: method()
      );
    };
  }
} 

