plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

// Firebase configurations
configurations {
    // No exclusions needed as we require firebase-iid
}

android {
    namespace "ke.co.onekitty"
    compileSdkVersion 35
    ndkVersion "28.0.13004108"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // Note: targetSdkVersion is defined twice in your original file
        // I've removed the duplicate and kept only the relevant one
        applicationId "ke.co.onekitty"
        multiDexEnabled true
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        if (keystorePropertiesFile.exists()) {
            release {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        release {
            if (keystorePropertiesFile.exists() && keystoreProperties['storeFile'] && file(keystoreProperties['storeFile']).exists()) {
                signingConfig signingConfigs.release
            } else {
                // Use debug signing when release signing is not available
                signingConfig signingConfigs.debug
            }
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            aaptOptions {
                ignoreAssetsPattern "mlkit_pose"
            }
        }
        debug {
            signingConfig signingConfigs.debug
            shrinkResources false
            minifyEnabled false
        }
    }

//     packagingOptions {
//     // Keep only face/text recognition dependencies; remove others
//     exclude 'lib/**/libtranslate_jni.so' // Translation (unused)
//     exclude 'lib/**/libdigitalink.so'    // Digital ink (unused)
//     exclude 'lib/**/libxeno_native.so'   // Likely unrelated to face/text
//     exclude 'lib/**/libbarhopper_v2.so'  // Barcode scanning (unused)
//     exclude 'lib/**/libclassifier_jni.so'// Image classification (unused)
//     exclude 'lib/**/liblanguage_id_jni.so'// Language ID (unused)
//     // Retain these if required by face/text recognition:
//     // exclude 'lib/**/libmlkitcommonpipeline.so' // Common pipeline (likely needed)
//     // exclude 'lib/**/libtensorflowlite_jni.so'  // TensorFlow Lite (likely needed)
// }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.window:window:1.2.0'
    implementation 'androidx.window:window-java:1.2.0'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "androidx.multidex:multidex:2.0.1"
    
    // // ML Kit Text Recognition dependencies
    // implementation 'com.google.mlkit:text-recognition:16.0.0'
    // implementation 'com.google.mlkit:text-recognition-chinese:16.0.0'
    // implementation 'com.google.mlkit:text-recognition-devanagari:16.0.0'
    // implementation 'com.google.mlkit:text-recognition-japanese:16.0.0'
    // implementation 'com.google.mlkit:text-recognition-korean:16.0.0'
    
    // Firebase Instance ID
    implementation 'com.google.firebase:firebase-iid:21.1.0'
}

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.12.0'
        force 'androidx.appcompat:appcompat:1.6.1'
    }
}
