import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/kitty/beneficiary_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/beneficiaries_page.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/timeSince.dart';

import 'view_single_beneficiary.dart';

class BeneficiaryCard extends StatelessWidget {
  const BeneficiaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isLight = theme.brightness == Brightness.light;
    final benController = Get.find<BeneficiaryController>();

    return Column(
      children: [
        const Divider(),
        _buildSectionHeader(context, benController),
        SizedBox(height: 8.h),
        _buildBeneficiaryContent(context, isLight, benController),
        const Divider(),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, BeneficiaryController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Beneficiar${controller.beneficiaries.length == 1 ? 'y' : 'ies'}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          TextButton(
            onPressed: () => Get.to(() => const BeneficiariesPage()),
            child: Text(
              'View All',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: 14.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBeneficiaryContent(BuildContext context, bool isLight, BeneficiaryController controller) {
    return Obx(() {
      if (controller.isFetchingBeneficiaries.value) {
        return SizedBox(
          height: 180.h,
          child: Center(
            child: SpinKitDualRing(
              lineWidth: 3,
              color: Theme.of(context).colorScheme.primary,
              // size: 40.0.sp,
            ),
          ),
        );
      }

      return controller.beneficiaries.length == 1
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: _BeneficiaryItem(beneficiary: controller.beneficiaries.first),
            )
          : carousel.CarouselSlider(
              items: controller.beneficiaries
                  .map((beneficiary) => _BeneficiaryItem(beneficiary: beneficiary))
                  .toList(),
              options: carousel.CarouselOptions(
                height: 180.h,
                viewportFraction: 0.85,
                enableInfiniteScroll: false,
                enlargeCenterPage: true,
                padEnds: false,
              ),
            );
    });
  }
}

class _BeneficiaryItem extends StatelessWidget {
  final BeneficiaryModel  beneficiary;

  const _BeneficiaryItem({required this.beneficiary});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isLight = theme.brightness == Brightness.light;

    return OpenContainer(
      closedElevation: 0,
      closedColor: Colors.transparent,
      middleColor: Colors.transparent,
      openBuilder: (context, action) => ViewSingleBeneficiary(beneficiary: beneficiary),
      closedBuilder: (context, action) => Container(
        margin: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: isLight ? Colors.white : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.only(top: 8.w, left: 8.w, right: 8.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              SizedBox(height: 8.h),
              _buildAccountInfo(context),
              SizedBox(height: 12.h),
              _buildAmountSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        _buildAvatar(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    beneficiary.accountName ?? 'Unnamed Account',
                    style: Theme.of(context).textTheme.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (beneficiary.role == 'PRIMARY') 
                    _buildPrimaryBadge(context),
                ],
              ),
              SizedBox(height: 4.h),
              Text(
                'Acct: ${beneficiary.accountNumber}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.2),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          _getInitials(beneficiary.accountName),
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildPrimaryBadge(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      margin: EdgeInsets.only(left: 8.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.verified, size: 14.sp, color: AppColors.primary),
          SizedBox(width: 4.w),
          Text(
            'Primary',
            style: TextStyle(
              fontSize: 10.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfo(BuildContext context) {
    return beneficiary.endDate != null
        ? RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 12.sp,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
              children: [
                TextSpan(
                  text: DateFormat('d MMM, HH:mm').format(beneficiary.endDate!),
                ),
                TextSpan(
                  text: ' • ${highPrecisiontimeSince(beneficiary.endDate!.toLocal())}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildAmountSection(BuildContext context) {
    final dataController = Get.find<DataController>();
    final balance = double.tryParse("${dataController.kitty.value.kitty?.balance ?? 0.0}") ?? 0.0;

    return Row(
      children: [
        if (beneficiary.splitConfig == "PERCENTAGE") _buildPercentageIndicator(),
        SizedBox(width: 12.w),
        Text(
          beneficiary.splitConfig == "AMOUNT"
              ? FormattedCurrency.getFormattedCurrency(beneficiary.amount ?? 0)
              : FormattedCurrency.getFormattedCurrency(balance * (beneficiary.percentage ?? 0)),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                letterSpacing: 1.1,
              ),
        ),
        // const Spacer(),
        // OutlinedButton(
        //   onPressed: () {},
        //   style: OutlinedButton.styleFrom(
        //     shape: RoundedRectangleBorder(
        //       borderRadius: BorderRadius.circular(20),
        //     ),
        //     side: BorderSide(color: Theme.of(context).colorScheme.primary),
        //   ),
        //   child: Text('View', style: TextStyle(fontSize: 12.sp))),
      ],
    );
  }

  Widget _buildPercentageIndicator() {
    return SizedBox(
      width: 40.w,
      height: 40.w,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            value: beneficiary.percentage?.toDouble(),
            strokeWidth: 4,
            backgroundColor: Colors.grey.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          Text(
            '${((beneficiary.percentage ?? 0) * 100).toStringAsFixed(0)}%',
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  String _getInitials(String? name) {
    if (name == null || name.isEmpty) return '';
    final parts = name.split(' ')..removeWhere((s) => s.isEmpty);
    if (parts.isEmpty) return '';
    if (parts.length == 1) return parts.first[0].toUpperCase();
    return '${parts.first[0]}${parts.last[0]}'.toUpperCase();
  }
}