import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import '../views/capture_back.dart';
import '../widgets/clay_progress_bar.dart';
import '../controllers/kyc_controller.dart';

class CaptureFrontIDPage extends StatefulWidget {
  const CaptureFrontIDPage({super.key});

  @override
  State<CaptureFrontIDPage> createState() => _CaptureFrontIDPageState();
}

class _CaptureFrontIDPageState extends State<CaptureFrontIDPage> {
  final KYCController controller = Get.find<KYCController>();
  CameraController? cameraController;
  bool isCameraInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      final status = await Permission.camera.status;

      if (status.isDenied) {
        final result = await Permission.camera.request();
        if (result.isDenied) {
          _showPermissionDeniedDialog();
          return;
        }
      }

      if (status.isPermanentlyDenied) {
        _showPermanentlyDeniedDialog();
        return;
      }

      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        _showNoCameraDialog();
        return;
      }

      cameraController = CameraController(
        cameras[0],
        ResolutionPreset.high,
        enableAudio: false,
      );

      await cameraController!.initialize();
      if (mounted) {
        setState(() {
          isCameraInitialized = true;
        });
      }
    } catch (e) {
      _showCameraInitError(e.toString());
    }
  }

  void _showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Camera Permission Required'),
        content: const Text(
          'Camera access is needed to capture your ID. You can also select from gallery instead.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _useGalleryFallback();
            },
            child: const Text('Use Gallery'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _initializeCamera(); // Retry
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  void _showPermanentlyDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Camera Permission Denied'),
        content: const Text(
          'Camera permission is permanently denied. Please enable it in settings or use gallery instead.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _useGalleryFallback();
            },
            child: const Text('Use Gallery'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  void _showNoCameraDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('No Camera Found'),
        content: const Text('No cameras are available on this device. Please select from gallery.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _useGalleryFallback();
            },
            child: const Text('Use Gallery'),
          ),
        ],
      ),
    );
  }

  void _showCameraInitError(String error) {
    Get.dialog(
      AlertDialog(
        title: const Text('Camera Error'),
        content: Text('Failed to initialize camera: $error\n\nWould you like to use gallery instead?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _useGalleryFallback();
            },
            child: const Text('Use Gallery'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _initializeCamera(); // Retry
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Future<void> _useGalleryFallback() async {
    final file = await controller.captureImage('front');
    if (file != null) {
      Get.snackbar(
        'Success',
        'Front ID selected successfully!',
        backgroundColor: Colors.green[200],
      );
      // Navigate to next step
      Get.back(); // or navigate to next screen
    }
  }

  Future<void> _captureImage() async {
    if (!isCameraInitialized || cameraController == null) {
      Get.snackbar(
        'Error',
        'Camera not initialized. Please restart the camera.',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red[200],
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      final XFile image = await cameraController!.takePicture();
      final File imageFile = File(image.path);

      // Validate the captured image
      if (!await imageFile.exists()) {
        throw Exception('Failed to save captured image');
      }

      final fileSizeInBytes = await imageFile.length();
      if (fileSizeInBytes == 0) {
        throw Exception('Captured image is empty');
      }

      controller.frontID.value = imageFile;
      controller.isFrontValid.value = true;

      Get.snackbar(
        'Success',
        'Front ID captured successfully!',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.green[200],
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to capture image: ${e.toString()}',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red[200],
        duration: const Duration(seconds: 3),
      );
    }
  }
/*
  Future<void> _validateDocument(File image) async {
    try {
      final textDetector = GoogleMlKit.vision.textRecognizer();
      final inputImage = InputImage.fromFile(image);
      final recognisedText = await textDetector.processImage(inputImage);
      await textDetector.close();

      if (recognisedText.text.isEmpty) {
        Get.snackbar(
          'Invalid Document',
          'No text detected in the image',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red[200],
        );
        controller.frontID.value = null;
      }
    } catch (e) {
      Get.snackbar(
        'Validation Error',
        'Failed to process document: ',
        snackPosition: SnackPosition.bottom,
      );
      controller.frontID.value = null;
    }
  }*/

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isLight = theme.brightness == Brightness.light;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Capture Front ID'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                const ClayProgress(
                  currentStep: 2,
                  totalSteps: 5,
                ),
                const SizedBox(height: 30),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: isLight ? Colors.white : Colors.black12,
                          offset: const Offset(-6, -6),
                          blurRadius: 12,
                        ),
                        BoxShadow(
                          color:
                              isLight ? Colors.grey.shade400 : Colors.black26,
                          offset: const Offset(6, 6),
                          blurRadius: 12,
                        ),
                      ],
                    ),
                    child: Center(
                      child: controller.frontID.value == null
                          ? _buildCameraPreview()
                          : _buildImagePreview(theme),
                    ),
                  ),
                ),
              ],
            ),
          )),
      floatingActionButton:
          controller.frontID.value == null ? _buildCaptureButton(theme) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildCameraPreview() {
    if (!isCameraInitialized) {
      return const Center(child: CircularProgressIndicator());
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: CameraPreview(cameraController!),
    );
  }

  Widget _buildImagePreview(ThemeData theme) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Image.file(controller.frontID.value!),
        ),
        Positioned(
          bottom: 20,
          right: 20,
          child: FloatingActionButton(
            backgroundColor: theme.colorScheme.surface,
            onPressed: () => Get.to(() => const CaptureBackIDPage()),
            child: Icon(Icons.check, color: theme.colorScheme.primary),
          ),
        ),
      ],
    );
  }

  Widget _buildCaptureButton(ThemeData theme) {
    return Container(
      height: 80,
      width: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.white
                : Colors.black12,
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.grey.shade400
                : Colors.black26,
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40),
          onTap: _captureImage,
          child: Icon(
            Icons.camera_alt,
            size: 32,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }
}
