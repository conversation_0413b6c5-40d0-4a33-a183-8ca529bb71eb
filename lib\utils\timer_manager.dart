import 'dart:async';
import 'package:flutter/foundation.dart';

/// A utility class for managing timers to prevent memory leaks
class TimerManager {
  static final Map<String, Timer> _timers = {};
  
  /// Start a new timer with a given key
  /// If a timer with the same key already exists, it will be canceled first
  ///
  /// [key] - Unique identifier for the timer
  /// [duration] - Duration after which the callback will be triggered
  /// [callback] - Function to execute when the timer finishes
  static void startTimer(String key, Duration duration, Function() callback) {
    stopTimer(key);
    _timers[key] = Timer(duration, () {
      callback();
      // Remove the timer from the map after it completes
      _timers.remove(key);
    });
    
    if (kDebugMode) {
      print('TimerManager: Started timer for key $key');
    }
  }
  
  /// Start a periodic timer with a given key
  /// If a timer with the same key already exists, it will be canceled first
  ///
  /// [key] - Unique identifier for the timer
  /// [duration] - Duration between each callback execution
  /// [callback] - Function to execute periodically
  static void startPeriodicTimer(String key, Duration duration, Function(Timer) callback) {
    stopTimer(key);
    _timers[key] = Timer.periodic(duration, (timer) {
      callback(timer);
    });
    
    if (kDebugMode) {
      print('TimerManager: Started periodic timer for key $key');
    }
  }
  
  /// Stop a timer with the given key
  ///
  /// [key] - The key of the timer to stop
  static void stopTimer(String key) {
    if (_timers.containsKey(key)) {
      _timers[key]?.cancel();
      _timers.remove(key);
      
      if (kDebugMode) {
        print('TimerManager: Stopped timer for key $key');
      }
    }
  }
  
  /// Check if a timer with the given key is active
  ///
  /// [key] - The key of the timer to check
  /// Returns true if an active timer exists with the given key
  static bool isTimerActive(String key) {
    return _timers.containsKey(key) && _timers[key]!.isActive;
  }
  
  /// Stop all active timers
  static void stopAllTimers() {
    _timers.forEach((key, timer) {
      timer.cancel();
    });
    _timers.clear();
    
    if (kDebugMode) {
      print('TimerManager: Stopped all timers');
    }
  }
  
  /// Get the number of active timers
  static int get activeTimerCount => _timers.length;
} 