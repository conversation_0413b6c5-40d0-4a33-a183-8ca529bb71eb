import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/my_text_field.dart';

class Socials extends StatelessWidget {
  final TextEditingController website, facebook, xlink, instagram, tiktok;

  const Socials({
    super.key,
    required this.website,
    required this.facebook,
    required this.xlink,
    required this.instagram,
    required this.tiktok,
  });

  /// Validates URL format
  String? _validateUrl(String? value, String platform) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }
    
    final url = value.trim();
  
    
    // Platform-specific validation
    final lowerUrl = url.toLowerCase();
    switch (platform.toLowerCase()) {
      case 'facebook':
        if (!lowerUrl.contains('facebook.com') && !lowerUrl.contains('fb.com')) {
          return 'Please enter a valid Facebook URL';
        }
        break;
      case 'x':
        if (!lowerUrl.contains('twitter.com') && !lowerUrl.contains('x.com')) {
          return 'Please enter a valid X (Twitter) URL';
        }
        break;
      case 'instagram':
        if (!lowerUrl.contains('instagram.com')) {
          return 'Please enter a valid Instagram URL';
        }
        break;
      case 'tiktok':
        if (!lowerUrl.contains('tiktok.com')) {
          return 'Please enter a valid TikTok URL';
        }
        break;
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        MyTextFieldwValidator(
            controller: website,
            validator: (value) => _validateUrl(value, 'website'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Website (optional)',
            hint: 'https://yourwebsite.com',
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: facebook,
            validator: (value) => _validateUrl(value, 'facebook'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Facebook link (optional)',
            hint: 'https://facebook.com/yourpage',
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: xlink,
            validator: (value) => _validateUrl(value, 'x'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'X link (optional)',
            hint: 'https://x.com/youraccount',
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: instagram,
            validator: (value) => _validateUrl(value, 'instagram'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Instagram link (optional)',
            hint: 'https://instagram.com/youraccount',
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: tiktok,
            validator: (value) => _validateUrl(value, 'tiktok'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'TikTok link (optional)',
            hint: 'https://tiktok.com/@youraccount',
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
      ]),
    );
  }
}
