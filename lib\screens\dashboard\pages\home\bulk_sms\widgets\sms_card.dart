import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/models/bulkSms/msg_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class SmsCardWidget extends StatelessWidget {
  final msgItem item;
  final BulkSMSController smsController = Get.put(BulkSMSController());

  SmsCardWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        var messageStored = Get.put(MessageController());
        messageStored.reSmessage.value = item.description ?? "";
        messageStored.msgId.value = item.id.toString();
        Logger().d(item.toJson());
        Get.toNamed(NavRoutes.singlebulksms);
      },
      child: Align(
        alignment: Alignment.centerLeft,
        child: Container(
          padding: EdgeInsets.only(top: 1.h),
          decoration: AppDecoration.outlineBluegray100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ReadMoreText(
                item.description ?? "",
                trimMode: TrimMode.Line,
                trimLines: 2,
                colorClickableText: ColorUtil.blueColor,
                trimCollapsedText: 'show more',
                trimExpandedText: 'show less',
                moreStyle: CustomTextStyles.bodySmallBluegray700,
              ),
              SizedBox(height: 11.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomImageView(
                    imagePath: AssetUrl.imgLockBlueGray700,
                    height: 18.h,
                    width: 18.w,
                    color: Colors.black,
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 6.w),
                    child: Text(
                      "${item.recipients ?? ""} Recipients",
                      style: theme.textTheme.bodySmall,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    DateFormat.jm().format(item.createdAt!.toLocal()),
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
