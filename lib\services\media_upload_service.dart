import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/helpers/show_toast.dart';

/// Service for handling media uploads with validation and progress tracking
class MediaUploadService extends GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  // Upload states
  final RxList<MediaUploadItem> uploadQueue = <MediaUploadItem>[].obs;
  final RxBool isUploading = false.obs;
  final RxInt completedUploads = 0.obs;
  final RxInt failedUploads = 0.obs;
  final RxDouble overallProgress = 0.0.obs;

  /// Add media item to upload queue
  void addToUploadQueue(MediaUploadItem item) {
    uploadQueue.add(item);
  }

  /// Remove media item from upload queue
  void removeFromUploadQueue(String itemId) {
    uploadQueue.removeWhere((item) => item.id == itemId);
  }

  /// Clear upload queue
  void clearUploadQueue() {
    uploadQueue.clear();
    completedUploads.value = 0;
    failedUploads.value = 0;
    overallProgress.value = 0.0;
  }

  /// Upload all items in queue
  Future<List<Map<String, dynamic>>> uploadAllMedia({
    required BuildContext context,
    Function(double)? onProgressUpdate,
    Function(MediaUploadItem, String)? onItemComplete,
    Function(MediaUploadItem, String)? onItemError,
  }) async {
    if (uploadQueue.isEmpty) return [];

    isUploading(true);
    completedUploads.value = 0;
    failedUploads.value = 0;
    overallProgress.value = 0.0;

    final List<Map<String, dynamic>> uploadedMedia = [];
    final totalItems = uploadQueue.length;

    try {
      for (int i = 0; i < uploadQueue.length; i++) {
        final item = uploadQueue[i];
        
        try {
          // Update item status
          item.status.value = MediaUploadStatus.uploading;
          
          // Upload the media
          final result = await _uploadSingleMedia(item);
          
          if (result != null) {
            // Success
            item.status.value = MediaUploadStatus.completed;
            item.uploadedUrl.value = result['url'] ?? '';
            uploadedMedia.add(result);
            completedUploads.value++;
            
            onItemComplete?.call(item, result['url'] ?? '');
          } else {
            // Failed
            item.status.value = MediaUploadStatus.failed;
            item.errorMessage.value = 'Upload failed';
            failedUploads.value++;
            
            onItemError?.call(item, 'Upload failed');
          }
        } catch (e) {
          // Error
          item.status.value = MediaUploadStatus.failed;
          item.errorMessage.value = e.toString();
          failedUploads.value++;
          
          onItemError?.call(item, e.toString());
        }

        // Update overall progress
        final progress = (i + 1) / totalItems;
        overallProgress.value = progress;
        onProgressUpdate?.call(progress);
      }

      // Show completion message
      if (completedUploads.value > 0) {
        ToastUtils.showSuccessToast(
          context,
          '${completedUploads.value} media files uploaded successfully',
          'Upload Complete',
        );
      }

      if (failedUploads.value > 0) {
        ToastUtils.showErrorToast(
          context,
          '${failedUploads.value} media files failed to upload',
          'Upload Errors',
        );
      }

      return uploadedMedia;
    } finally {
      isUploading(false);
    }
  }

  /// Upload single media item
  Future<Map<String, dynamic>?> _uploadSingleMedia(MediaUploadItem item) async {
    try {
      if (item.file != null) {
        // File upload
        return await _uploadFile(item.file!, item.mediaType);
      } else if (item.url != null && item.url!.isNotEmpty) {
        // URL validation and processing
        if (await _validateMediaUrl(item.url!)) {
          return {
            'url': item.url!,
            'type': item.mediaType.name,
            'source': 'external',
          };
        } else {
          throw Exception('Invalid media URL');
        }
      } else {
        throw Exception('No file or URL provided');
      }
    } catch (e) {
      logger.e('Error uploading media: $e');
      return null;
    }
  }

  /// Upload file to server
  Future<Map<String, dynamic>?> _uploadFile(File file, MediaType mediaType) async {
    try {
      // For now, simulate file upload - in real implementation,
      // you would use dio FormData or similar multipart upload
      final response = await apiProvider.request(
        url: ApiUrls.UPLOADFILE,
        method: Method.POST,
        params: {
          'media_type': mediaType.name,
          'file_path': file.path, // Placeholder - real implementation would handle file upload
        },
      );

      if (response.data['status'] ?? false) {
        return {
          'url': response.data['data']['url'],
          'type': mediaType.name,
          'source': 'uploaded',
          'file_id': response.data['data']['id'],
        };
      } else {
        throw Exception(response.data['message'] ?? 'Upload failed');
      }
    } catch (e) {
      logger.e('File upload error: $e');
      return null;
    }
  }

  /// Validate media URL
  Future<bool> _validateMediaUrl(String url) async {
    try {
      // Basic URL validation
      final uri = Uri.tryParse(url);
      if (uri == null || !uri.hasScheme || !uri.hasAuthority) {
        return false;
      }

      // Check if URL is accessible (optional - can be expensive)
      // You might want to make this configurable
      return true;
    } catch (e) {
      logger.e('URL validation error: $e');
      return false;
    }
  }

  /// Check if all uploads are complete
  bool get allUploadsComplete {
    return uploadQueue.every((item) => 
      item.status.value == MediaUploadStatus.completed ||
      item.status.value == MediaUploadStatus.failed
    );
  }

  /// Check if any uploads are in progress
  bool get hasUploadsInProgress {
    return uploadQueue.any((item) => 
      item.status.value == MediaUploadStatus.uploading ||
      item.status.value == MediaUploadStatus.pending
    );
  }

  /// Get upload summary
  Map<String, int> get uploadSummary {
    final summary = <String, int>{
      'total': uploadQueue.length,
      'completed': 0,
      'failed': 0,
      'pending': 0,
      'uploading': 0,
    };

    for (final item in uploadQueue) {
      switch (item.status.value) {
        case MediaUploadStatus.completed:
          summary['completed'] = summary['completed']! + 1;
          break;
        case MediaUploadStatus.failed:
          summary['failed'] = summary['failed']! + 1;
          break;
        case MediaUploadStatus.uploading:
          summary['uploading'] = summary['uploading']! + 1;
          break;
        case MediaUploadStatus.pending:
          summary['pending'] = summary['pending']! + 1;
          break;
      }
    }

    return summary;
  }
}

/// Media upload item model
class MediaUploadItem {
  final String id;
  final MediaType mediaType;
  final File? file;
  final String? url;
  final Rx<MediaUploadStatus> status;
  final RxString uploadedUrl;
  final RxString errorMessage;
  final RxDouble progress;

  MediaUploadItem({
    required this.id,
    required this.mediaType,
    this.file,
    this.url,
    MediaUploadStatus initialStatus = MediaUploadStatus.pending,
  }) : status = initialStatus.obs,
       uploadedUrl = ''.obs,
       errorMessage = ''.obs,
       progress = 0.0.obs;

  /// Create from file
  factory MediaUploadItem.fromFile(File file, MediaType mediaType) {
    return MediaUploadItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      mediaType: mediaType,
      file: file,
    );
  }

  /// Create from URL
  factory MediaUploadItem.fromUrl(String url, MediaType mediaType) {
    return MediaUploadItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      mediaType: mediaType,
      url: url,
    );
  }

  /// Get display name
  String get displayName {
    if (file != null) {
      return file!.path.split('/').last;
    } else if (url != null) {
      return Uri.parse(url!).pathSegments.last;
    }
    return 'Unknown';
  }

  /// Check if upload is complete
  bool get isComplete => status.value == MediaUploadStatus.completed;

  /// Check if upload failed
  bool get hasFailed => status.value == MediaUploadStatus.failed;

  /// Check if upload is in progress
  bool get isUploading => status.value == MediaUploadStatus.uploading;
}

/// Media upload status enum
enum MediaUploadStatus {
  pending,
  uploading,
  completed,
  failed,
}

/// Media type enum
enum MediaType {
  image,
  video,
  document,
}

/// Extension for MediaUploadStatus
extension MediaUploadStatusExtension on MediaUploadStatus {
  String get displayName {
    switch (this) {
      case MediaUploadStatus.pending:
        return 'Pending';
      case MediaUploadStatus.uploading:
        return 'Uploading';
      case MediaUploadStatus.completed:
        return 'Completed';
      case MediaUploadStatus.failed:
        return 'Failed';
    }
  }

  Color get color {
    switch (this) {
      case MediaUploadStatus.pending:
        return Colors.grey;
      case MediaUploadStatus.uploading:
        return Colors.blue;
      case MediaUploadStatus.completed:
        return Colors.green;
      case MediaUploadStatus.failed:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case MediaUploadStatus.pending:
        return Icons.schedule;
      case MediaUploadStatus.uploading:
        return Icons.cloud_upload;
      case MediaUploadStatus.completed:
        return Icons.check_circle;
      case MediaUploadStatus.failed:
        return Icons.error;
    }
  }
}
