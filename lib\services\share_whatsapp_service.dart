import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ShareWhatsapp {
  static Future<void> share(String message, {String subject = ''}) async {
    
  // Define WhatsApp URIs
  final whatsappUri = Uri.parse(
      "whatsapp://send?text=${Uri.encodeComponent(message)}");
  final businessUri = Uri.parse(
      "whatsapp-business://send?text=${Uri.encodeComponent(message)}");

  // Check availability of both apps
  final whatsappAvailable = await canLaunchUrl(whatsappUri);
  final businessAvailable = await canLaunchUrl(businessUri);

  List<String> options = [];
  if (whatsappAvailable) options.add("WhatsApp");
  if (businessAvailable) options.add("WhatsApp Business");

  if (options.isEmpty) {
    // Fallback if neither app is installed
    await Share.share(message, subject: subject);
    return;
  }

  

  try {
     await launchUrl(whatsappUri);
    
  } catch (e) {
    // Fallback if launching fails
    await Share.share(message, subject: 'Transaction details');
  }    
  }
}