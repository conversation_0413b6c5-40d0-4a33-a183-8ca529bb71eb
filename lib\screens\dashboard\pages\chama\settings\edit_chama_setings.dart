import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chama_settings.dart';
import 'package:onekitty/models/chama/edit_chama_settings_request.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/utils_exports.dart';

class EditChamaSettings extends StatefulWidget {
  final ChamaSetting chamaSetting;
  const EditChamaSettings({super.key, required this.chamaSetting});

  @override
  State<EditChamaSettings> createState() => _EditChamaSettingsState();
}

class _EditChamaSettingsState extends State<EditChamaSettings> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  TextEditingController benefPerCycleCtr = TextEditingController();
  TextEditingController sigThreshCtr = TextEditingController();
  final formKey = GlobalKey<FormState>();
  double value = 0.0;

  @override
  void initState() {
    super.initState();
    benefPerCycleCtr.text = chamaController.benefPerCycle.value.toString();
    sigThreshCtr.text = chamaController.signatureThreshold.value.toString();
    value = chamaController.benefPercentage.value * 100;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 40),
          child: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const RowAppBar(),
                    Text("Edit Chama Settings",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold, fontSize: 22)),
                    SizedBox(
                      height: 5.h,
                    ),
                    const Text(
                      "Chama settings are set upon creating a chama but you can edit for smooth running of your chama",
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    SingleLineRow(
                      text: "Beneficiaries per cycle",
                      popup: KtStrings.benefPerCycle,
                    ),
                    CustomTextField(
                      controller: benefPerCycleCtr,
                      labelText: "Enter number of beneficiaries per cycle",
                      showNoKeyboard: true,
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "This field can't be empty";
                        }
                        return null;
                      },
                    ),
                    SingleLineRow(
                      text: "Beneficiaries percentage ($value%)",
                      popup: KtStrings.benefPercentage,
                    ),
                    Text(
                      "$value% of the Chama Balance will be sent to the Beneficiary(ies)",
                      style: context.dividerTextSmall?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: AppColors.greyTextColor,
                      ),
                    ),
                    Slider(
                        value: value,
                        min: 0,
                        max: 100,
                        divisions: 10,
                        label: value.round().toString(),
                        onChanged: (value) {
                          setState(() {
                            this.value = value;
                          });
                        }),
                    SingleLineRow(
                      text: "Signature threshhold",
                      popup: KtStrings.sigThreshold,
                    ),
                    CustomTextField(
                      controller: sigThreshCtr,
                      labelText: "Enter number of sign signatories",
                      showNoKeyboard: true,
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "This field can't be empty";
                        }
                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Obx(() => CustomKtButton(
                        isLoading: chamaController.isEditChamaSettings.isTrue,
                        onPress: () async {
                          if (formKey.currentState!.validate()) {
                            EditChamaSettingsRequest edit =
                                EditChamaSettingsRequest(
                              id: chamaController.settingId.value,
                              chamaId:
                                  chamaDataController.chama.value.chama?.id,
                              beneficiariesPerCycle:
                                  int.tryParse(benefPerCycleCtr.text.trim()),
                              beneficiaryPercentage: double.parse(
                                  (value / 100).toStringAsFixed(1)),
                              signatureThreshold:
                                  int.parse(sigThreshCtr.text.trim()),
                            );
                            bool res = await chamaController.editChamaSettings(
                                request: edit);
                            if (res) {
                              if (!mounted) return;
                              Snack.show(
                                  res, chamaController.apiMessage.string);
                              Get.offAndToNamed(NavRoutes.chamaSettings);
                            } else {
                              if (!mounted) return;
                              Snack.show(
                                  res, chamaController.apiMessage.string);
                            }
                          }
                        },
                        btnText: "Edit"))
                  ],
                ),
              ))),
    );
  }
}
