import 'package:connectivity_checker/connectivity_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class ConnectivityCheck extends StatelessWidget {
  ConnectivityCheck({super.key, required this.child});
  Widget child;
  @override
  Widget build(BuildContext context) {
    return ConnectivityScreenWrapper(
      decoration: BoxDecoration(
        color: const Color.fromARGB(142, 196, 1, 60),
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(10.sp),
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(255, 56, 51, 51).withOpacity(0.2),
            spreadRadius: 3,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      positionOnScreen: PositionOnScreen.TOP,
      height: 62.sp,
      textAlign: TextAlign.center,
      disableInteraction: false,
      child: child,
    );
  }
}


