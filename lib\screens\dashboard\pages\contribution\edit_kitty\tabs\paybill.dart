import 'package:flutter/material.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';

class PayBill2 extends StatelessWidget {
  final TextEditingController paybillController;
  final TextEditingController accountController;
  const PayBill2(
      {super.key,
      required this.paybillController,
      required this.accountController,});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Mpesa PayBill",
              style: context.titleText,
            ),
          ),
          CustomTextField(
            labelText: "PayBill number",
            controller: paybillController,
            hintText: "paybill",
            showNoKeyboard: true,
            allowAlphanumeric: true,
            isRequired: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "Enter Paybill Number";
              } else if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
                return "Paybill number can only contain letters and numbers";
              } else {
                return null;
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Account number",
              style: context.titleText,
            ),
          ),
          CustomTextField(
            labelText: "Account number",
            controller: accountController,
            isRequired: true,
            hintText: "account number",
            allowAlphanumeric: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "Enter Account Number";
              } else {
                return null;
              }
            },
          ),
        ],
      ),
    );
  }
}