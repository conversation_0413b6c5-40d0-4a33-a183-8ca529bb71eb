// To parse this JSON data, do
//
//     final messages = messagesFrom<PERSON><PERSON>(jsonString);

import 'dart:convert';

Messages messagesFromJson(String str) => Messages.fromJson(json.decode(str));

String messagesToJson(Messages data) => json.encode(data.toJson());

class Messages {
  bool? status;
  String? message;
  msgData? data;

  Messages({
    this.status,
    this.message,
    this.data,
  });

  factory Messages.fromJson(Map<String, dynamic> json) => Messages(
        status: json["status"],
        message: json["message"],
        data: msgData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class msgData {
  List<msgItem>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  msgData({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory msgData.fromJson(Map<String, dynamic> json) => msgData(
        items:
            List<msgItem>.from(json["items"].map((x) => msgItem.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class msgItem {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? description;
  String? product;
  int? subscriptionId;
  double? amount;
  int? quantity;
  double? pricePerUnit;
  String? status;
  String? responseDesc;
  String? reference;
  int? recipients;
  int? userId;
  String? messageId;
  int? metadataId;

  msgItem({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.description,
    this.product,
    this.subscriptionId,
    this.amount,
    this.quantity,
    this.pricePerUnit,
    this.status,
    this.responseDesc,
    this.reference,
    this.userId,
    this.metadataId,
    this.messageId,
    this.recipients
  });

  factory msgItem.fromJson(Map<String, dynamic> json) => msgItem(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        description: json["description"],
        product: json["product"],
        subscriptionId: json["subscription_id"],
        amount: json["amount"]?.toDouble(),
        quantity: json["quantity"],
        pricePerUnit: json["price_per_unit"]?.toDouble(),
        status: json["status"],
        responseDesc: json["response_desc"],
        reference: json["reference"],
        userId: json["user_id"],
        metadataId: json["metadata_id"],
        messageId: json["message_id"],
        recipients: json["total_recipients"]
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "description": description,
        "product": product,
        "subscription_id": subscriptionId,
        "amount": amount,
        "quantity": quantity,
        "price_per_unit": pricePerUnit,
        "status": status,
        "response_desc": responseDesc,
        "reference": reference,
        "user_id": userId,
        "metadata_id": metadataId,
        "message_id":messageId,
        "total_recipients":recipients,
      };
}
