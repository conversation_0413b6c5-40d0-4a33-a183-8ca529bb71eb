import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/custom_text_style.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final double? height;
  final double? width;
  final Function(String val)? onChanged;
  final String labelText;
  final String hintText;
  final IconData? prefixIcon;
  final bool? isRequired;
  final bool? readOnly;
  final int? maxLength;
  final bool obscureText;
  final bool showNoKeyboard;
  final bool allowAlphanumeric;
  final List<TextInputFormatter>? inputFormatters;
  final double paddingHorizontal;
  final Widget? suffixIcon;
  final Widget? prefix;
  // final Widget? suffix;
  final String? Function(String?)? validator;
  final String? initialValue;
  final InputDecoration? decoration;
  final TextStyle? style;
  final bool? suffixIconConstraints;
  final Widget? suffix;
  const CustomTextField({
    super.key,
    required this.controller,
    this.height,
    this.labelText = 'Enter text',
    this.hintText = '',
    this.suffixIcon,
    this.readOnly,
    this.width,
    this.maxLength,
    this.prefixIcon,
    this.inputFormatters,
    this.paddingHorizontal = 0,
    this.obscureText = false,
    this.isRequired = false,
    this.showNoKeyboard = false,
    this.allowAlphanumeric = false,
    this.validator,
    this.initialValue,
    this.suffix,
    this.suffixIconConstraints = false,
    this.prefix,
    this.onChanged, this.decoration, this.style,

  });
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width ?? double.infinity,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: paddingHorizontal, vertical: paddingHorizontal),
        child: SizedBox(
          //height: height ?? 65.h,
          child: TextFormField(
            readOnly: readOnly ?? false,
            initialValue: initialValue,
            maxLength: maxLength,
            controller: controller,
            obscureText: obscureText,
            inputFormatters: showNoKeyboard
                ? [
                    allowAlphanumeric
                        ? FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9]"))
                        : FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                  ]
                : inputFormatters,
            keyboardType: showNoKeyboard
                ? (allowAlphanumeric ? TextInputType.text : TextInputType.number)
                : null,
            validator: validator,
            onChanged: onChanged,
            decoration:  decoration != null  ? decoration!.copyWith( 
              prefix: prefix,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(labelText),
                  Visibility(
                    visible: isRequired ?? false,
                    child: Text(
                      " *",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13.sp,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  )
                ],
              ),
              suffixIcon: suffixIcon,
              suffixIconConstraints: suffixIconConstraints == true 
                ? const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                    maxWidth: 48,
                    maxHeight: 48,
                  ) 
                : null,
              suffix: suffix,
              counter: const SizedBox.shrink(),
              hintText: hintText,
              hintStyle: CustomTextStyles.hintTextStyle,
              prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
              border: const OutlineInputBorder(),
              filled: true,
              fillColor: Colors.blueAccent.withOpacity(0.1),
           
            ) : InputDecoration(
              prefix: prefix,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(labelText),
                  Visibility(
                    visible: isRequired ?? false,
                    child: Text(
                      " *",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13.sp,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  )
                ],
              ),
              suffixIcon: suffixIcon,
              // Removed suffixIconConstraints here as well
              counter: const SizedBox.shrink(),
              hintText: hintText,
              hintStyle: CustomTextStyles.hintTextStyle,
              helperStyle: CustomTextStyles.helperTextStyle,
              prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
              border: const OutlineInputBorder(),
              filled: true,
              fillColor: Colors.blueAccent.withOpacity(0.1),
            ),
          ),
        ),
      ),
    );
  }
}
class CustomTextFieldText extends StatelessWidget {
  final TextEditingController controller;
  final double? height;
  final double? width;
  final Function(String val)? onChanged;
  final String labelText;
  final String hintText;
  final IconData? prefixIcon;
  final bool? isRequired;
  final bool? readOnly;
  final int? maxLength;
  final bool obscureText;
  final bool showNoKeyboard;
  final bool allowAlphanumeric;
  final List<TextInputFormatter>? inputFormatters;
  final double paddingHorizontal;
  final Widget? suffixIcon;
  final Widget? prefix;
  final String? Function(String?)? validator;
  final String? initialValue;
  final TextInputType? keyboardType;
  final Widget? bottomChild;
  final FocusNode? focusNode;
  final int? maxLines;

  const CustomTextFieldText({
    super.key,
    required this.controller,
    this.height,
    this.labelText = 'Enter text',
    this.hintText = '',
    this.suffixIcon,
    this.readOnly,
    this.width,
    this.maxLength,
    this.prefixIcon,
    this.inputFormatters,
    this.paddingHorizontal = 0,
    this.obscureText = false,
    this.isRequired = false,
    this.showNoKeyboard = false,
    this.allowAlphanumeric = false,
    this.validator,
    this.initialValue,
    this.prefix,
    this.onChanged,
    this.keyboardType,
    this.bottomChild,
    this.focusNode,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width ?? double.infinity,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: paddingHorizontal, vertical: paddingHorizontal),
        child: Column(
          children: [
            TextFormField(
              maxLines: maxLines, 
              focusNode: focusNode,
              readOnly: readOnly ?? false,
              initialValue: initialValue,
              maxLength: maxLength,
              controller: controller,
              obscureText: obscureText,
              inputFormatters: showNoKeyboard
                  ? [
                      allowAlphanumeric
                          ? FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9]"))
                          : FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ]
                  : inputFormatters,
              keyboardType: keyboardType ??
                  (showNoKeyboard
                      ? (allowAlphanumeric ? TextInputType.text : TextInputType.number)
                      : null),
              validator: validator,
              onChanged: onChanged,
              
              decoration: InputDecoration(
                prefix: prefix,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(labelText),
                    Visibility(
                      visible: isRequired ?? false,
                      child: Text(
                        " *",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 13.sp,
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    )
                  ],
                ),
                suffix: suffixIcon,
                counter: const SizedBox.shrink(),
                hintText: hintText,
                hintStyle: CustomTextStyles.hintTextStyle,
                prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
                border: const OutlineInputBorder(),
                filled: true,
                fillColor: Colors.blueAccent.withOpacity(0.1),
              ),
            ),
          bottomChild ?? const SizedBox()
          ],
        ),
      ),
    );
  }
}
