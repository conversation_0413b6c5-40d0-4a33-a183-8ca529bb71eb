import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/// A class representing a cached response with expiration
class CachedResponse {
  final dynamic data;
  final DateTime timestamp;
  final Duration expiryDuration;
  
  CachedResponse(this.data, {this.expiryDuration = const Duration(minutes: 15)})
      : timestamp = DateTime.now();
  
  bool get isExpired {
    return DateTime.now().difference(timestamp) > expiryDuration;
  }
  
  /// Convert to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'data': jsonEncode(data),
      'timestamp': timestamp.millisecondsSinceEpoch,
      'expiryDuration': expiryDuration.inMilliseconds,
    };
  }
  
  /// Create from a map from storage
  factory CachedResponse.fromMap(Map<String, dynamic> map) {
    return CachedResponse(
      jsonDecode(map['data']),
      expiryDuration: Duration(milliseconds: map['expiryDuration']),
    );
  }
}

/// A utility class for caching network requests
class RequestCache {
  static final Map<String, CachedResponse> _memoryCache = {};
  static const String _storageCacheKey = 'request_cache';
  static final GetStorage _storage = GetStorage();
  static bool _initialized = false;
  
  /// Initialize the cache from persistent storage
  static Future<void> init() async {
    if (_initialized) return;
    
    await GetStorage.init();
    _loadCacheFromStorage();
    _initialized = true;
    
    if (kDebugMode) {
      print('RequestCache: Initialized with ${_memoryCache.length} cached items');
    }
  }
  
  /// Load cache from persistent storage
  static void _loadCacheFromStorage() {
    try {
      final cachedData = _storage.read<Map<String, dynamic>>(_storageCacheKey);
      if (cachedData != null) {
        cachedData.forEach((key, value) {
          try {
            final cachedResponse = CachedResponse.fromMap(value);
            if (!cachedResponse.isExpired) {
              _memoryCache[key] = cachedResponse;
            }
          } catch (e) {
            if (kDebugMode) {
              print('RequestCache: Error parsing cached item: $e');
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('RequestCache: Error loading cache from storage: $e');
      }
    }
  }
  
  /// Save cache to persistent storage
  static void _saveCacheToStorage() {
    try {
      final Map<String, dynamic> dataToSave = {};
      
      _memoryCache.forEach((key, value) {
        if (!value.isExpired) {
          dataToSave[key] = value.toMap();
        }
      });
      
      _storage.write(_storageCacheKey, dataToSave);
    } catch (e) {
      if (kDebugMode) {
        print('RequestCache: Error saving cache to storage: $e');
      }
    }
  }
  
  /// Get cached response or fetch new data
  /// 
  /// [url] - The key/URL to cache against
  /// [fetcher] - Function that fetches fresh data
  /// [forceFresh] - Whether to force a fresh fetch even if cached data exists
  /// [expiryDuration] - How long the data should remain valid
  static Future<dynamic> getCachedResponse(
    String url,
    Future<dynamic> Function() fetcher, {
    bool forceFresh = false,
    Duration expiryDuration = const Duration(minutes: 15),
  }) async {
    await init();
    
    // Check for cached non-expired data
    if (!forceFresh && _memoryCache.containsKey(url) && !_memoryCache[url]!.isExpired) {
      if (kDebugMode) {
        print('RequestCache: Cache hit for $url');
      }
      return _memoryCache[url]!.data;
    }
    
    // Fetch fresh data
    try {
      if (kDebugMode) {
        print('RequestCache: Cache miss for $url, fetching fresh data');
      }
      
      final response = await fetcher();
      
      // Cache the response
      _memoryCache[url] = CachedResponse(response, expiryDuration: expiryDuration);
      _saveCacheToStorage();
      
      return response;
    } catch (e) {
      // If there's an error but we have expired data, return it as fallback
      if (_memoryCache.containsKey(url)) {
        if (kDebugMode) {
          print('RequestCache: Error fetching fresh data, using expired cache as fallback');
        }
        return _memoryCache[url]!.data;
      }
      
      // Otherwise, rethrow the error
      rethrow;
    }
  }
  
  /// Invalidate a specific cached response
  static void invalidate(String url) {
    _memoryCache.remove(url);
    _saveCacheToStorage();
    
    if (kDebugMode) {
      print('RequestCache: Invalidated cache for $url');
    }
  }
  
  /// Clear all cached responses
  static void clearAll() {
    _memoryCache.clear();
    _storage.remove(_storageCacheKey);
    
    if (kDebugMode) {
      print('RequestCache: Cleared all cached responses');
    }
  }
} 