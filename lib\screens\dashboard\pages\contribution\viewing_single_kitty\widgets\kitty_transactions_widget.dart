// ignore_for_file: must_be_immutable, use_build_context_synchronously, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/transaction_item.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import '../../../../../../utils/utils_exports.dart';
import 'package:onekitty/main.dart' show isLight;

class TransactionWidget extends StatefulWidget {
  const TransactionWidget({super.key});

  @override
  State<TransactionWidget> createState() => _TransactionWidgetState();
}

class _TransactionWidgetState extends State<TransactionWidget> {
  final DataController dataController = Get.find<DataController>();
  final ContributeController contributeController = Get.find();
  final KittyController controller = Get.put(KittyController());
  final dateformat = DateFormat('EE, dd MMMM');
  
  // Pagination variables
  final RxInt _currentPage = 1.obs;
  final int _pageSize = 20;
  final RxBool _isLoadingMore = false.obs;
  final RxBool _hasMoreData = true.obs;
  final RxList<TransactionModel> _paginatedTransactions = <TransactionModel>[].obs;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    
    // Add scroll listener for pagination
    _scrollController.addListener(_scrollListener);
    
    // Initial load
    _loadTransactions();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }
  
  // Scroll listener for pagination
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore.value &&
        _hasMoreData.value) {
      _loadMoreTransactions();
    }
  }
  
  // Load initial transactions
  Future<void> _loadTransactions() async {
    try {
      await controller.getKittyContributions(
        kittyId: dataController.kitty.value.kitty?.iD ?? 0,
      );
      
      // After loading all transactions, initialize the paginated list
      _updatePaginatedList();
    } catch (e) {
      print("Error loading Contributions: $e");
    }
  }
  
  // Load more transactions when scrolling
  void _loadMoreTransactions() {
    if (controller.transactionsKitty.isEmpty || !_hasMoreData.value) {
      return;
    }
    
    _isLoadingMore.value = true;
    _currentPage.value++;
    
    // Update paginated list after a small delay to simulate network request
    Future.delayed(const Duration(milliseconds: 300), () {
      _updatePaginatedList();
      _isLoadingMore.value = false;
    });
  }
  
  // Update the paginated list based on the current page
  void _updatePaginatedList() {
    final int startIndex = 0;
    final int endIndex = _currentPage.value * _pageSize;
    
    if (endIndex >= controller.transactionsKitty.length) {
      _hasMoreData.value = false;
      _paginatedTransactions.assignAll(controller.transactionsKitty);
    } else {
      _paginatedTransactions.assignAll(
        controller.transactionsKitty.sublist(startIndex, endIndex)
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Transactions", style: CustomTextStyles.titleSmallGray900),
                GestureDetector(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (BuildContext context) {
                        return ExportContentWidget2(
                          eventId: null,
                          singleTrans: false,
                        );
                      },
                    );
                  },
                  child: Chip(
                    label: Text(
                      "Export",
                      style: CustomTextStyles.titleSmallIndigo500,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(NavRoutes.seeAlltranscScreen);
                  },
                  child: Chip(
                    label: Text("See all", style: CustomTextStyles.titleSmallIndigo500),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 15.h),
          Obx(() {
            if (controller.loadingTransactions.isTrue && _currentPage.value == 1) {
              return SizedBox(
                height: SizeConfig.screenHeight * .33,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4.sp,
                        size: 40.0.sp,
                      ),
                      const Text(
                        "loading..",
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      )
                    ],
                  ),
                ),
              );
            } else if (controller.transactionsKitty.isEmpty) {
              return Column(
                children: [
                  const Text("You have no transactions yet"),
                  Image.asset(
                    AssetUrl.notFound,
                    height: 90.h,
                  ),
                  Obx(() => CustomKtButton(
                    isLoading: contributeController.isgetkittyloading.isTrue,
                    onPress: () async {
                      final id = dataController.kitty.value.kitty?.iD;
                      final res = await contributeController.getKitty(id: id);
                      if (res) {
                        Get.toNamed(NavRoutes.kittycontributionScreen + id.toString());
                      }
                    },
                    width: 110.w,
                    height: 23.h,
                    btnText: "Contribute",
                  )),
                ],
              );
            } else {
              return Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: GroupedListView<TransactionModel, DateTime>(
                        controller: _scrollController,
                        sort: false,
                        elements: _paginatedTransactions,
                        stickyHeaderBackgroundColor: Colors.transparent,
                        groupBy: (TransactionModel element) {
                          DateTime date = element.createdAt!.toLocal();
                          return DateTime(date.year, date.month, date.day);
                        },
                        groupHeaderBuilder: (value) {
                          final date = dateformat.format(value.createdAt!.toLocal());
                          return Container(
                            decoration: BoxDecoration(
                              color: isLight.value ? appTheme.gray50 : appTheme.gray900,
                              borderRadius: BorderRadius.all(Radius.elliptical(5, 10)),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                date,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15.0,
                                ),
                              ),
                            ),
                          );
                        },
                        itemBuilder: (_, TransactionModel item) {
                          return TransactionItem(item: item);
                        },
                        separator: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 5),
                        ),
                      ),
                    ),
                    // Show loading indicator at the bottom when loading more data
                    if (_isLoadingMore.value)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: ColorUtil.blueColor,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }
          }),
        ],
      ),
    );
  }
}
