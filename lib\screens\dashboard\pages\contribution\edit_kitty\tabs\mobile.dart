import 'package:flutter/material.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/utils/common_strings.dart';

class Mobile2 extends StatefulWidget {
  final PaymentChannelsBuilder2 paymentChannelsBuilder2;
  final CustomInternationalPhoneInput internationalPhoneNumberInput;
  const Mobile2(
      {super.key,
      required this.paymentChannelsBuilder2,
      required this.internationalPhoneNumberInput});

  @override
  State<Mobile2> createState() => _Mobile2State();
}

class _Mobile2State extends State<Mobile2> {
  String? selectedChannel = 'M-Pesa';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          widget.paymentChannelsBuilder2,
          SingleLineRow(
            text: "Enter beneficiary phone number",
            popup: KtStrings.benfAcc,
          ),
          widget.internationalPhoneNumberInput
        ],
      ),
    );
  }
}
