import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
class CustomImageView extends StatelessWidget {
  final String? imagePath;
  final double? height;
  final double? width;
  final Color? color;
  final IconData? icon;
  final BoxFit fit;
  final String placeHolder;
  final AlignmentGeometry alignment;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? radius;
  final BoxBorder? border;

  const CustomImageView({
    super.key,
    this.imagePath,
    this.height,
    this.width,
    this.color,
    this.fit = BoxFit.cover,
    this.alignment = Alignment.center,
    this.onTap,
    this.radius,
    this.margin,
    this.border,
    this.placeHolder = 'assets/images/image_not_found.png', this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        child: ClipRRect(
          borderRadius: radius ?? BorderRadius.zero,
          child: _buildImageView(),
        ),
      ),
    );
  }

  Widget _buildImageView() {
    if(icon != null){
      return Icon(icon,  size: height,
        )
      
      ;
    }
    if (imagePath == null || imagePath!.isEmpty) {
      return Image.asset(
        placeHolder,
        height: height,
        width: width,
        fit: fit,
      );
    }

    switch (imagePath!.imageType) {
      case ImageType.svg:
        return SvgPicture.asset(
          imagePath!,
          height: height,
          width: width,
          fit: fit, 
        );
      case ImageType.file:
        try {
          String cleanPath = imagePath!.replaceFirst('file:///', '');
          if (Platform.isWindows) {
            cleanPath = cleanPath.replaceFirst(RegExp(r'^/+'), '');
          } else {
            cleanPath = '/${cleanPath.replaceFirst(RegExp(r'^/+'), '')}';
          }

          final file = File(cleanPath);
          if (file.existsSync()) {
            return Image.file(
              file,
              height: height,
              width: width,
              fit: fit,
              color: color,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Error loading file image: $error');
                return Image.asset(
                  placeHolder,
                  height: height,
                  width: width,
                  fit: fit,
                );
              },
            );
          }
        } catch (e) {
          debugPrint('Error processing file path: $e');
        }
        return Image.asset(
          placeHolder,
          height: height,
          width: width,
          fit: fit,
        );
      case ImageType.network:
        if (!_isValidUrl(imagePath!)) {
          return Image.asset(
            placeHolder,
            height: height,
            width: width,
            fit: fit,
          );
        }
        return FastCachedImage(
          height: height,
          width: width,
          fit: fit,
          url: imagePath??'',
          color: color,
          loadingBuilder: (context, url) => const SizedBox(
            height: 30,
            width: 30,
            child: CircularProgressIndicator(),
          ),
          errorBuilder: (context, url, error) => Image.asset(
            placeHolder,
            height: height,
            width: width,
            fit: fit,
          ),
        );
      case ImageType.png:
      default:
        return Image.asset(
          imagePath!,
          height: height,
          width: width,
          fit: fit,
          color: color,
        );
    }
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }
}

extension ImageTypeExtension on String {
  ImageType get imageType {
    if (startsWith('http') || startsWith('https')) {
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
      // } else if (startsWith('file://') || startsWith('/')) {
      //   return ImageType.file;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, file, unknown }
