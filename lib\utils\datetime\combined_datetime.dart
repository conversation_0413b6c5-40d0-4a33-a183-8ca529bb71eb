import 'package:intl/intl.dart';

DateTime combineDateTime(String date, String time) {
  // Parse the date string to get the year, month, and day
  if (date.isEmpty || time.isEmpty) {
    return DateTime.now();
  }
  final dateParts = date.split('-');
  final year = int.parse(dateParts[0]);
  final month = int.parse(dateParts[1]);
  final day = int.parse(dateParts[2]);

  // Parse the time string to get the hour and minute
  final timeParts = time.split(':');
  final hour = int.parse(timeParts[0]);
  final minute = int.parse(timeParts[1].split('')[0]);
  final isPM = timeParts[1].contains('PM');
  // Adjust hour if it's PM and not 12 (since 12 PM is represented as 0 in 24-hour format)
  int hour24 = hour;
  if (isPM && hour != 12) {
    hour24 += 12;
  } else if (!isPM && hour == 12) {
    // Adjust hour if it's AM and 12 (midnight)
    hour24 = 0;
  }

  // Combine the date and time components into a single DateTime object
  return DateTime(year, month, day, hour, minute);
}

String formatDateTime(DateTime dateTime) {
  // Format the combined DateTime object according to the backend's expected format
  return DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(dateTime.toUtc());
}
