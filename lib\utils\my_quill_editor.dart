import 'dart:convert' show jsonDecode;
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'iswysiwyg.dart';  
class QuillEditorWidget extends StatelessWidget {
  final String? text;
  final int maxLines;
  final bool readMore;
  final String? tag; // Add tag parameter for unique controller instances

  const QuillEditorWidget({
    super.key,
    this.text,
    this.readMore = true,
    this.maxLines = 3,
    this.tag, // Optional tag parameter
  });

  @override
  Widget build(BuildContext context) {
    // Generate a unique tag if none provided
    final controllerTag = tag ?? '${text.hashCode}_${DateTime.now().microsecondsSinceEpoch}';
    final controller = Get.put(QuillReadMoreController(tag: controllerTag), tag: controllerTag);

    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    try {
      late Document document;

      if (isWysiwygFormat(text!)) {
        final htmlToDelta = HtmlToDelta();
        final delta = htmlToDelta.convert(text!);
        document = Document.fromDelta(delta);
      } else {
        final operations = _parseContent(text!);
        document = Document.fromDelta(Delta.fromJson(operations));
      }

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              color: Colors.transparent,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  if (!readMore) {
                    return _buildQuillEditor(document, controller);
                  }

                  final needsReadMore = _needsReadMore(document);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Obx(() {
                        // Use a large finite value instead of infinity for expanded state
                        final double maxHeightValue = needsReadMore && !controller.isExpanded.value
                            ? 80.h
                            : 10000.0; // Large enough to act as "unlimited" but still finite
                            
                        return AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          constraints: BoxConstraints(
                            maxHeight: maxHeightValue,
                          ),
                          child: _buildQuillEditor(
                                document,
                                controller,
                                maxHeight: controller.isExpanded.value
                                    ? maxHeightValue // Use same value as container constraint
                                    : (needsReadMore ? 80.h : maxHeightValue),
                              ),
                        );
                      }),
                      if (needsReadMore)
                        Obx(
                          () => Container(
                            alignment: Alignment.centerRight, 
                            child: TextButton(
                              onPressed: () {
                                controller.toggleExpanded();
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0,
                                  vertical: 2.0,
                                ),
                              ),
                              child: Text(
                                controller.isExpanded.value
                                    ? 'Read Less'
                                    : 'Read More',
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return _buildFallbackWidget(text ?? '', controller);
    }
  }

  Widget _buildQuillEditor(
    Document document,
    QuillReadMoreController controller, {
    double? maxHeight,
  }) {
    final scrollController = ScrollController();
    final isScrollable = controller.isExpanded.value;
    
    // Always use a finite constraint value
    final double constraintHeight = maxHeight ?? 10000.0;
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: constraintHeight,
      ),
      decoration: const BoxDecoration(),  // Add empty decoration to satisfy the assertion
      clipBehavior: Clip.antiAlias,
      child: QuillEditor(
        config: QuillEditorConfig(
         
          autoFocus: false,
          scrollable: isScrollable,
          showCursor: false,
          padding: EdgeInsets.zero,
          enableInteractiveSelection: false,
          enableSelectionToolbar: false,
          customStyles: DefaultStyles(
            h1: DefaultTextBlockStyle(
  TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  ),
  const HorizontalSpacing( 16.0,  0.0), // Use HorizontalSpacing
  const VerticalSpacing( 0.0,  0.0),   // Use VerticalSpacing
  const VerticalSpacing( 0.0,  0.0),   // Use VerticalSpacing
  null, // BoxDecoration (optional)
),

            sizeSmall: TextStyle(fontSize: 12.sp),
            subscript: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.normal,
            ),
          ),
          embedBuilders: [
            QuillEditorImageEmbedBuilder(),
          ],
        ),
        scrollController: scrollController,
        focusNode: FocusNode(canRequestFocus: false), controller:   QuillController(
            document: document,
            selection: const TextSelection.collapsed(offset: 0),
            config: const QuillControllerConfig (),
          )..readOnly = true,
      ),
    );
  }

  List<dynamic> _parseContent(String inputText) {
    if (inputText.isEmpty) {
      return [{"insert": "\n"}];
    }

    String cleanedText = inputText.trim();
    List<dynamic> operations;

    try {
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        operations = jsonDecode(cleanedText);
      } else {
        dynamic decodedContent = jsonDecode(cleanedText);
        operations = (decodedContent is Map && decodedContent.containsKey('ops'))
            ? decodedContent['ops']
            : decodedContent;
      }
    } catch (_) {
      operations = [
        {"insert": "$cleanedText\n"}
      ];
    }

    if (operations.isEmpty) {
 
      return [{"insert": "\n"}];
    }
 
    final lastOp = operations.last;
    if (lastOp is Map && lastOp.containsKey('insert')) {
      String lastText = lastOp['insert'] as String;
      if (!lastText.endsWith('\n')) {
        lastOp['insert'] = '$lastText\n';
      }
    } else {
      operations.add({"insert": "\n"});
    }

    return operations;
  }

  bool _needsReadMore(Document document) {
    try {
      final plainText = document.toPlainText();
      final lineCount = plainText.split('\n').length;
      final textLength = plainText.characters.length;

      return lineCount > maxLines || textLength > 200;
    } catch (_) {
      return false;
    }
  }

  Widget _buildFallbackWidget(
    String fallbackText,
    QuillReadMoreController controller,
  ) {
    try {
      final delta = Delta.fromJson([
        {"insert": "$fallbackText\n"}
      ]);

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Material(
          color: Colors.transparent,
          child: Obx(
            () => _buildQuillEditor(
              Document.fromDelta(delta),
              controller,
              maxHeight: controller.isExpanded.value ? 10000.0 : 25, // Use finite value instead of null
            ),
          ),
        ),
      );
    } catch (_) {
      return const SizedBox.shrink();
    }
  }
}

class QuillReadMoreController extends GetxController {
  final isExpanded = false.obs;
  final shouldShowReadMore = false.obs;
  final String? tag; // Add tag property
  
  // Use a static map to track controllers by tag for cleanup if needed
  static final Map<String, QuillReadMoreController> _controllers = {};
  
  // Constructor with optional tag parameter
  QuillReadMoreController({this.tag});
  
  @override
  void onInit() {
    super.onInit();
    // Store controller reference by tag
    if (Get.currentRoute.isNotEmpty) {
      _controllers[tag ?? ''] = this;
    }
  }
  
  @override
  void onClose() {
    // Remove controller reference when closed
    _controllers.remove(tag ?? '');
    super.onClose();
  }

  void toggleExpanded() {
    print('Toggle expanded from ${isExpanded.value} to ${!isExpanded.value}');
    isExpanded.value = !isExpanded.value;
    // Force UI update by calling update()
    update();
    print('State after toggle: ${isExpanded.value}');
  }
  void updateShouldShowReadMore(bool value) => shouldShowReadMore.value = value;
}

class QuillEditorImageEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'image';

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    final imageUrl = embedContext.node.value.data as String?;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 300),
        child: imageUrl == null
            ? const Icon(Icons.broken_image)
            : Image.network(
                imageUrl,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) =>
                    const Icon(Icons.error),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
              ),
      ),
    );
  }
}

class QuillEditorShortWidget extends StatelessWidget {
  final String? text;
  final String tag;
  final int maxLines;

  const QuillEditorShortWidget({
    super.key,
    this.text,
    this.maxLines = 3, required this.tag,
  });

  @override
  Widget build(BuildContext context) {
    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    try {
      late Document document;

      if (isWysiwygFormat(text!)) {
        final htmlToDelta = HtmlToDelta();
        final delta = htmlToDelta.convert(text!);
        document = Document.fromDelta(delta);
      } else {
        final operations = _parseContent(text!);
        document = Document.fromDelta(Delta.fromJson(operations));
      }

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Material(
          color: Colors.transparent,
          child: Hero(
            tag: tag,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 80.h,
              ),
              child: QuillEditor(
                config: QuillEditorConfig(
                  autoFocus: false,
                  scrollable: false,
                  showCursor: false,
                  padding: EdgeInsets.zero,
                  enableInteractiveSelection: false,
                  enableSelectionToolbar: false,
                  customStyles: DefaultStyles(
                    h1: DefaultTextBlockStyle(
                      TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      const HorizontalSpacing(16.0, 0.0),
                      const VerticalSpacing(0.0, 0.0),
                      const VerticalSpacing(0.0, 0.0),
                      null,
                    ),
                    sizeSmall: TextStyle(fontSize: 12.sp),
                    subscript: TextStyle(
                      fontSize: 10.sp,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  embedBuilders: [
                    QuillEditorImageEmbedBuilder(),
                  ],
                ),
                scrollController: ScrollController(),
                focusNode: FocusNode(canRequestFocus: false),
                controller: QuillController(
                  document: document,
                  selection: const TextSelection.collapsed(offset: 0),
                  config: const QuillControllerConfig(),
                )..readOnly = true,
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  List<dynamic> _parseContent(String inputText) {
    if (inputText.isEmpty) {
      return [{"insert": "\n"}];
    }

    String cleanedText = inputText.trim();
    List<dynamic> operations;

    try {
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        operations = jsonDecode(cleanedText);
      } else {
        dynamic decodedContent = jsonDecode(cleanedText);
        operations = (decodedContent is Map && decodedContent.containsKey('ops'))
            ? decodedContent['ops']
            : decodedContent;
      }
    } catch (_) {
      operations = [
        {"insert": "$cleanedText\n"}
      ];
    }

    if (operations.isEmpty) {
      return [{"insert": "\n"}];
    }

    final lastOp = operations.last;
    if (lastOp is Map && lastOp.containsKey('insert')) {
      String lastText = lastOp['insert'] as String;
      if (!lastText.endsWith('\n')) {
        lastOp['insert'] = '$lastText\n';
      }
    } else {
      operations.add({"insert": "\n"});
    }

    return operations;
  }
}