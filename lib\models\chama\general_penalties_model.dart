// To parse this JSON data, do
//
//     final generalPenaltiesModel = generalPenaltiesModelFromJson(jsonString);

import 'dart:convert';

GeneralPenaltiesModel generalPenaltiesModelFromJson(String str) => GeneralPenaltiesModel.fromJson(json.decode(str));

String generalPenaltiesModelToJson(GeneralPenaltiesModel data) => json.encode(data.toJson());

class GeneralPenaltiesModel {
    bool? status;
    String? message;
    List<GeneralPenalty>? data;

    GeneralPenaltiesModel({
        this.status,
        this.message,
        this.data,
    });

    factory GeneralPenaltiesModel.fromJson(Map<String, dynamic> json) => GeneralPenaltiesModel(
        status: json["status"],
        message: json["message"],
        data: List<GeneralPenalty>.from(json["data"].map((x) => GeneralPenalty.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
    };
}

class GeneralPenalty {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    String? title;
    String? description;
    int? amount;
    int? severity;
    String? status;

    GeneralPenalty({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.title,
        this.description,
        this.amount,
        this.severity,
        this.status,
    });

    factory GeneralPenalty.fromJson(Map<String, dynamic> json) => GeneralPenalty(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        severity: json["severity"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "amount": amount,
        "severity": severity,
        "status": status,
    };
}
