// To parse this JSON data, do
//
//     final chamaMembers = chamaMembersFromJson(jsonString);

import 'dart:convert';

AddPenaltyRequest chamaMembersFromJson(String str) => AddPenaltyRequest.fromJson(json.decode(str));

String chamaMembersToJson(AddPenaltyRequest data) => json.encode(data.toJson());

class AddPenaltyRequest {
    int chamaId;
    String? title;
    String? description;
    num? amount;
    num? severity;
    String? status;

    AddPenaltyRequest({
        required this.chamaId,
        this.title,
        this.description,
        this.amount,
        this.severity,
        this.status,
    });

    factory AddPenaltyRequest.fromJson(Map<String, dynamic> json) => AddPenaltyRequest(
        chamaId: json["chama_id"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        severity: json["severity"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "title": title,
        "description": description,
        "amount": amount,
        "severity": severity,
        "status": status,
    };
}


UpdatePenaltyRequest updatePenaltyRequestFromJson(String str) => UpdatePenaltyRequest.fromJson(json.decode(str));

String updatePenaltyRequestToJson(UpdatePenaltyRequest data) => json.encode(data.toJson());

class UpdatePenaltyRequest {
    int id;
    int chamaId;
    String? title;
    String? description;
    num? amount;
    num? severity;
    String? status;

    UpdatePenaltyRequest({
        required this.id,
        required this.chamaId,
        this.title,
        this.description,
        this.amount,
        this.severity,
        this.status,
    });

    factory UpdatePenaltyRequest.fromJson(Map<String, dynamic> json) => UpdatePenaltyRequest(
        id: json["ID"],
        chamaId: json["chama_id"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        severity: json["severity"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "chama_id": chamaId,
        "title": title,
        "description": description,
        "amount": amount,
        "severity": severity,
        "status": status,
    };
}
