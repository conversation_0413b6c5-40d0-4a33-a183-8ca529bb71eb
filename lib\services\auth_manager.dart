import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart'; 
import '../utils/cache_keys.dart'; 
import 'init_service.dart';

mixin CacheManager {
  Future<bool> saveToken(String? token) async {
    final box = GetStorage();
    await box.write(CacheKeys.token, token);
    return true;
  }

  String? getToken() {
    final box = GetStorage();
    return box.read(CacheKeys.token);
  }

  Future<void> removeToken() async {
    final box = GetStorage();
    await box.remove(CacheKeys.token);
    await box.remove(CacheKeys.user);
  }

  Future<bool> saveLocation(String lat, String long) async {
    final box= GetStorage();
    await box.write(CacheKeys.lat, lat);
    await box.write(CacheKeys.long, long);
    return true;

  }

   String? getLat() {
    final box = GetStorage();
    return box.read(CacheKeys.lat);
  }
   String? getLong() {
    final box = GetStorage();
    return box.read(CacheKeys.long);
  }
}

class AuthenticationManager with CacheManager {
  final isLogged = false.obs;

  /// deletes the stored token and navigates user to the sign in page.
  final box = Get.put<GetStorage>(GetStorage());
  void logOut() async {
    isLogged.value = false;
    await removeToken();
    box.write(CacheKeys.user, null);    
    await FirebaseAuth.instance.signOut();
    // Get.delete<HttpService>();
    servicesInitialize();
  }

  void login(String? token) async {
    isLogged.value = true;
    //Token is cached
    await saveToken(token);
    // Ensure token is saved to box as well
    box.write(CacheKeys.token, token);
  }

  Future<void> checkLoginStatus() async {
    // Check token directly instead of using an isolate
    final token = getToken();
    isLogged.value = token != null;
  }
  
  void bioPrefs(bool allowsBiometric) {
    GetStorage().write(CacheKeys.allowsBiometric, allowsBiometric);
  }
}
