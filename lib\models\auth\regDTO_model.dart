import 'dart:convert';

NewUser userModelFromJson(String str) => NewUser.fromJson(json.decode(str));

String userModelToJson(NewUser data) => json.encode(data.toJson());

class UserModel {
  bool? status;
  String? message;
  Data? data;

  UserModel({this.status, this.message, this.data});

  UserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? checkoutId;
  NewUser? user;

  Data({this.checkoutId, this.user});

  Data.fromJson(Map<String, dynamic> json) {
    checkoutId = json['checkout_id'];
    user = json['user'] != null ? NewUser.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['checkout_id'] = checkoutId;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}

class NewUser {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? phoneNumber;
  String? firstName;
  String? secondName;

  NewUser(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.phoneNumber,
      this.firstName,
      this.secondName});

  NewUser.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    phoneNumber = json['phone_number'];
    firstName = json['first_name'];
    secondName = json['second_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['phone_number'] = phoneNumber;
    data['first_name'] = firstName;
    data['second_name'] = secondName;
    return data;
  }
}