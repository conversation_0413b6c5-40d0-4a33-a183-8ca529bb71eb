import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/main.dart' show isLight;
import '../utils_exports.dart';

// ignore: must_be_immutable
class CustomBottomBar extends StatefulWidget {
  const CustomBottomBar({super.key, this.onChanged});

  final Function(BottomBarEnum)? onChanged;

  @override
  CustomBottomBarState createState() => CustomBottomBarState();
}

class CustomBottomBarState extends State<CustomBottomBar> {
  int selectedIndex = 0;

  List<BottomMenuModel> bottomMenuList = [
    const BottomMenuModel(
      icon: AssetUrl.imgNavHome,
      activeIcon: AssetUrl.imgNavHome,
      title: "Home",
      type: BottomBarEnum.Home,
    ),
    const BottomMenuModel(
      icon: AssetUrl.imgNavContribution,
      activeIcon: AssetUrl.imgNavContribution,
      title: "Contribution",
      type: BottomBarEnum.Contribution,
    ),
    const BottomMenuModel(
      icon: AssetUrl.imgNavChama,
      activeIcon: AssetUrl.imgNavChama,
      title: "Chama",
      type: BottomBarEnum.Chama,
    ),
    const BottomMenuModel(
      icon: AssetUrl.imgNavEvents,
      activeIcon: AssetUrl.imgNavEvents,
      title: "Events",
      type: BottomBarEnum.Events,
    )
  ];

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      backgroundColor: Colors.transparent,
      showSelectedLabels: false,
      showUnselectedLabels: false,
      selectedFontSize: 0,
      elevation: 0,
      currentIndex: selectedIndex,
      type: BottomNavigationBarType.fixed,
      items: List.generate(bottomMenuList.length, (index) {
        return BottomNavigationBarItem(
          icon: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomImageView(
                imagePath: bottomMenuList[index].icon,
                height: 18.h,
                width: 15.w,
                color: appTheme.blueGray700,
              ),
              Padding(
                padding: EdgeInsets.only(
                  top: 1.h,
                  bottom: 1.h,
                ),
                child: Text(
                  bottomMenuList[index].title ?? "",
                  style: theme.textTheme.bodySmall!.copyWith(
                    color: appTheme.blueGray700,
                  ),
                ),
              ),
            ],
          ),
          activeIcon: Container(
            decoration: isLight.value ? AppDecoration.fillWhiteA : AppDecoration.fillWhiteA.copyWith(color: Colors.transparent),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  decoration: AppDecoration.fillGray100.copyWith(
                    borderRadius: BorderRadiusStyle.circleBorder16,
                  ),
                  child: CustomImageView(
                    imagePath: bottomMenuList[index].activeIcon,
                    height: 18.h,
                    width: 15.w,
                    color: theme.colorScheme.primary,
                    margin: EdgeInsets.symmetric(
                      horizontal: 20.w,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    top: 1.h,
                    bottom: 1.h,
                  ),
                  child: Text(
                    bottomMenuList[index].title ?? "",
                    style: theme.textTheme.labelLarge!.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          label: '',
        );
      }),
      onTap: (index) {
        selectedIndex = index;
        widget.onChanged?.call(bottomMenuList[index].type);
        setState(() {});
      },
    );
  }
}

enum BottomBarEnum {
  Home,
  Contribution,
  Chama,
  Events,
}

class BottomMenuModel {
  const BottomMenuModel({
    required this.icon,
    required this.activeIcon,
    this.title,
    required this.type,
  });

  final String icon;

  final String activeIcon;

  final String? title;

  final BottomBarEnum type;
}

class DefaultWidget extends StatelessWidget {
  const DefaultWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xffffffff),
      padding: const EdgeInsets.all(10),
      child: const Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Widgets to be displayed',
              style: TextStyle(
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
