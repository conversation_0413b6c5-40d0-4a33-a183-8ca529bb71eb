import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/member_penalty_request.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class ChamaMembersPage extends StatefulWidget {
  const ChamaMembersPage({super.key});

  @override
  State<ChamaMembersPage> createState() => _ChamaMembersState();
}

class _ChamaMembersState extends State<ChamaMembersPage> {
  TextEditingController amountController = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  String? pentitle;
  String? pendescription;
  int? penamount;
  int? penaltyId;
  bool value = false;
  bool selectAll = false;

  @override
  void dispose() {
    amountController.dispose();
    reasonController.dispose();
    super.dispose();
  }

  void toggleSelectAll() {
    setState(() {
      selectAll = !selectAll;
      for (int i = 0; i < chamaController.chamaMembers.length; i++) {
        chamaController.checkboxStates[i] = selectAll;
      }
    });
  }

  void updateSelectAllState() {
    bool allSelected =
        chamaController.checkboxStates.every((isSelected) => isSelected);
    if (selectAll != allSelected) {
      setState(() {
        selectAll = allSelected;
      });
    }
  }

  //selectedMembers.map((m) => m.firstName).join(", ")}")
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
      //appBar: buildAppBar(context),
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          children: [
            const RowAppBar(),
            Text("Penalise multiple members",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            SizedBox(
              height: 5.h,
            ),
            const Text(
              "Penalise multiple members by just one click",
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Checkbox(
                    value: selectAll,
                    onChanged: (bool? value) {
                      toggleSelectAll();
                    }),
                // Radio<bool>(
                //   value: selectAll,
                //   groupValue: value,
                //   onChanged: (bool? value) {
                //     toggleSelectAll();
                //     // if (value != null) {
                //     //   toggleSelectAll(value);
                //     // }
                //   },
                // ),
                const Text("Select All Members"),
              ],
            ),
            SizedBox(
              height: 15.h,
            ),
            Expanded(
              child: GetX(
                  init: ChamaController(),
                  initState: (state) {
                    Future.delayed(Duration.zero, () async {
                      try {
                        await state.controller?.getChamaMembers(
                            chamaId:
                                chamaDataController.chama.value.chama?.id ?? 0,
                            size: chamaController.OData.value.total);
                        chamaController.reset();
                      } catch (e) {
                        throw e;
                      }
                    });
                  },
                  builder: (ChamaController chamaController) {
                    if (chamaController.isloadingChama.isTrue) {
                      return SizedBox(
                        height: SizeConfig.screenHeight * .33,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SpinKitDualRing(
                                color: ColorUtil.blueColor,
                                lineWidth: 4.sp,
                                size: 40.0.sp,
                              ),
                              const Text(
                                "loading..",
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    } else if (chamaController.chamaMembers.isEmpty) {
                      return const Text("No members found");
                    } else if (chamaController.chamaMembers.isNotEmpty) {
                      return ListView.separated(
                          itemBuilder: (context, index) {
                            final member = chamaController.chamaMembers[index];
                            return ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(9),
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.blueButtonColor
                                          .withOpacity(0.5)),
                                  child: Text("${index + 1}"),
                                ),
                                title: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${member.firstName} ${member.secondName}",
                                      style: context.dividerTextLarge
                                          ?.copyWith(fontSize: 15),
                                    ),
                                    Text(member.phoneNumber ?? ""),
                                    Text(member.role ?? "")
                                  ],
                                ),
                                trailing: Obx(
                                  () => Checkbox(
                                    value:
                                        chamaController.checkboxStates[index],
                                    onChanged: (bool? value) {
                                      chamaController.checkboxStates[index] =
                                          value!;
                                      updateSelectAllState();
                                    },
                                  ),
                                ));
                          },
                          separatorBuilder: (context, index) {
                            return const Divider();
                          },
                          itemCount: chamaController.chamaMembers.length);
                    }
                    return const Text("No members found");
                  }),
            )
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
          backgroundColor: AppColors.blueButtonColor,
          onPressed: () {
            showBottomAddSheet2();
          },
          label: const Text("Penalise")),
    );
  }

  showBottomAddSheet2() {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.7,
              expand: false,
              builder: (context, scrollController) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Text(
                        "Penalties",
                        style: context.titleText,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                          "Choose the type of penalty from the options below, you can also edit the amount to penalise",
                          style: context.titleText?.copyWith(fontSize: 15)),
                    ),
                    Expanded(
                      child: GetX(
                          init: ChamaController(),
                          initState: (state) {
                            Future.delayed(Duration.zero, () async {
                              try {
                                await state.controller?.getChamaPenalties(
                                    chamaId: chamaDataController
                                            .chama.value.chama?.id ??
                                        0);
                              } catch (e) {
                                throw e;
                              }
                            });
                          },
                          builder: (ChamaController chamaController) {
                            if (chamaController
                                .isGetChamaPenaltyLoading.isTrue) {
                              return SizedBox(
                                height: SizeConfig.screenHeight * .33,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SpinKitDualRing(
                                        color: ColorUtil.blueColor,
                                        lineWidth: 4.sp,
                                        size: 40.0.sp,
                                      ),
                                      const Text(
                                        "loading..",
                                        style: TextStyle(
                                          color: Colors.white,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            } else if (chamaController.penalties.isEmpty) {
                              return const Text("No penalties added yet.");
                            } else if (chamaController.penalties.isNotEmpty) {
                              return ListView.separated(
                                  itemBuilder: (context, index) {
                                    final penalty =
                                        chamaController.penalties[index];
                                    return Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 12),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColors.blueButtonColor),
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      child: ListTile(
                                        onTap: () {
                                          Navigator.pop(context);
                                          amountController.text =
                                              penalty.amount.toString();
                                          reasonController.text =
                                              penalty.title ?? "";
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  title: Text(
                                                    "Feel free to edit the penalty details, you can also press cancel if you don't want to edit",
                                                    style: context
                                                        .dividerTextLarge,
                                                  ),
                                                  content: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      CustomTextField(
                                                        controller:
                                                            amountController,
                                                        showNoKeyboard: true,
                                                        labelText:
                                                            'Enter penalty amount',
                                                      ),
                                                      CustomTextField(
                                                        controller:
                                                            reasonController,
                                                        labelText:
                                                            "Enter reason for penalty",
                                                      )
                                                    ],
                                                  ),
                                                  actions: [
                                                    SizedBox(
                                                      height: 45,
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          OutlinedButton(
                                                              onPressed: () {
                                                                Navigator.pop(
                                                                    context);
                                                              },
                                                              child: const Text(
                                                                  "Cancel")),
                                                          Obx(
                                                            () =>
                                                                CustomKtButton(
                                                                    width: 80,
                                                                    isLoading: chamaController
                                                                        .isPenalizeMultiple
                                                                        .isTrue,
                                                                    onPress:
                                                                        () async {
                                                                      final selectedMembers =
                                                                          chamaController
                                                                              .getSelectedMembers();
                                                                      selectedPenalty(
                                                                          penalty
                                                                              .title,
                                                                          penalty
                                                                              .description,
                                                                          penalty
                                                                              .amount,
                                                                          penalty
                                                                              .id);
                                                                      List<MemberBeingPenalized>
                                                                          members =
                                                                          selectedMembers
                                                                              .map((member) {
                                                                        return MemberBeingPenalized(
                                                                            memberId: member
                                                                                .id,
                                                                            amount: amountController.text.isEmpty
                                                                                ? penamount
                                                                                : int.parse(amountController.text.trim()),
                                                                            reason: reasonController.text.isEmpty ? pendescription : reasonController.text.trim());
                                                                      }).toList();
                                                                      MultiplePenaltyRequest request = MultiplePenaltyRequest(
                                                                          isAll: selectAll
                                                                              ? true
                                                                              : false,
                                                                          chamaId: chamaDataController
                                                                              .chama
                                                                              .value
                                                                              .chama
                                                                              ?.id,
                                                                          penaltyId:
                                                                              penaltyId,
                                                                          members:
                                                                              members);
                                                                      bool res =
                                                                          await chamaController.penalizeMultiple(
                                                                              request: request);
                                                                      if (members
                                                                          .isEmpty) {
                                                                        ToastUtils.showErrorToast(
                                                                            context,
                                                                            "Kindly pick at least one member",
                                                                            "Error");
                                                                        return;
                                                                      }
                                                                      if (res) {
                                                                        if (!mounted) {
                                                                          return;
                                                                        }
                                                                        Snack.show(
                                                                            res,
                                                                            chamaController.apiMessage.string);
                                                                        Navigator.pop(
                                                                            context);
                                                                        Get.offNamed(
                                                                            NavRoutes.viewingSingleChama);
                                                                      } else {
                                                                        if (!mounted) {
                                                                          return;
                                                                        }
                                                                        Snack.show(
                                                                            res,
                                                                            chamaController.apiMessage.string);
                                                                      }
                                                                    },
                                                                    btnText:
                                                                        "OK"),
                                                          )
                                                        ],
                                                      ),
                                                    )
                                                  ],
                                                );
                                              });
                                        },
                                        tileColor: AppColors.slate,
                                        title: Text(
                                          penalty.title ?? "",
                                          style: context.dividerTextLarge,
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(penalty.description ?? ""),
                                            Text(
                                              "${penalty.amount}",
                                              style: context.dividerTextLarge
                                                  ?.copyWith(color: Colors.red),
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                  separatorBuilder: (context, index) {
                                    return const Divider();
                                  },
                                  itemCount: chamaController.penalties.length);
                            }
                            return const Text("No penalties added yet.");
                          }),
                    ),
                  ],
                );
              });
        });
  }

  void selectedPenalty(String? selectedTitle, String? selectedDes,
      int? selectedAmt, int? selectedId) {
    if (selectedTitle is String) {
      setState(() {
        pentitle = selectedTitle;
        pendescription = selectedDes;
        penamount = selectedAmt;
        penaltyId = selectedId;
      });
    }
  }
}
