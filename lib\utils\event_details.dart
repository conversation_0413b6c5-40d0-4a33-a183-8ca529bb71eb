import 'package:flutter/material.dart';

class EventDetailsWidget extends StatelessWidget {
  final String label;
  final String? image;
  final String id;
  final IconData? icon;
  const EventDetailsWidget(
      {super.key,
      required this.label,
      this.icon,
      required this.id,
      this.image});

  @override
  Widget build(BuildContext context) {
    if (icon == null && image == null) {
      throw "image and icon cannot both be null";
    }
    return Padding(
      padding: const EdgeInsets.only(top: 4.0, bottom: 4),
      child: Row(
        children: [
          Hero(
            tag: 'i$id',
            child: image != null
                ? Image.asset(
                    image!,
                    height: 20,
                    width: 20,
                    color: Colors.grey.shade700,
                  )
                : Icon(
                    icon,
                    color: Colors.grey.shade700,
                    size: 20,
                  ),
          ),
          const SizedBox(width: 8),
          Expanded(
              child: Hero(
            tag: 't$id',
            child: Material(
              color: Colors.transparent,
              child: Text(
                label,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ))
        ],
      ),
    );
  }
}
