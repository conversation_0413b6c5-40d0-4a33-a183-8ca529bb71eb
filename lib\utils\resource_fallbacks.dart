import 'package:flutter/material.dart';

/// Class that provides fallback assets and widgets when original resources fail to load
class ResourceFallbacks {
  /// Fallback widget for when images fail to load
  static Widget imagePlaceholder({
    double size = 100,
    Color color = Colors.grey,
    double opacity = 0.3,
  }) {
    return Container(
      width: size,
      height: size,
      color: Colors.grey.withOpacity(0.1),
      child: Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: color.withOpacity(opacity),
          size: size * 0.5,
        ),
      ),
    );
  }

  /// Fallback widget for when profile images fail to load
  static Widget profilePlaceholder({
    double size = 50,
    Color color = Colors.grey,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withOpacity(0.2),
      ),
      child: Center(
        child: Icon(
          Icons.person,
          color: color,
          size: size * 0.6,
        ),
      ),
    );
  }

  /// Fallback widget for when data fails to load
  static Widget dataPlaceholder({
    required String message,
    IconData icon = Icons.error_outline,
    VoidCallback? onRetry,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 50, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Returns network image with error builder to prevent gray screens
  static Image safeNetworkImage(
    String url, {
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image.network(
      url,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        return imagePlaceholder(
          size: width ?? height ?? 100,
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
          ),
        );
      },
    );
  }

  /// Returns asset image with error builder to prevent gray screens
  static Image safeAssetImage(
    String path, {
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image.asset(
      path,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        return imagePlaceholder(
          size: width ?? height ?? 100,
        );
      },
    );
  }
} 