class EventMedia {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? eventId;
  final String? url;
  final String? title;
  final String? description;
  final String? type;
  final String? category;

  EventMedia({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.eventId,
    this.url,
    this.title,
    this.description,
    this.type,
    this.category,
  });

  factory EventMedia.fromJson(Map<String, dynamic> json) => EventMedia(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] != null
            ? DateTime.parse(json["DeletedAt"])
            : null,
        eventId: json["event_id"],
        url: json["url"],
        title: json["title"],
        description: json["description"],
        type: json["type"],
        category: json["category"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "event_id": eventId,
        "url": url,
        "title": title,
        "description": description,
        "type": type,
        "category": category,
      };
}
