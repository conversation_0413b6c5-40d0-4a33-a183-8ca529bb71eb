import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/beneficiaries_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/end_date.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/step1.dart';
import 'package:onekitty/screens/dashboard/pages/events/manage_delegates.dart';
import '../../../../../../utils/utils_exports.dart';
import '../../../events/signatory_transactions.dart';

import 'package:onekitty/screens/dashboard/pages/events/view_single_event_organizer.dart'
    as e;

class ServicesWidget extends StatelessWidget {
  const ServicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final hasSignatoryTransactions =
        Get.find<ContributeController>().hasSignatoryTransactions;
    return Column(
      children: [
        SizedBox(height: 8.h),
        GridView.count(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          children: [
            e.ServicesWidget(
                icon: Icons.person,
                image: AssetUrl.imgCalendar,
                label: "Edit End Date",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => const EndDate()),
                      (route) => route.isActive);
                }),
            e.ServicesWidget(
              icon: Icons.person,
              image: AssetUrl.imgEdit,
              label: "Edit Details",
              onTap: () {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const EditKittyDetails()),
                    (route) => route.isActive);
              },
            ),
            e.ServicesWidget(
              icon: Icons.person,
              image: AssetUrl.imgUser,
              label: "Withdraw",
              onTap: () {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const WithdrawPage()),
                    (route) => route.isActive);
              },
            ),
            e.ServicesWidget(
                icon: Icons.group_outlined,
                label: "Beneficiaries",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const BeneficiariesPage()),
                      (route) => route.isActive);
                }),
            e.ServicesWidget(
                icon: Icons.manage_accounts_outlined,
                label: "Delegates",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => InvitePage(
                                type: 'kitty',
                                kittyId: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.iD ??
                                    0,
                                eventname: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.title ??
                                    '',
                              )),
                      (route) => route.isActive);
                }),
            Obx(() => e.ServicesWidget(
                  flash: hasSignatoryTransactions.value,
                  image: AssetUrl.imgGroup7,
                  icon: Icons.person,
                  label: "Signatory Approvals",
                  onTap: () {
                    Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                            builder: (context) => SignatoryTransactions(
                                  kittyId: Get.find<DataController>()
                                          .kitty
                                          .value
                                          .kitty
                                          ?.iD ??
                                      0,
                                )),
                        (route) => route.isActive);
                  },
                )),
            e.ServicesWidget(
                icon: Icons.monetization_on_outlined,
                label: "Contribute",
                onTap: () {
                  final kittyId =
                      Get.find<DataController>().kitty.value.kitty?.iD ?? 0;
                  String route = NavRoutes.kittycontributionScreen;
                  route = route.replaceAll(":id", kittyId.toString());
                  Get.toNamed(route);
                }),
            e.ServicesWidget(
                icon: Icons.import_export,
                label: "Export Transactions",
                onTap: () {
                  final kittyId =
                      Get.find<DataController>().kitty.value.kitty?.iD ?? 0;

                  showDialog(
                    context: context,
                    barrierDismissible: true,
                    builder: (context) => const Dialog(
                      child: SizedBox(
                        height: 100,
                        width: 100,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Center(child: CircularProgressIndicator()),
                            SizedBox(height: 16),
                            Text('Fetching Transactions...'),
                          ],
                        ),
                      ),
                    ),
                  );
                  Get.put(KittyController())
                      .getKittyContributions(kittyId: kittyId)
                      .then((_) {
                    Navigator.pop(context); // Dismiss loading dialog
                    showModalBottomSheet(
                      context: context,
                      builder: (BuildContext context) {
                        return ExportContentWidget2(
                          eventId: null,
                          singleTrans: false,
                        );
                      },
                    );
                  }).catchError((_) {
                    Navigator.pop(context); // Dismiss loading dialog on error
                  });
                }),
          ],
        ),
      ],
    );
  }
}
/*
class e.ServicesWidget extends StatelessWidget {
  final String? edit;
  final IconData? icon;
  final String resources;
  final VoidCallback onTap;
  final bool? flash;
  final Color? color;

  const e.ServicesWidget({
    super.key,
    this.edit,
    this.icon,
    required this.resources,
    required this.onTap,
    this.flash,
    this.color,
  }) : assert(
            edit != null || icon != null, "both edit and icon cannot be null");

  @override
  Widget build(BuildContext context) {
    final flashColor = color?.obs ?? AppColors.blueButtonColor.obs;
    if (flash ?? false) {
      Timer.periodic(const Duration(milliseconds: 800), (t) {
        flashColor.value =
            t.tick.isEven ? AppColors.blueButtonColor : Colors.red;
      });
    }
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
          vertical: 4.h,
        ),
        decoration: AppDecoration.outlineIndigo.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              edit != null
                  ? SvgPicture.asset(edit!,
                      height: 30.h,
                      width: 30.w,
                      colorFilter:
                          ColorFilter.mode(flashColor.value, BlendMode.srcIn))
                  : Icon(icon, size: 30.spMin, color: flashColor.value),
              // CustomImageView(
              //   color: color,
              //   imagePath: edit,
              //   height: 30.h,
              //   width: 30.w,
              // ),
              SizedBox(height: 2.h),
              Text(
                resources,
                style: theme.textTheme.labelLarge!.copyWith(
                  color: flashColor.value,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
*/
