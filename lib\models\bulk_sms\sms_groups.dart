import 'dart:convert';

class SmsGroups {
    final int? id;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final dynamic deletedAt;
    final String? name;
    final String? phoneNumber;
    final int? externalUserId;
    final bool? isActive;
    final String? description;
    final List<SmsGroupMember>? smsGroupMembers;

    SmsGroups({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.name,
        this.phoneNumber,
        this.externalUserId,
        this.isActive,
        this.description,
        this.smsGroupMembers,
    });

    factory SmsGroups.fromRawJson(String str) => SmsGroups.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory SmsGroups.fromJson(Map<String, dynamic> json) => SmsGroups(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        name: json["name"],
        phoneNumber: json["phone_number"] ?? "",
        externalUserId: json["external_user_id"],
        isActive: json["is_active"],
        description: json["description"],
        smsGroupMembers: json["sms_group_members"] == null ? [] : List<SmsGroupMember>.from(json["sms_group_members"]!.map((x) => SmsGroupMember.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "name": name,
        "phone_number": phoneNumber,
        "external_user_id": externalUserId,
        "is_active": isActive,
        "description": description,
        "sms_group_members": smsGroupMembers == null ? [] : List<dynamic>.from(smsGroupMembers!.map((x) => x.toJson())),
    };
}

class SmsGroupMember {
    final int? id;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final dynamic deletedAt;
    final int? smsGroupId;
    final String? phoneNumber;
    final String? firstName;
    final String? secondName;

    SmsGroupMember({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.smsGroupId,
        this.phoneNumber,
        this.firstName,
        this.secondName,
    });
 @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is SmsGroupMember &&
      other.firstName == firstName &&
      other.secondName == secondName &&
      other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode => firstName.hashCode ^ secondName.hashCode ^ phoneNumber.hashCode;

    factory SmsGroupMember.fromRawJson(String str) => SmsGroupMember.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory SmsGroupMember.fromJson(Map<String, dynamic> json) => SmsGroupMember(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        smsGroupId: json["sms_group_id"],
        phoneNumber: json["phone_number"] ?? "",
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "sms_group_id": smsGroupId,
        "phone_number": phoneNumber,
        "first_name": firstName,
        "second_name": secondName,
    };
}
