// To parse this JSON data, do
//
//     final chamaMembers = chamaMembersFromJson(jsonString);

import 'dart:convert';

List<UserChama> chamaMembersFromJson(String str) =>
    List<UserChama>.from(json.decode(str).map((x) => UserChama.fromJson(x)));

String chamaMembersToJson(List<UserChama> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class UserChama {
  int? membersCount;
  Chama? chama;
  Member? member;

  UserChama({
    this.membersCount,
    this.chama,
    this.member,
  });

  factory UserChama.fromJson(Map<String, dynamic> json) => UserChama(
        membersCount: json["members_count"],
        chama: json["chama"] == null ? null : Chama.fromJson(json["chama"]),
        member: json["member"] == null ? null : Member.from<PERSON><PERSON>(json["member"]),
      );

  Map<String, dynamic> toJson() => {
        "members_count": membersCount,
        "chama": chama?.toJson(),
        "member": member?.toJson(),
      };
}

class Chama {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  int? kittyId;
  final String? title;
  final String? username;
  final String? description;
  final String? phoneNumber;
  final String? accountNumber;
  final String? accountNumberRef;
  final int? channel;
  final String? transferMode;
  final double? balance;
  final double? totaBal;
  final dynamic amount;
  final String? email;
  final int? refererCode;
  DateTime? nextOccurrence;
  String? profileUrl;
  //DateTime? lastOccurrence;
  final String? frequency;
  String? status;
  final int? lastReceivedMemberId;

  Chama({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.kittyId,
    this.title,
    this.username,
    this.description,
    this.phoneNumber,
    this.accountNumber,
    this.accountNumberRef,
    this.profileUrl,
    this.channel,
    this.transferMode,
    this.balance,
    this.totaBal,
    this.amount,
    this.email,
    this.refererCode,
    this.nextOccurrence,
    //this.lastOccurrence,
    this.frequency,
    this.status,
    this.lastReceivedMemberId,
  });

  factory Chama.fromJson(Map<String, dynamic> json) => Chama(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        kittyId: json["kitty_id"],
        title: json["title"],
        username: json["username"],
        description: json["description"],
        phoneNumber: json["phone_number"] ?? "",
        accountNumber: json["account_number"],
        accountNumberRef: json["account_number_ref"],
        profileUrl: json['profile_url'],
        channel: json["channel"],
        transferMode: json["transfer_mode"],
        balance: double.tryParse(
            json["balance"] != null ? json["balance"].toString() : '0.0'),
        totaBal: double.tryParse(json["total_balance"] != null
            ? json["total_balance"].toString()
            : '0.0'),
        amount: json["amount"],
        email: json["email"],
        refererCode: json["referer_code"],
        nextOccurrence: json["next_occurrence"] == null
            ? null
            : DateTime.parse(json["next_occurrence"]),
        //lastOccurrence: DateTime.parse(json["last_occurrence"]),
        frequency: json["frequency"],
        status: json["status"]?.toString(),
        lastReceivedMemberId: json["LastReceivedMemberID"],
      );
  Chama copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    int? kittyId,
    String? title,
    String? username,
    String? description,
    String? phoneNumber,
    String? accountNumber,
    String? accountNumberRef,
    String? profileUrl,
    int? channel,
    String? transferMode,
    double? balance,
    double? totaBal,
    dynamic amount,
    String? email,
    int? refererCode,
    DateTime? nextOccurrence,
    String? frequency,
    String? status,
    int? lastReceivedMemberId,
  }) {
    return Chama(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      kittyId: kittyId ?? this.kittyId,
      title: title ?? this.title,
      username: username ?? this.username,
      description: description ?? this.description,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      accountNumber: accountNumber ?? this.accountNumber,
      accountNumberRef: accountNumberRef ?? this.accountNumberRef,
      profileUrl: profileUrl ?? this.profileUrl,
      channel: channel ?? this.channel,
      transferMode: transferMode ?? this.transferMode,
      balance: balance ?? this.balance,
      totaBal: totaBal ?? this.totaBal,
      amount: amount ?? this.amount,
      email: email ?? this.email,
      refererCode: refererCode ?? this.refererCode,
      nextOccurrence: nextOccurrence ?? this.nextOccurrence,
      frequency: frequency ?? this.frequency,
      status: status ?? this.status,
      lastReceivedMemberId: lastReceivedMemberId ?? this.lastReceivedMemberId,
    );
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "kitty_id": kittyId,
        "title": title,
        "profile_url": profileUrl,
        "username": username,
        "description": description,
        "phone_number": phoneNumber,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
        "channel": channel,
        "transfer_mode": transferMode,
        "balance": balance,
        "total_balance": totaBal,
        "amount": amount,
        "email": email,
        "referer_code": refererCode,
        "next_occurrence": nextOccurrence!.toIso8601String(),
        //"last_occurrence": lastOccurrence!.toIso8601String(),
        "frequency": frequency,
        "status": status,
        "LastReceivedMemberID": lastReceivedMemberId,
      };
}

class Member {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? phoneNumber;
  dynamic countryCode;
  String? firstName;
  String? secondName;
  String? role;
  dynamic beneficiary;
  int? chamaId;
  int? receivingOrder;
  String? status;

  Member({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.phoneNumber,
    this.countryCode,
    this.firstName,
    this.secondName,
    this.role,
    this.beneficiary,
    this.chamaId,
    this.receivingOrder,
    this.status,
  });

  factory Member.fromJson(Map<String, dynamic> json) => Member(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["country_code"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        role: json["role"],
        beneficiary: json["beneficiary"],
        chamaId: json["chama_id"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "user_id": userId,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "beneficiary": beneficiary,
        "chama_id": chamaId,
        "receiving_order": receivingOrder,
        "status": status,
      };
}

// To parse this JSON data, do
//
//     final allChamaDts = allChamaDtsFromJson(jsonString);

AllChamaDts allChamaDtsFromJson(String str) =>
    AllChamaDts.fromJson(json.decode(str));

String allChamaDtsToJson(AllChamaDts data) => json.encode(data.toJson());

class AllChamaDts {
  bool? status;
  String? message;
  DataCls? data;

  AllChamaDts({
    this.status,
    this.message,
    this.data,
  });

  factory AllChamaDts.fromJson(Map<String, dynamic> json) => AllChamaDts(
        status: json["status"],
        message: json["message"],
        data: DataCls.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class DataCls {
  ChamaCls chama;
  bool isSignatory;
  List<MemberCls> members;
  int membersCount;
  List<NextBeneficiary> nextBeneficiaries;
  List<NotificationCls> notification;
  int penaltiesCount;
  int penaltyKittyBalance;
  Settings settings;

  DataCls({
    required this.chama,
    required this.isSignatory,
    required this.members,
    required this.membersCount,
    required this.nextBeneficiaries,
    required this.notification,
    required this.penaltiesCount,
    required this.penaltyKittyBalance,
    required this.settings,
  });

  factory DataCls.fromJson(Map<String, dynamic> json) => DataCls(
        chama: ChamaCls.fromJson(json["chama"]),
        isSignatory: json["is_signatory"],
        members: List<MemberCls>.from(
            json["members"].map((x) => MemberCls.fromJson(x))),
        membersCount: json["members_count"],
        nextBeneficiaries: List<NextBeneficiary>.from(
            json["next_beneficiaries"].map((x) => NextBeneficiary.fromJson(x))),
        notification: List<NotificationCls>.from(
            json["notification"].map((x) => NotificationCls.fromJson(x))),
        penaltiesCount: json["penalties_count"],
        penaltyKittyBalance: json["penalty_kitty_balance"],
        settings: Settings.fromJson(json["settings"]),
      );

  Map<String, dynamic> toJson() => {
        "chama": chama.toJson(),
        "is_signatory": isSignatory,
        "members": List<dynamic>.from(members.map((x) => x.toJson())),
        "members_count": membersCount,
        "next_beneficiaries":
            List<dynamic>.from(nextBeneficiaries.map((x) => x.toJson())),
        "notification": List<dynamic>.from(notification.map((x) => x.toJson())),
        "penalties_count": penaltiesCount,
        "penalty_kitty_balance": penaltyKittyBalance,
        "settings": settings.toJson(),
      };
}

class ChamaCls {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? kittyId;
  String? title;
  String? username;
  String? description;
  String? profileUrl;
  String? phoneNumber;
  String? accountNumber;
  String? accountNumberRef;
  int? channel;
  String? transferMode;
  int? balance;
  int? totaBal;
  int? amount;
  String? email;
  int? refererCode;
  DateTime? nextOccurrence;
  String? frequency;
  String? status;
  dynamic lastReceivedMemberId;
  dynamic lastReceivedOrder;

  ChamaCls({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.kittyId,
    this.title,
    this.username,
    this.description,
    this.profileUrl,
    this.phoneNumber,
    this.accountNumber,
    this.accountNumberRef,
    this.channel,
    this.transferMode,
    this.balance,
    this.totaBal,
    this.amount,
    this.email,
    this.refererCode,
    this.nextOccurrence,
    this.frequency,
    this.status,
    this.lastReceivedMemberId,
    this.lastReceivedOrder,
  });

  factory ChamaCls.fromJson(Map<String, dynamic> json) => ChamaCls(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        kittyId: json["kitty_id"],
        title: json["title"],
        username: json["username"],
        description: json["description"],
        profileUrl: json["profile_url"],
        phoneNumber: json["phone_number"] ?? "",
        accountNumber: json["account_number"],
        accountNumberRef: json["account_number_ref"],
        channel: json["channel"],
        transferMode: json["transfer_mode"],
        balance: json["balance"],
        totaBal: json["total_balance"],
        amount: json["amount"],
        email: json["email"],
        refererCode: json["referer_code"],
        nextOccurrence: DateTime.parse(json["next_occurrence"]),
        frequency: json["frequency"],
        status: json["status"],
        lastReceivedMemberId: json["last_received_member_id"],
        lastReceivedOrder: json["last_received_order"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "kitty_id": kittyId,
        "title": title,
        "username": username,
        "description": description,
        "profile_url": profileUrl,
        "phone_number": phoneNumber,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
        "channel": channel,
        "transfer_mode": transferMode,
        "balance": balance,
        "total_balance": totaBal,
        "amount": amount,
        "email": email,
        "referer_code": refererCode,
        "next_occurrence": nextOccurrence?.toIso8601String(),
        "frequency": frequency,
        "status": status,
        "last_received_member_id": lastReceivedMemberId,
        "last_received_order": lastReceivedOrder,
      };
}

class MemberCls {
  int id;
  DateTime createdAt;
  DateTime updatedAt;
  dynamic deletedAt;
  int? userId;
  String profileUrl;
  String phoneNumber;
  String countryCode;
  String firstName;
  String secondName;
  String role;
  dynamic beneficiary;
  int chamaId;
  int receivingOrder;
  String status;

  MemberCls({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.userId,
    required this.profileUrl,
    required this.phoneNumber,
    required this.countryCode,
    required this.firstName,
    required this.secondName,
    required this.role,
    required this.beneficiary,
    required this.chamaId,
    required this.receivingOrder,
    required this.status,
  });

  factory MemberCls.fromJson(Map<String, dynamic> json) => MemberCls(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        profileUrl: json["profile_url"],
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["country_code"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        role: json["role"],
        beneficiary: json["beneficiary"],
        chamaId: json["chama_id"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt.toIso8601String(),
        "UpdatedAt": updatedAt.toIso8601String(),
        "DeletedAt": deletedAt,
        "user_id": userId,
        "profile_url": profileUrl,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "beneficiary": beneficiary,
        "chama_id": chamaId,
        "receiving_order": receivingOrder,
        "status": status,
      };
}

class NextBeneficiary {
  double? beneficiaryPercentage;
  dynamic amountToReceive;
  Member? member;
  List<Beneficiary>? beneficiaries;

  NextBeneficiary({
    this.beneficiaryPercentage,
    this.amountToReceive,
    this.member,
    this.beneficiaries,
  });

  factory NextBeneficiary.fromJson(Map<String, dynamic> json) =>
      NextBeneficiary(
        beneficiaryPercentage: json["beneficiary_percentage"]?.toDouble(),
        amountToReceive: json["amount_to_receive"],
        member: Member.fromJson(json["member"]),
        beneficiaries: List<Beneficiary>.from(
            json["beneficiaries"].map((x) => Beneficiary.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "beneficiary_percentage": beneficiaryPercentage,
        "amount_to_receive": amountToReceive,
        "member": member?.toJson(),
        "beneficiaries":
            List<dynamic>.from(beneficiaries!.map((x) => x.toJson())),
      };
}

class Beneficiary {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  int? userId;
  int? chamaId;
  int? memberId;
  String? transferMode;
  dynamic percentage;
  String? beneficiaryType;
  String? channelName;
  int? channel;
  String? status;
  String? accountNumber;
  String? accountNumberRef;

  Beneficiary({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.userId,
    this.chamaId,
    this.memberId,
    this.transferMode,
    this.percentage,
    this.beneficiaryType,
    this.channelName,
    this.channel,
    this.status,
    this.accountNumber,
    this.accountNumberRef,
  });

  factory Beneficiary.fromJson(Map<String, dynamic> json) => Beneficiary(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        userId: json["user_id"],
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        transferMode: json["transfer_mode"],
        percentage: json["percentage"],
        beneficiaryType: json["beneficiary_type"],
        channelName: json["channel_name"],
        channel: json["channel"],
        status: json["status"],
        accountNumber: json["account_number"],
        accountNumberRef: json["account_number_ref"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "user_id": userId,
        "chama_id": chamaId,
        "member_id": memberId,
        "transfer_mode": transferMode,
        "percentage": percentage,
        "beneficiary_type": beneficiaryType,
        "channel_name": channelName,
        "channel": channel,
        "status": status,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
      };
}

class NotificationCls {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? chamaId;
  String? phoneNumber;
  String? jid;
  String? jidUser;
  int? jidAgent;
  int? jidDevice;
  bool? jidAd;
  String? jidServer;
  String? whatsAppLink;
  String? whatsappGroupName;
  String? email;
  String? whatsappStatus;
  String? whatsappProfile;
  String? onekittyNumber;

  NotificationCls({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.chamaId,
    this.phoneNumber,
    this.jid,
    this.jidUser,
    this.jidAgent,
    this.jidDevice,
    this.jidAd,
    this.jidServer,
    this.whatsAppLink,
    this.whatsappGroupName,
    this.email,
    this.whatsappStatus,
    this.whatsappProfile,
    this.onekittyNumber,
  });

  factory NotificationCls.fromJson(Map<String, dynamic> json) =>
      NotificationCls(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        phoneNumber: json["phone_number"] ?? "",
        jid: json["jid"],
        jidUser: json["jid_user"],
        jidAgent: json["jid_agent"],
        jidDevice: json["jid_device"],
        jidAd: json["jid_ad"],
        jidServer: json["jid_server"],
        whatsAppLink: json["whats_app_link"],
        whatsappGroupName: json["whatsapp_group_name"],
        email: json["email"],
        whatsappStatus: json["whatsapp_status"],
        whatsappProfile: json["whatsapp_profile"],
        onekittyNumber: json["onekitty_number"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "phone_number": phoneNumber,
        "jid": jid,
        "jid_user": jidUser,
        "jid_agent": jidAgent,
        "jid_device": jidDevice,
        "jid_ad": jidAd,
        "jid_server": jidServer,
        "whats_app_link": whatsAppLink,
        "whatsapp_group_name": whatsappGroupName,
        "email": email,
        "whatsapp_status": whatsappStatus,
        "whatsapp_profile": whatsappProfile,
        "onekitty_number": onekittyNumber,
      };
}

class Settings {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? chamaId;
  int? beneficiariesPerCycle;
  double? beneficiaryPercentage;
  int? signatureThreshold;

  Settings({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.chamaId,
    this.beneficiariesPerCycle,
    this.beneficiaryPercentage,
    this.signatureThreshold,
  });

  factory Settings.fromJson(Map<String, dynamic> json) => Settings(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        beneficiariesPerCycle: json["beneficiaries_per_cycle"],
        beneficiaryPercentage: json["beneficiary_percentage"]?.toDouble(),
        signatureThreshold: json["signature_threshold"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "beneficiaries_per_cycle": beneficiariesPerCycle,
        "beneficiary_percentage": beneficiaryPercentage,
        "signature_threshold": signatureThreshold,
      };
}

// To parse this JSON data, do
//
//     final chamaFromKitty = chamaFromKittyFromJson(jsonString);

ChamaFromKitty chamaFromKittyFromJson(String str) =>
    ChamaFromKitty.fromJson(json.decode(str));

String chamaFromKittyToJson(ChamaFromKitty data) => json.encode(data.toJson());

class ChamaFromKitty {
  String? title;
  String? username;
  String? description;
  String? profileUrl;
  int? amount;
  DateTime? deadline;
  String? frequency;
  String? status;
  DateTime? createdAt;

  ChamaFromKitty({
    this.title,
    this.username,
    this.description,
    this.profileUrl,
    this.amount,
    this.deadline,
    this.frequency,
    this.status,
    this.createdAt,
  });

  ChamaFromKitty copyWith({
    String? title,
    String? username,
    String? description,
    String? profileUrl,
    int? amount,
    DateTime? deadline,
    String? frequency,
    String? status,
    DateTime? createdAt,
  }) =>
      ChamaFromKitty(
        title: title ?? this.title,
        username: username ?? this.username,
        description: description ?? this.description,
        profileUrl: profileUrl ?? this.profileUrl,
        amount: amount ?? this.amount,
        deadline: deadline ?? this.deadline,
        frequency: frequency ?? this.frequency,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
      );

  factory ChamaFromKitty.fromJson(Map<String, dynamic> json) => ChamaFromKitty(
        title: json["title"],
        username: json["username"],
        description: json["description"],
        profileUrl: json["profile_url"],
        amount: json["amount"],
        deadline:
            json["deadline"] == null ? null : DateTime.parse(json["deadline"]),
        frequency: json["frequency"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "username": username,
        "description": description,
        "profile_url": profileUrl,
        "amount": amount,
        "deadline": deadline?.toIso8601String(),
        "frequency": frequency,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
      };
}
