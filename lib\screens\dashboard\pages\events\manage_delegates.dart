 
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';

import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/events/invite_page_controller.dart';
import 'package:onekitty/models/events/operators_model.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:shimmer/shimmer.dart'; 

class InvitePage extends StatefulWidget {
  final int kittyId;
  final String eventname;

  /// whether its event, chama or kitty;
  /// Specifies the classification or category of the operation being performed.
  final String type;
  const InvitePage(
      {super.key,
      required this.kittyId,
      required this.eventname,
      this.type = "event"});

  @override
  State<InvitePage> createState() => _InvitePageState();
}

class _InvitePageState extends State<InvitePage> {
  final TextEditingController _phoneNumber = TextEditingController();
  String? number;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalControllers _controller = Get.put(GlobalControllers());
  final InvitePageController controller = Get.put(InvitePageController());
  @override
  void initState() {
    super.initState();
    controller.fetchOperators(widget.kittyId);
    _controller.onInit();
  }

  @override
  void dispose() {
    _phoneNumber.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.eventname,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(
                  () => Text(
                    '${controller.operators.length} ${controller.operators.length > 1 ? 'Operators' : 'Operator'}',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
                Form(
                  key: _formKey,
                  child: CustomInternationalPhoneInput(
                    controller: _phoneNumber,
                    onInputChanged: (PhoneNumber numbers) {
                      number = numbers.phoneNumber.toString().replaceAll("+", '');
                    },
                    onInputValidated: (bool value) {},
                    ignoreBlank: false,
                    initialValue: PhoneNumber(isoCode: 'KE', dialCode: '+254'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Phone Number is required';
                      }
                      return null;
                    },
                    formatInput: true,
                  /*  inputDecoration: InputDecoration(
                      hintText: "eg. 0712345678",
                      suffixIcon: IconButton(
                          onPressed: () async {
                            Contact? result = await showDialog<Contact>(
                                context: context,
                                builder: (context) {
                                  return const Dialog(
                                      child: GetXContactPicker(
                                    mode: ContactPickerMode.single,
                                    title: 'Select Contact',
                                  ));
                                });

                            // Handle the returned result when dialog is popped
                            if (result != null) {
                              final phoneNumber = ContactPickerHelper.getNormalizedPhoneNumber(result);

                              // Format phone number for API (254 format)
                              String formattedPhone = phoneNumber.startsWith('0')
                                  ? "254${phoneNumber.substring(1)}"
                                  : phoneNumber;

                              // Show the operator details form with the selected contact
                              showDialog(
                                  context: context,
                                  builder: (BuildContext context) =>
                                      OperatorsDetailsForm(
                                        phone: formattedPhone,
                                        name: ContactPickerHelper.getDisplayName(result),
                                        kittyId: widget.kittyId,
                                      ));
                            }
                          },
                          icon: const Icon(Icons.contacts_rounded)),
                      filled: true,
                      fillColor: Colors.white70,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          width: 1,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  */),
                ),
                SizedBox(height: 20.h),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) =>
                              OperatorsDetailsForm(
                                phone: number??'',
                                kittyId: widget.kittyId,
                              )).whenComplete(() {
                        _phoneNumber.clear();
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      SizedBox(width: 8.w),
                      Text(
                        'Invite Member',
                        style: TextStyle(fontSize: 16.sp, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '${widget.type} operators',
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 8.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Click on a member to set their role in ${widget.type} management',
                    style: TextStyle(
                        fontSize: 13.sp,
                        color: Colors.grey[900],
                        fontStyle: FontStyle.italic),
                  ),
                ),
                SizedBox(height: 20.h),
                Obx(() => controller.isLoadingOperators.value
                    ? ListView.builder(
                        itemCount: 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: double.infinity,
                              height: 100.h,
                              color: Colors.white,
                            ),
                          );
                        })
                    : controller.operators.isEmpty
                        ? const Center(child: Text('No Operators found'))
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: controller.operators.length,
                            itemBuilder: (context, index) {
                              if (controller.isLoadingOperators.value) {
                                return ListView.builder(
                                    itemCount: 3,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      return Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          margin: const EdgeInsets.all(4),
                                          width: double.infinity,
                                          height: 100.h,
                                          color: Colors.white,
                                        ),
                                      );
                                    });
                              }
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) => EditOperator(
                                              details: controller.operators[
                                                  index])).whenComplete(
                                          () => _phoneNumber.clear());
                                    },
                                    child: Row(children: [
                                      Text('${index + 1}'),
                                      SizedBox(width: 8.w),
                                      const CircleAvatar(
                                        child: Icon(Icons.person),
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Wrap(
                                                children: [
                                                  Text(
                                                      "${controller.operators[index].firstName == "" && controller.operators[index].lastName == "" ? controller.operators[index].phoneNumber : ''}"
                                                      "${controller.operators[index].firstName} ${controller.operators[index].lastName}",
                                                      style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w600)),
                                                  SizedBox(width: 8.w),
                                                  if (controller
                                                      .operators[index]
                                                      .isSignatory)
                                                    Container(
                                                      width: 82.w,
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 6.0,
                                                          vertical: 2.0),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            color: const Color(
                                                                0xFFD4AF37)),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(25),
                                                        color:
                                                            Colors.transparent,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Icon(
                                                              Icons
                                                                  .verified_outlined,
                                                              color: const Color(
                                                                  0xFFD4AF37),
                                                              size: 12.sp),
                                                          const SizedBox(
                                                              width: 4),
                                                          FittedBox(
                                                            child: Text(
                                                              'Signatory',
                                                              style: TextStyle(
                                                                color: const Color(
                                                                    0xFFD4AF37),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize:
                                                                    10.spMin,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  if (controller
                                                      .operators[index]
                                                      .adminLocked)
                                                    Container(
                                                      width: 102.w,
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 6.0,
                                                          vertical: 2.0),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                          color:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(25),
                                                        color:
                                                            Colors.transparent,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Icon(Icons.lock,
                                                              color: Theme.of(
                                                                      context)
                                                                  .primaryColor,
                                                              size: 12.sp),
                                                          const SizedBox(
                                                              width: 4),
                                                          FittedBox(
                                                            child: Text(
                                                              'Admin Locked',
                                                              style: TextStyle(
                                                                color: Theme.of(
                                                                        context)
                                                                    .primaryColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize:
                                                                    10.spMin,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  /*
                                                   
                                                    Chip(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              0),
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(25),
                                                        side: const BorderSide(
                                                            color: Color(
                                                                0xFFD4AF37),
                                                            width: 1),
                                                      ),
                                                      label: const Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Icon(Icons.verified,
                                                              color: Color(
                                                                  0xFFD4AF37),
                                                              size: 12),
                                                          SizedBox(width: 4),
                                                          Text('Signatory',
                                                              style: TextStyle(
                                                                color: Color(
                                                                    0xFFD4AF37),
                                                                fontSize: 10,
                                                                letterSpacing:
                                                                    1.2,
                                                              ))
                                                        ],
                                                      ),
                                                    )
                                             */
                                                ],
                                              ),
                                              SizedBox(height: 2.h),
                                              Text(
                                                controller
                                                    .operators[index].role,
                                                style: TextStyle(
                                                    fontSize: 14.spMin,
                                                    color: Colors.grey[700]),
                                              ),
                                            ]),
                                      ),
                                      if (controller
                                              .operators[index].phoneNumber !=
                                          Get.put(Eventcontroller())
                                              .getLocalUser()
                                              ?.phoneNumber)
                                        IconButton(
                                          icon: const Icon(Icons.close,
                                              color: Colors.grey),
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return AlertDialog(
                                                  title: const Text(
                                                      'Confirm Deletion'),
                                                  content: const Text(
                                                      'Are you sure you want to delete this operator?'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () {
                                                        Navigator.of(context)
                                                            .pop(); // Close the dialog
                                                      },
                                                      child:
                                                          const Text('Cancel'),
                                                    ),
                                                    TextButton(
                                                      onPressed: () {
                                                        controller.deleteOperator(
                                                            controller
                                                                    .operators[
                                                                        index]
                                                                    .id ??
                                                                0,
                                                            widget.kittyId);
                                                        Navigator.of(context)
                                                            .pop(); // Close the dialog
                                                      },
                                                      child:
                                                          const Text('Delete'),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                    ]),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: Divider(color: Colors.grey[300]),
                                  ),
                                ],
                              );
                            })),
              ],
            ),
          ),
        ));
  }
}

class OperatorsDetailsForm extends StatelessWidget {
  final int kittyId;
  final String phone;
  final String? name;
  const OperatorsDetailsForm(
      {super.key, required this.kittyId, required this.phone, this.name});

  @override
  Widget build(BuildContext context) {
    final isAdminLocked = false.obs;
    List<String> nameParts = name?.split(' ') ?? [];
    String firstname = nameParts.isNotEmpty ? nameParts.first : '';
    String secondname =
        nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';
    final formValid = GlobalKey<FormState>();
    final firstName = TextEditingController(text: firstname);
    final lastName = TextEditingController(text: secondname);
    RxString selectedRole = 'VIEWER'.obs;
    RxBool isSignatory = false.obs;
    final _controller = Get.put(InvitePageController());
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 18),
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(8),
        contentPadding: const EdgeInsets.all(8),
        title: Text('Invite Member - $phone'),
        content: SingleChildScrollView(
          child: Form(
            key: formValid,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                MyTextFieldwValidator(
                  title: 'First name: ',
                  controller: firstName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a first name';
                    }
                    return null;
                  },
                ),
                MyTextFieldwValidator(
                  title: 'Last Name',
                  controller: lastName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a last name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                const Text('Select Role: ',
                    style: TextStyle(fontWeight: FontWeight.w600)),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Obx(() => Checkbox(
                          value: isSignatory.value,
                          onChanged: (bool? value) {
                            isSignatory.value = value!;
                          },
                        )),
                    const Text('Is Signatory'),
                    const SizedBox(width: 5),
                    const Tooltip(
                      message:
                          'A signatory is a person authorized to approve financial transactions on behalf of the members.',
                      triggerMode: TooltipTriggerMode.tap,
                      child: Icon(Icons.info_outline, size: 18),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Obx(() => Wrap(
                      spacing: 8.0,
                      children: ["VIEWER", "ADMIN", "TREASURER", "SECRETARY"]
                          .map((String role) {
                        return ChoiceChip(
                          label: Text(role),
                          selected: selectedRole.value == role,
                          showCheckmark: true,
                          selectedColor:
                              ColorUtil.yellowOrange.withOpacity(0.85),
                          onSelected: (bool selected) {
                            if (selected) {
                              selectedRole.value = role;
                            }
                          },
                        );
                      }).toList(),
                    )),
              ],
            ),
          ),
        ),
        actions: <Widget>[
          MyButton(
            width: 120.w,
            outlined: true,
            label: 'Cancel',
            onClick: () => Navigator.of(context).pop(),
          ),
          Obx(
            () => MyButton(
              width: 150.w,
              showLoading: _controller.isAddingOperators.value,
              label: 'Invite',
              onClick: () async {
                if (formValid.currentState!.validate()) {
                  await _controller
                      .addOperator(
                          kittyId,
                          firstName.text,
                          lastName.text,
                          selectedRole.value,
                          phone,
                          isSignatory.value,
                          isAdminLocked.value)
                      .whenComplete(() {
                        // Clear text fields after successful operation
                        firstName.clear();
                        lastName.clear();
                        selectedRole.value = 'VIEWER';
                        isSignatory.value = false;
                        Navigator.pop(context);
                      });
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}

class EditOperator extends StatelessWidget {
  final OperatorsModel details;

  const EditOperator({super.key, required this.details});

  @override
  Widget build(BuildContext context) {
    final isAdminLocked = details.adminLocked.obs;
    final number = PhoneNumber(phoneNumber: details.whatsappNumber);
    final whatsappController = TextEditingController(
        text: details.whatsappNumber.length >= 9
            ? details.whatsappNumber
                .substring(details.whatsappNumber.length - 9)
            : details.whatsappNumber);

    final formValid = GlobalKey<FormState>();
    final firstName = TextEditingController(text: details.firstName);
    final lastName = TextEditingController(text: details.lastName);
    RxBool isSignatory = details.isSignatory.obs;
    final email = TextEditingController(text: details.email);
    RxString selectedRole = details.role.obs;
    String whatsappNumber = details.whatsappNumber;
    final _controller = Get.put(InvitePageController());
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(8),
        contentPadding: const EdgeInsets.all(8),
        title: Row(
          children: [
            const Text('Edit Member'),
            const Spacer(),
            details.adminLocked
                ? Container(
                    width: 100.w,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 6.0, vertical: 2.0),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                      ),
                      borderRadius: BorderRadius.circular(25),
                      color: Colors.transparent,
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.lock,
                            color: Theme.of(context).primaryColor, size: 12.sp),
                        const SizedBox(width: 4),
                        FittedBox(
                          child: Text(
                            'Admin Locked',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 10.spMin,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox()
          ],
        ),
        content: SingleChildScrollView(
          child: Form(
            key: formValid,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                MyTextFieldwValidator(
                  title: 'First name: ',
                  controller: firstName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a first name';
                    }
                    return null;
                  },
                ),
                MyTextFieldwValidator(
                  title: 'Last Name',
                  controller: lastName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a last name';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 8.h),
                const Text('Whatsapp Number',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                CustomInternationalPhoneInput(
                  textFieldController: whatsappController,
                  onInputChanged: (PhoneNumber number) {
                    whatsappNumber = number.phoneNumber.toString().replaceAll("+", '');
                    print(whatsappNumber);
                  },
                  onInputValidated: (bool value) {},
                  ignoreBlank: false,
                  initialValue: PhoneNumber(
                      isoCode: number.isoCode ?? 'KE',
                      dialCode: number.dialCode ?? "+254"),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Phone Number is required';
                    }
                    return null;
                  },
                  formatInput: true,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                ),
                MyTextFieldwValidator(
                  title: 'email',
                  controller: email,
                ),
                const SizedBox(height: 20),
                details.adminLocked
                    ? const SizedBox()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Select Role: ',
                              style: TextStyle(fontWeight: FontWeight.w600)),
                          Row(
                            children: [
                              Obx(() => Checkbox(
                                    value: isSignatory.value,
                                    onChanged: (bool? value) {
                                      isSignatory.value = value!;
                                    },
                                  )),
                              const Text('Is Signatory'),
                              const SizedBox(width: 5),
                              const Tooltip(
                                message:
                                    'A signatory is a person authorized to approve financial transactions on behalf of the members.',
                                triggerMode: TooltipTriggerMode.tap,
                                child: Icon(Icons.info_outline, size: 18),
                              ),
                            ],
                          ),
                          SizedBox(height: 8.sp),
                        ],
                      ),
                Obx(() => Wrap(
                      spacing: 8.0,
                      children: ["VIEWER", "ADMIN", "TREASURER", "SECRETARY"]
                          .map((String role) {
                        return ChoiceChip(
                          label: Text(role),
                          selected: selectedRole.value == role,
                          showCheckmark: true,
                          selectedColor:
                              ColorUtil.yellowOrange.withOpacity(0.8),
                          onSelected: (bool selected) {
                            if (selected) {
                              selectedRole.value = role;
                            }
                          },
                        );
                      }).toList(),
                    )),
                details.adminLocked
                    ? const SizedBox()
                    : Obx(
                        () => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Divider(),
                            MyButton(
                              onClick: () {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AlertDialog(
                                      title: const Text('Admin Lock'),
                                      content: isAdminLocked.value
                                          ? const Text(
                                              'Once you lock an admin, they cannot be modified or removed without contacting customer support. This is a security feature to prevent unauthorized changes by admin accounts.')
                                          : const Text(
                                              'Once you lock an admin, they cannot be modified or removed without contacting customer support. This is a security feature to prevent unauthorized changes by admin accounts. Are you sure you want to proceed?'),
                                      actions: [
                                        if (isAdminLocked.value)
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.of(context).pop(),
                                            child: const Text('Close'),
                                          ),
                                        if (!isAdminLocked.value)
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.of(context).pop(),
                                            child: const Text('Cancel'),
                                          ),
                                        if (!isAdminLocked.value)
                                          TextButton(
                                            onPressed: () {
                                              isAdminLocked.value = true;
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('Lock Admin'),
                                          ),
                                      ],
                                    );
                                  },
                                );
                              },
                              label: 'Admin Locked',
                              outlined: !isAdminLocked.value,
                              width: 220.w,
                              icon: isAdminLocked.value
                                  ? Icons.lock
                                  : Icons.lock_open,
                            ),
                            const Divider(),
                          ],
                        ),
                      ),
              ],
            ),
          ),
        ),
        actions: <Widget>[
          MyButton(
            width: 100.w,
            outlined: true,
            label: 'Cancel',
            onClick: () => Navigator.of(context).pop(),
          ),
          Obx(
            () => MyButton(
              width: 163.w,
              showLoading: _controller.isUpdatingOperator.value,
              label: 'Update',
              onClick: () async {
                if (formValid.currentState!.validate()) {
                  await _controller.updateOperator({
                    "ID": details.id,
                    "kitty_id": details.kittyId,
                    "first_name": firstName.text,
                    "last_name": lastName.text,
                    "role": selectedRole.value,
                    "user_id": null,
                    "is_signatory": isSignatory.value,
                    "status": "ACTIVE",
                    // "phone_number": details.phon,
                    "whatsapp_number": whatsappNumber,
                    "email": email.text,
                    "is_admin_locked": isAdminLocked.value,
                  }, details.kittyId).whenComplete(() {
                    // Clear text fields after successful operation
                    firstName.clear();
                    lastName.clear();
                    email.clear();
                    whatsappController.clear();
                    selectedRole.value = 'VIEWER';
                    isSignatory.value = false;
                    Navigator.pop(context);
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}


