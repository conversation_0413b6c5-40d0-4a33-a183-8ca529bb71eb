// To parse this JSON data, do
//
//     final enums = enumsFromJson(jsonString);

import 'dart:convert';

Enums enumsFromJson(String str) => Enums.fromJson(json.decode(str));

String enumsToJson(Enums data) => json.encode(data.toJson());

class Enums {
  final List<String> eventPaymentModes;
  final List<String> eventStatus;
  final List<String> eventTypes;
  final List<String> ticketStatus;
  final List<String> ticketType;

  Enums({
    List<String>? eventPaymentModes,
    List<String>? eventStatus,
    List<String>? eventTypes,
    List<String>? ticketStatus,
    List<String>? ticketType,
  })  : eventPaymentModes = eventPaymentModes ?? [''],
        eventStatus = eventStatus ?? [''],
        eventTypes = eventTypes ?? [''],
        ticketStatus = ticketStatus ?? [''],
        ticketType = ticketType ?? [''];

  factory Enums.fromJson(Map<String, dynamic> json) => Enums(
        eventPaymentModes: json["event_payment_modes"] != null
            ? List<String>.from(json["event_payment_modes"].map((x) => x))
            : [''],
        eventStatus: json["event_status"] != null
            ? List<String>.from(json["event_status"].map((x) => x))
            : [''],
        eventTypes: json["event_types"] != null
            ? List<String>.from(json["event_types"].map((x) => x))
            : [''],
        ticketStatus: json["ticket_status"] != null
            ? List<String>.from(json["ticket_status"].map((x) => x))
            : [''],
        ticketType: json["ticket_type"] != null
            ? List<String>.from(json["ticket_type"].map((x) => x))
            : [''],
      );

  Map<String, dynamic> toJson() => {
        "event_payment_modes":
            List<dynamic>.from(eventPaymentModes.map((x) => x)),
        "event_status": List<dynamic>.from(eventStatus.map((x) => x)),
        "event_types": List<dynamic>.from(eventTypes.map((x) => x)),
        "ticket_status": List<dynamic>.from(ticketStatus.map((x) => x)),
        "ticket_type": List<dynamic>.from(ticketType.map((x) => x)),
      };
}
