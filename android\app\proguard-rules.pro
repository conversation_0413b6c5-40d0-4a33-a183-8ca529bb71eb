
-keep class io.grpc.** {*;}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepattributes AutoValue

-keep class com.google.firebase.installations.** {
  *;
}

-keep interface com.google.firebase.installations.** {
  *;
}

-keep class com.google.firebase.** { *; }

# Keep ML Kit text recognition classes
-keep class com.google.mlkit.vision.text.** { *; }
-keep class com.google.mlkit.vision.text.chinese.** { *; }
-keep class com.google.mlkit.vision.text.devanagari.** { *; }
-keep class com.google.mlkit.vision.text.japanese.** { *; }
-keep class com.google.mlkit.vision.text.korean.** { *; }
-keep class com.google.android.gms.internal.mlkit_linkfirebase.** { *; }
-keep class com.google.mlkit.common.** { *; }
-keep class com.google.mlkit.linkfirebase.** { *; }