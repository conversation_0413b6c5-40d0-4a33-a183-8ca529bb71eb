// To parse this JSON data, do
//
//     final chamaTextTransactions = chamaTextTransactionsFromJson(jsonString);

import 'dart:convert';

ChamaTextTransactions chamaTextTransactionsFromJson(String str) => ChamaTextTransactions.fromJson(json.decode(str));

String chamaTextTransactionsToJson(ChamaTextTransactions data) => json.encode(data.toJson());

class ChamaTextTransactions {
    DateTime? startDate;
    String? action;
    int? chamaId;

    ChamaTextTransactions({
        this.startDate,
        this.action,
        this.chamaId,
    });

    factory ChamaTextTransactions.fromJson(Map<String, dynamic> json) => ChamaTextTransactions(
        startDate: DateTime.parse(json["start_date"]),
        action: json["action"],
        chamaId: json["chama_id"],
    );

    Map<String, dynamic> toJson() => {
        "start_date": startDate?.toIso8601String(),
        "action": action,
        "chama_id": chamaId,
    };
}
