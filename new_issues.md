- [x] Send Statement/Generate Statement (change the name)
          - tooltip to tell the user that it will send to whatsapp/email,
          - remove mask number option
          - change statement type to document type
          - appear first on the export option list
          - align the elements on the export option list
          - can send either to whatsapp or email (if null send as empty string)
- [x] Always Go Back after create/edit (if successful)
- [x] Check the Edit Transaction (endpoint and payload) - only admin
          [x]  - it should appear on my_transaction/kitty_transaction
          [x]  - check the product (deny if its chama or event) 
          [x] - prefill the dialog with the existing data
          [x]  - add option to edit payment ref (eg. employee id) show info(tooltip)
          [x]  - only for type in
- [x] use international phone number in Delegates (everywhere else ) - remove hardcoded 254
- [x] Kitty Filter 
         - remove phone number
         - Active/Completed/Draft (send their ids)
         - merge search with kitty_id and identify either as int or string
 - [x] Error in uploading banner
 - [x] Error in uploading kitty Image
 - [ ] Fix Native Splash screen for android 12 + 
 - [x] when creating a ticket send price as null if kitty type == Free (hide the price input)
 - [x] handle regex on website (after creating an event)
 - [x] Change the Signatory label to `Signatory Approvals`
 - [x] The Settings tab on kitty Edit Settigs page
 - [x] Remove the hardcoded KSH or chama transfer confirmation dialog
 - [..] Update the chama balance on the view chama details to display the correct amount
 - [x] on the confirmation dialog show the channel (Mpesa Wallet , Bank_name ...) 
 - [x] Check the Kitty DeepLink Page - Add a report action , percentage indicator , correct kitty details
 - [x] Add multiselect kitty categories functionality. - with search option (make the target input optional) - max should be three categories
 - [..] Event Statistics
 - [..] Refactoring 