import 'package:flutter/material.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/utils/size_config.dart';

class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        // height: SizeConfig.screenHeight * .4,
        // width: SizeConfig.screenWidth * .8,
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: getProportionalScreenWidth(10),
              vertical: getProportionalScreenHeight(10)),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 10),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Prominent Disclosure",
                    style: context.titleText,
                  ),
                  const Text(
                      "Thank you for choosing OneKitty. We are committed to protecting your privacy and ensuring the security of your personal information. In order to provide you with the best possible experience and to fulfill the functionalities of our app, we collect certain information from you. Below is a summary of the information we collect and how it is used:"),
                  Text(
                    "1. Contact Information:",
                    style: context.titleText,
                  ),
                  const Text(
                      "We may request access to your contacts in order to provide features such as inviting friends to use the app or facilitating communication between users. Your contacts will not be stored or used for any other purpose without your explicit consent."),
                  Text(
                    "2. Biometric Data:",
                    style: context.titleText,
                  ),
                  const Text(
                      "Our app may utilize biometric authentication methods such as fingerprint or facial recognition for login purposes. This data is securely stored on your device and is not transmitted to our servers."),
                  Text(
                    "3. Email and Password:",
                    style: context.titleText,
                  ),
                  const Text(
                      "If you choose to create an account with us, we will collect your email address and password for authentication and account management purposes. Your password is encrypted and stored securely."),
                  Text(
                    "4. OTP (One-Time Password):",
                    style: context.titleText,
                  ),
                  const Text(
                      "In order to verify your identity and enhance the security of your account, we may send OTPs to your messages (SMS). These OTPs are used for login or transaction verification purposes only and are not stored beyond their immediate use."),
                  Text(
                    "5. Payment Information:",
                    style: context.titleText,
                  ),
                  const Text(
                      "When you use our payment gateway to send or receive money, we collect necessary payment information such as credit/debit card details or bank account information. This data is securely encrypted and processed by our trusted payment partners. We do not store your payment information on our servers."),
                  const SizedBox(
                    height: 10,
                  ),
                  const Text(
                      "We are committed to safeguarding your personal information and ensuring its confidentiality. We do not sell, trade, or otherwise transfer your information to third parties without your consent, except as required by law or as necessary to provide our services."),
                  const Text(
                      "By using our app, you consent to the collection and use of your information as outlined in this disclosure. If you have any questions or concerns about our privacy practices, please contact us at:"),
                  RichText(
                      text: TextSpan(children: [
                    TextSpan(text: "Tel:", style: context.dividerTextSmall),
                    TextSpan(
                        text: " +254 733550051",
                        style: context.dividerTextSmall
                            ?.copyWith(color: AppColors.blueButtonColor)),
                  ])),
                  RichText(
                      text: TextSpan(children: [
                    TextSpan(text: "Email:", style: context.dividerTextSmall),
                    TextSpan(
                        text: " <EMAIL>",
                        style: context.dividerTextSmall
                            ?.copyWith(color: AppColors.blueButtonColor)),
                  ])),
                  RichText(
                      text: TextSpan(children: [
                    TextSpan(text: "Website:", style: context.dividerTextSmall),
                    TextSpan(
                        text: " www.onekitty.co.ke",
                        style: context.dividerTextSmall
                            ?.copyWith(color: AppColors.blueButtonColor)),
                  ])),
                  // Text(
                  //     "Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke"),
                  const Text("Thank you for your trust in OneKitty"),
                  const SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
