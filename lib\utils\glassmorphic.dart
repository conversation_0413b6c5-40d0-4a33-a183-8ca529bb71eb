import 'dart:ui';
import 'package:flutter/material.dart';

class GlassmorphicContainer extends StatelessWidget {
  final Widget? child;
  final double? width,
      height,
      cornerRadius,
      padding,
      margin,
      blurRadius,
      colorIntensity,
      background;
  final bool? showBorder, animated;
  final Color? borderColor, color;
  final Duration? duration;
  final Function()? onTap;

  const GlassmorphicContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.cornerRadius,
    this.padding,
    this.showBorder,
    this.borderColor,
    this.margin,
    this.blurRadius,
    this.color,
    this.colorIntensity,
    this.background,
    this.animated,
    this.duration,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          if (background == 3)
            AnimatedContainer(
              duration: animated ?? false
                  ? duration ?? const Duration(milliseconds: 500)
                  : Duration.zero,
              width: width, //?? 200,
              height: height,
              margin: EdgeInsets.all(margin ?? 4),

              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cornerRadius ?? 10),
                  gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: [
                        // Colors.blue.withOpacity(0.5),
                        Colors.blue.withOpacity(0.3),

                        // Colors.transparent, Colors.transparent,
                        // Colors.transparent,
                        Colors.transparent,
                        Colors.transparent,
                        Colors.red.withOpacity(0.1),
                        // Colors.deepPurpleAccent
                      ])),
            ),
          if (background == 3)
            AnimatedContainer(
              duration: animated ?? false
                  ? duration ?? const Duration(milliseconds: 500)
                  : Duration.zero,
              width: width, //?? 200,
              height: height,
              margin: EdgeInsets.all(margin ?? 4),

              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cornerRadius ?? 10),
                  gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: [
                        // Colors.blue.withOpacity(0.5),
                        Colors.blue.withOpacity(0.3),

                        // Colors.transparent, Colors.transparent,
                        // Colors.transparent,
                        Colors.transparent,
                        Colors.transparent,
                        Colors.red.withOpacity(0.1),
                        // Colors.deepPurpleAccent
                      ])),
            ),
          if (background == 2)
            AnimatedContainer(
              duration: animated ?? false
                  ? duration ?? const Duration(milliseconds: 500)
                  : Duration.zero,
              width: width, //?? 200,
              height: height,
              margin: EdgeInsets.all(margin ?? 4),

              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cornerRadius ?? 10),
                  gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),

                        Colors.transparent, Colors.transparent,
                        Colors.transparent,
                        Colors.transparent,
                        Colors.transparent,
                        Colors.white.withOpacity(0.09),
                        // Colors.deepPurpleAccent
                      ])),
            ),
          if (background == 1)
            AnimatedContainer(
              duration: animated ?? false
                  ? duration ?? const Duration(milliseconds: 500)
                  : Duration.zero,
              width: width, //?? 200,
              height: height,
              margin: EdgeInsets.all(margin ?? 4),

              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cornerRadius ?? 10),
                  gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.deepPurple.withOpacity(0.3),
                        Colors.deepPurple.withOpacity(0.2),
                        Colors.deepPurple.withOpacity(0.1),

                        Colors.transparent, Colors.transparent,
                        Colors.transparent,
                        // Colors.deepPurpleAccent
                      ])),
            ),
          AnimatedContainer(
            duration: animated ?? false
                ? duration ?? const Duration(milliseconds: 500)
                : Duration.zero,
            width: width, //?? 200,
            height: height,
            margin: EdgeInsets.all(margin ?? 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(colorIntensity ?? 0.01),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 32.0,
                  offset: Offset(0, 0),
                ),
              ],
              borderRadius: BorderRadius.circular(cornerRadius ?? 10.0),
              border: showBorder ?? false
                  ? Border.all(
                      color: borderColor ?? Colors.white.withOpacity(0.18),
                      width: 1.0,
                    )
                  : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(cornerRadius ?? 10.0),
              child: BackdropFilter(
                filter: ImageFilter.blur(
                    sigmaX: blurRadius ?? 8, sigmaY: blurRadius ?? 8),
                child: AnimatedContainer(
                  duration: animated ?? false
                      ? const Duration(seconds: 0)
                      : Duration.zero,
                  padding: EdgeInsets.all(padding ?? 8),
                  color: Colors.white.withOpacity(0.0),
                  child: child,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
