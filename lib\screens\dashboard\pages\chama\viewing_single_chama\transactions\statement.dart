import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/pdf_statement.dart';
import 'package:printing/printing.dart';

// ignore: must_be_immutable
class SingleChamaStatementPage extends StatelessWidget {
  SingleChamaStatementPage(
      {super.key,
      
       this.transaction,
      this.chama});
  UserChama? chama;
  TransactionModel? transaction;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Statement'),
      ),
      body: PdfPreview(
        shareActionExtraSubject: "${"onekitty"} transactions statement",
        pdfFileName:
             "onekitty_transactions_statement.pdf",
            
        build: (context) => 
            makeChamaSinglePdf(transaction ?? TransactionModel())
            
      ),
    );
  }
}

// ignore: must_be_immutable
class ChamaStatementPage extends StatelessWidget {
  ChamaStatementPage(
      {super.key,
      this.transactions,
      this.chama});
  List<TransactionModel>? transactions = [];
  UserChama? chama;
  final ChamaDataController dataController = Get.find<ChamaDataController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Statement'),
      ),
      body: PdfPreview(
        shareActionExtraSubject:
            "${dataController.chama.value.chama?.title ?? "onekitty"} transactions statement",
        pdfFileName: 
            "${dataController.chama.value.chama?.title ?? ""}_transactions_statement.pdf",
            
        build: (context) => 
            makeChamaFullContributionsPdf(transactions ?? [], chama)
           
      ),
    );
  }
}