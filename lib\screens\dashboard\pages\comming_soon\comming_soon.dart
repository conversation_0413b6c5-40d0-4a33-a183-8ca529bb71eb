import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart'; 
import 'package:get/get.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_button.dart';

class CommingSoon extends StatefulWidget {
  const CommingSoon({super.key});

  @override
  State<CommingSoon> createState() => _CommingSoonState();
}

class _CommingSoonState extends State<CommingSoon> {
  UserKittyController userKittyController = Get.put(UserKittyController());
  final controller = FlipCardController();
  final bool debugMode = true;
  DateTime now = DateTime.now();
  DateTime dDay = DateTime(2024, 4, 26, 0, 0, 0);
  @override
  Widget build(BuildContext context) {
    dDay = (debugMode)
        ? DateTime(now.year, now.month + 2, now.day, now.hour, now.minute,
            now.second + 10)
        : dDay;
    return Scaffold(
        extendBody: true,
        body: Container(
          height: double.infinity,
          decoration: const BoxDecoration(
              gradient: LinearGradient(colors: [
            AppColors.neutralDark,
            AppColors.blueButtonColor,
            AppColors.mainPurple,
            AppColors.neutral,
          ], begin: Alignment.topRight, end: Alignment.bottomLeft)),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Hello ${userKittyController.getLocalUser()?.firstName}",
                            style: context.titleText
                                ?.copyWith(color: Colors.white),
                          ),
                          Text(
                            "We are launching soon",
                            style: context.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 30),
                          )
                              .animate(
                                onComplete: (controller) => controller.repeat(),
                              )
                              .shimmer(
                                  duration: const Duration(milliseconds: 2000),
                                  delay: const Duration(milliseconds: 1000),
                                  color: AppColors.blueButtonColor),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      // FlipClock.reverseCountdown(
                      //   height: 40.0,
                      //   width: 40.0,
                      //   duration: _duration,
                      //   digitColor: Colors.white,
                      //   backgroundColor: AppColors.stackBlue,
                      //   digitSize: 30.0,
                      //   borderRadius:
                      //       const BorderRadius.all(Radius.circular(3.0)),
                      //   //onDone: () => print('ih'),
                      // ),
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: AppColors.stackBlue),
                        child: InkWell(
                          onTap: () => controller.flipCard(),
                          child: FlipCardWidget(
                              controller: controller,
                              front: Image.asset(AssetUrl.frontImg),
                              back: Image.asset(AssetUrl.backImg)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      CustomKtButton(
                          onPress: () => controller.flipCard(),
                          btnText: "Click me to flip")
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
 
  }
}

class FlipCardController {
  FlipCardWidgetState? _state;
  Future flipCard() async => _state?.flipCard();
}

class FlipCardWidget extends StatefulWidget {
  final FlipCardController controller;
  final Image front;
  final Image back;
  const FlipCardWidget(
      {super.key,
      required this.front,
      required this.back,
      required this.controller});

  @override
  State<FlipCardWidget> createState() => FlipCardWidgetState();
}

class FlipCardWidgetState extends State<FlipCardWidget>
    with TickerProviderStateMixin {
  late AnimationController controller;
  bool isFront = true;
  double anglePlus = 0;
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      precacheImage(widget.front.image, context);
      precacheImage(widget.back.image, context);
    });
    controller = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    widget.controller._state = this;
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  Future flipCard() async {
    if (controller.isAnimating) return;
    isFront = !isFront;
    await controller.forward(from: 0).then((value) => anglePlus = pi);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          double angle = controller.value * -pi;
          if (isFront) angle += anglePlus;
          final transform = Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(angle);
          return isFrontImg(angle.abs())
              ? widget.front
              : Transform(
                  transform: transform,
                  alignment: Alignment.center,
                  child: Transform(
                    transform: Matrix4.identity()..rotateY(pi),
                    alignment: Alignment.center,
                    child: widget.back,
                  ),
                );
        });
  }

  bool isFrontImg(double angle) {
    const degrees90 = pi / 2;
    const degrees270 = 3 * pi / 2;
    return angle <= degrees90 || angle >= degrees270;
  }
}
