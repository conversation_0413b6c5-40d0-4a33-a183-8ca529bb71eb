import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';

import '../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class ContibutionErrorScreen extends StatelessWidget {
  final      contributeController = Get.put(ContributeController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  ContibutionErrorScreen({
    super.key,
  });
  // bool isRichText;
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: _buildAppBar(context),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(children: [
            const RowAppBar(),
            Text(
                contributeController.kittGoten.value.title ??
                    chamaDataController.chama.value.chama?.title ??
                    "",
                style: theme.textTheme.titleLarge),
            SizedBox(height: 8.h),
            // isRichText
            //     ?
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                width: 343.w,
                margin: EdgeInsets.only(right: 24.w),
                child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                            text:
                                "Every shilling counts; make a positive impact on ",
                            style: CustomTextStyles.bodySmallff545963),
                        TextSpan(
                            text: contributeController.kittGoten.value.title ??
                                chamaDataController.chama.value.chama?.title ??
                                "",
                            style: CustomTextStyles.labelLargeff545963)
                      ],
                    ),
                    textAlign: TextAlign.center),
              ),
            ),
            // : SizedBox(),
            SizedBox(height: 30.h),
            SizedBox(
              height: 360.h,
              width: 339.w,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  CustomImageView(
                      imagePath: AssetUrl.errorState,
                      height: 300.h,
                      width: 275.w,
                      alignment: Alignment.topCenter),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(contributeController.resContrData["status"],
                            style: theme.textTheme.titleLarge),
                        SizedBox(height: 2.h),
                        SizedBox(
                          width: 339.w,
                          child: Text(
                              contributeController
                                      .resContrData["result_desc"] ??
                                  contributeController
                                      .resContrData["response_description"],
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                              style: CustomTextStyles.bodyMediumff545963),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),
            contributeController.status.value == "PROCESSING"
                ? const Text("")
                : CustomElevatedButton(
                    onPressed: () {
                      onTapArrowLeft(context);
                    },
                    width: 85.w,
                    text: "Retry"),
            SizedBox(height: 5.h)
          ]),
        ),
      ),
    );
  }

  /// Section Widget

  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
