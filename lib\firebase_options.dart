// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCRY5TrzLBjnKhL6uBaRqscrsgQmqdf6_E',
    appId: '1:470354413031:web:e33b18a7f57803160fe942',
    messagingSenderId: '470354413031',
    projectId: 'onekitty-345a1',
    authDomain: 'onekitty-345a1.firebaseapp.com',
    storageBucket: 'onekitty-345a1.appspot.com',
    measurementId: 'G-KVK6GQ1WFK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCR1MbIRF1cc8g8p5y5g16NQ2NT97DvzUQ',
    appId: '1:470354413031:android:b4dd8239bf014bf10fe942',
    messagingSenderId: '470354413031',
    projectId: 'onekitty-345a1',
    storageBucket: 'onekitty-345a1.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAg60GDwCrWJQkS15stCAut5yMSA59zwpQ',
    appId: '1:470354413031:ios:532888773df55b0d0fe942',
    messagingSenderId: '470354413031',
    projectId: 'onekitty-345a1',
    storageBucket: 'onekitty-345a1.appspot.com',
    iosBundleId: 'com.example.onekittyMobile',
  );
}
