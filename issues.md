# Project issues:

## OVERALL [kitty, chama, events] 
critical [ ] : WhatsApp URL Regex Update
- Current regex pattern `r'^https:\/\/chat\.whatsapp\.com\/[A-Za-z0-9_-]{22}$'` needs to be updated
- Should handle various WhatsApp link formats including invite links and new formats
- Update needed in:
  - Kitty whatsapp_link.dart
  - Event organizer view
  - Chama group management
  - All other WhatsApp integration points
- Must maintain backward compatibility with existing links

critical [x] PDF Export Null Value Fix
- Issue in transactions export to PDF functionality
- Null check operator used on null value in statement generation
- Affects all transaction exports (kitty, chama, events)
- Check transaction data model completeness before PDF generation
- Implement proper null safety checks in pdf_statement.dart
- FIXED: Added comprehensive null safety checks across all PDF generation functions
- FIXED: Replaced null check operators (!) with proper null validation
- FIXED: Added fallback values for null transaction properties (id, createdAt, amount, etc.)
- FIXED: Fixed logical errors in email condition checks (changed OR to AND)
- FIXED: Updated both contribution and chama PDF statement files


 
[ ] WhatsApp Transaction Statements
- Implement feature to request event transaction statements via WhatsApp
- Add endpoint for WhatsApp-based statement requests
- Include transaction summary and detailed report options
- Integrate with existing WhatsApp notification system

[ ] Event Statistics Dashboard
- Implement visualization dashboard in view_single_event_organizer.dart
- Use endpoint event/statistics/
- Show metrics:
  - Ticket sales by category
  - Revenue statistics
  - Attendance tracking
  - Sales trends over time

[ ] Media Upload Validation
- Add upload progress tracking
- Disable submit button until all media uploads complete
- Implement proper error handling for failed uploads
- Add validation for media URL fields

[ ] Media URL Validation
- Add checks for empty media URLs
- Implement URL validation before submission
- Handle social media links validation
- Update ADDSOCIALMEDIA endpoint handling

## KYC 
critical [ ] Selfie Upload Process
- Debug and fix selfie upload functionality
- Review entire KYC flow for potential issues
- Check API integration in updateKYC endpoint
- Validate image upload process
- Ensure proper error handling and user feedback
- Test on different devices and network conditions 