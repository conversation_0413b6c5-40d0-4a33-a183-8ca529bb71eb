import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/utils/my_text_field.dart';

class DatePicker extends StatefulWidget {
  final TextEditingController date;
  final TextEditingController time;
  final bool isAllow;

  const DatePicker({
    super.key,
    required this.date,
    required this.time,
    required this.isAllow,
  });

  @override
  State<DatePicker> createState() => _DatePickerState();
}

class _DatePickerState extends State<DatePicker> {
  late DateTime selectedDate;
  late TimeOfDay selectedTime;
  final TextEditingController _mergedController = TextEditingController();

  @override
  void initState() {
    super.initState();
    selectedDate = DateTime.now();
    selectedTime = TimeOfDay.now();
  }

  Future<void> _selectDateTime(BuildContext context) async {
    // First, pick the date
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      selectableDayPredicate: widget.isAllow
          ? (day) {
              return day.isAfter(DateTime.now().subtract(const Duration(days: 1)));
            }
          : null,
    );

    if (pickedDate != null) {
      // If date is picked, immediately show time picker
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: selectedTime,
      );

      if (pickedTime != null) {
        setState(() {
          // Update selected date and time
          selectedDate = pickedDate;
          selectedTime = pickedTime;

          // Update individual controllers
          widget.date.text = DateFormat('yyyy-MM-dd').format(selectedDate);
          widget.time.text = selectedTime.format(context);

          // Merge date and time in the merged controller
          _mergedController.text = 
            '${DateFormat('yyyy-MM-dd').format(selectedDate)} ${selectedTime.format(context)}';
        });
      } else {
        if (mounted) {
          ToastUtils.showErrorToast(context, "You didn't pick any time", "Oops!");
        }
      }
    } else {
      if (mounted) {
        ToastUtils.showErrorToast(context, "You didn't pick a date", "Oops!");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _mergedController,
      onTap: () => _selectDateTime(context),
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'Select Date and Time',
        hintText: DateFormat().add_yMd().add_jm().format(DateTime.now()),
        suffixIcon: IconButton(
          onPressed: () => _selectDateTime(context),
          icon: const Icon(Icons.calendar_month),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Date and Time can't be empty";
        }
        return null;
      },
    );
  }

  @override
  void dispose() {
    _mergedController.dispose();
    super.dispose();
  }
}
class DateOnlyPicker extends StatefulWidget {
  final TextEditingController dateController;
  final bool isAllow;
  const DateOnlyPicker(
      {super.key, required this.dateController, required this.isAllow});

  @override
  State<DateOnlyPicker> createState() => _DateOnlyPickerState();
}

class _DateOnlyPickerState extends State<DateOnlyPicker> {
  Future<void> selectDate(
    BuildContext context,
  ) async {
    final DateTime initialDate = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1920),
      lastDate: DateTime(2100),
      selectableDayPredicate: widget.isAllow
          ? (day) {
              return day
                  .isAfter(DateTime.now().subtract(const Duration(days: 1)));
            }
          : null,
    );

    if (picked != null) {
      widget.dateController.text = DateFormat('yyyy-MM-dd').format(picked);
    } else {
      if (context.mounted) {
        // Display toast or handle error
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("You didn't pick a date")),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onTap: () => selectDate(context),
      readOnly: true,
      controller: widget.dateController,
      decoration: InputDecoration(
        labelText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        suffixIcon: IconButton(
          onPressed: () => selectDate(context),
          icon: const Icon(Icons.calendar_month),
        ),
      ),
      validator: (value) {
        if (value!.isEmpty) {
          return "Date can't be empty";
        }
        return null;
      },
    );
  }
}

class TimePicker extends StatefulWidget {
  final TextEditingController time;

  const TimePicker({super.key, required this.time});

  @override
  State<TimePicker> createState() => _TimePickerState();
}

class _TimePickerState extends State<TimePicker> {
  late TimeOfDay selectedTime;

  @override
  void initState() {
    super.initState();
    selectedTime = TimeOfDay.now();
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime,
    );
    if (picked != null && picked != selectedTime) {
      setState(() {
        selectedTime = picked;
        widget.time.text = selectedTime.format(context);
      });
    } else {
      if (mounted) {
        ToastUtils.showErrorToast(context, "You didn't pick any time", "Oops!");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () {
              _selectTime;
            },
            child: TextFormField(
              readOnly: true,
              controller: widget.time,
              decoration: InputDecoration(
                labelText: "Choose Time",
                suffixIcon: IconButton(
                  onPressed: () => _selectTime(context),
                  icon: const Icon(Icons.access_time),
                ),
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return "Time can't be empty";
                }
                return null;
              },
            ),
          ),
        ),
      ],
    );
  }
}

// ignore: must_be_immutable
class DatePick extends StatefulWidget {
  final TextEditingController date;
  final TextEditingController time;
  DateTime combinedDateTime;
  DatePick({
    super.key,
    required this.date,
    required this.time,
    required this.combinedDateTime, // Pass the callback function
  });

  @override
  _DatePickState createState() => _DatePickState();
}

class _DatePickState extends State<DatePick> {
  DateTime selectedDate = DateTime.now();
  TimeOfDay selectedTime = TimeOfDay.now();

  Future<void> _selectDateAndTime(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null && pickedDate != selectedDate) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: selectedTime,
      );

      if (pickedTime != null) {
        setState(() {
          selectedDate = pickedDate;
          selectedTime = pickedTime;
          widget.combinedDateTime = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            selectedTime.hour,
            selectedTime.minute,
          );

          // Invoke callback function with combined date and time
          print("From function ${widget.combinedDateTime}");
        });
        widget.date.text = DateFormat.yMd().format(selectedDate);
        widget.time.text = selectedTime.format(context);

        // Calculate combined date and time
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            onTap: () {
              _selectDateAndTime(context);
            },
            readOnly: true,
            controller: widget.date,
            decoration: InputDecoration(
              fillColor: Colors.blueAccent.withOpacity(0.1),
              labelText: "Date",
              suffixIcon: IconButton(
                onPressed: () => _selectDateAndTime(context),
                icon: const Icon(Icons.calendar_month),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Date can't be empty";
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            readOnly: true,
            controller: widget.time,
            onTap: () async {
              final TimeOfDay? pickedTime = await showTimePicker(
                context: context,
                initialTime: selectedTime,
              );

              if (pickedTime != null) {
                setState(() {
                  selectedTime = pickedTime;
                  widget.combinedDateTime = DateTime(
                    selectedDate.year,
                    selectedDate.month,
                    selectedDate.day,
                    selectedTime.hour,
                    selectedTime.minute,
                  );

                  // Invoke callback function with combined date and time
                  print("From function ${widget.combinedDateTime}");
                });
                widget.date.text = DateFormat.yMd().format(selectedDate);
                widget.time.text = selectedTime.format(context);

                // Calculate combined date and time
              }
            },
            decoration: InputDecoration(
              fillColor: Colors.blueAccent.withOpacity(0.1),
              suffixIcon: IconButton(
                onPressed: () {},
                icon: const Icon(Icons.timer_outlined),
              ),
              labelText: "Time",
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Time can't be empty";
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}



class DateTimePickerMerged extends StatelessWidget {
  final TextEditingController date;
  final TextEditingController time;
  final bool isAllow;
  final TextEditingController controller;
  final String? title;
  const DateTimePickerMerged({super.key, required this.date, required this.time, required this.isAllow, required this.controller,  this.title});

  @override
  Widget build(BuildContext context) {
    return    MyTextFieldwValidator(
              readOnly: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '$title is required';
                }
                return null;
              },
              controller: controller,
              titleStyle:
                  TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
              iconSuffix: IconButton(
                icon: const Icon(Icons.calendar_month),
                onPressed: () async {
                  DateTime? pickedDateTime = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );

                  if (pickedDateTime != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                    );

                    if (pickedTime != null) {
                      DateTime finalDateTime = DateTime(
                        pickedDateTime.year,
                        pickedDateTime.month,
                        pickedDateTime.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );

                      String formattedDateTime =
                          DateFormat('dd/MM/yyyy HH : mm a')
                              .format(finalDateTime);
                      time.text = formattedDateTime;
                    }
                  }
                },
              ),
              title: title,
              hint: '13/2/2024 2:00 PM');
       
  }
}