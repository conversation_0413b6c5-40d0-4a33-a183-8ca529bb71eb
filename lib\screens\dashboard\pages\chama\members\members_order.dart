import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:date_time_format/date_time_format.dart';

import '../../../../../utils/utils_exports.dart';

class MembersOrder extends StatefulWidget {
  const MembersOrder({super.key});

  @override
  State<MembersOrder> createState() => _MembersOrderState();
}

class _MembersOrderState extends State<MembersOrder> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  TextEditingController phoneController = TextEditingController();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');

  String invitePhone = "";
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar:buildAppBar(context),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.all(10.w),
            child: Column(
              children: [
                const RowAppBar(),
                Text(chamaDataController.chama.value.chama?.title ?? "",
                    style: CustomTextStyles.titleMediumBlack900),
                // Text("${chamaDataController.chama.value.membersCount} Members",
                // style: theme.textTheme.titleLarge),
                GetBuilder(
                  builder: (ChamaController chamaController) {
                    if (chamaController.isloadingChama.isTrue) {
                      return const Text("checking");
                    }

                    return Text("${chamaController.OData.value.total} Members ",
                        style: theme.textTheme.titleLarge);
                  },
                ),

                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Chama Members",
                    style: CustomTextStyles.titleMediumBlack900,
                  ),
                ),
                SizedBox(height: 5.h),
                Text(
                  "Hold and Drag the member to set their contribution receiving order",
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: theme.textTheme.bodyLarge!.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontStyle: FontStyle.italic),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton(
                    onPressed: () {
                      chamaController.shuffleChamaMembers();
                    },
                    child: const Text(
                      'Shuffle Members',
                    ),
                  ),
                ),
                SizedBox(height: 5.h),
                buildChamaMembers(context),
              ],
            ),
          ),
        ),
        floatingActionButton: Obx(
          () => CustomKtButton(
            isLoading: chamaController.isSetting.isTrue,
            width: 90.w,
            height: 30.h,
            btnText: "Save Order",
            onPress: () async {
              await _submitOrder();
            },
            alignment: Alignment.bottomRight,
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  Widget buildChamaMembers(BuildContext context) {
    return GetX(
      init: ChamaController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            await state.controller?.getMembersOrder(
                chamaId: chamaDataController.chama.value.chama?.id,
                size: chamaController.OData.value.total);
          } catch (e) {
            throw e;
          }
        });
      },
      builder: (ChamaController chamaController) {
        if (chamaController.isloadingChama.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .33,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (chamaController.membersOrder.isEmpty) {
          return const Text("No members added yet.");
        } else if (chamaController.membersOrder.isNotEmpty) {
          return Column(
            children: [
              Container(
                height: SizeConfig.screenHeight * .7,
                margin: EdgeInsets.only(left: 2.h),
                padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
                decoration: AppDecoration.outlineGray
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
                child: ReorderableListView(
                  padding: EdgeInsets.zero,
                  children: List.generate(
                    chamaController.membersOrder.length,
                    (index) {
                      final member = chamaController.membersOrder[index];
                      return Container(
                        key: Key('$index'),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadiusStyle.roundedBorder8,
                        ),
                        child: Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomImageView(
                                  imagePath: AssetUrl.dotSix,
                                  height: 25.h,
                                  width: 25.w,
                                  margin: EdgeInsets.only(right: 3.h),
                                ),
                                Opacity(
                                  opacity: 0.5,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.only(top: 6.h, bottom: 8.h),
                                    child: Text(
                                      "${index + 1}",
                                      style:
                                          theme.textTheme.titleSmall!.copyWith(
                                        color: appTheme.blueGray700
                                            .withOpacity(0.53),
                                      ),
                                    ),
                                  ),
                                ),
                                CustomImageView(
                                  imagePath: AssetUrl.imgPerson,
                                  height: 25.h,
                                  width: 25.w,
                                  margin: EdgeInsets.only(left: 3.h),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 6.h, top: 1.h, bottom: 1.h),
                                      child: Text(
                                        "${member.firstName} ${member.secondName}",
                                        style: CustomTextStyles
                                            .titleSmallGray90001
                                            .copyWith(
                                          color: appTheme.gray90001,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 6.h, top: 1.h, bottom: 1.h),
                                      child: Text(
                                        member.phoneNumber ?? "",
                                        style: CustomTextStyles
                                            .titleSmallGray90001
                                            .copyWith(
                                          color: appTheme.gray90001
                                              .withOpacity(0.53),
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 6.h, top: 1.h, bottom: 1.h),
                                      child: Text(
                                        "Joined On: ${DateTimeFormat.format(member.updatedAt?.toLocal() ?? DateTime.now(), format: ' M j')}\n",
                                        overflow: TextOverflow.ellipsis,
                                        style: CustomTextStyles
                                            .titleSmallGray90001
                                            .copyWith(
                                                color: appTheme.gray90001
                                                    .withOpacity(0.53),
                                                fontStyle: FontStyle.italic),
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                member.role == "CHAIRPERSON"
                                    ? const Padding(
                                        padding:
                                            EdgeInsets.only(top: 12.0),
                                        child: CustomImageView(
                                          imagePath: AssetUrl.crownsv,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 10.h, horizontal: 2.h),
                                  child: Text(
                                    member.role ?? "",
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 2.h),
                            const Divider(),
                          ],
                        ),
                      );
                    },
                  ),
                  onReorder: (int oldIndex, int newIndex) {
                    chamaController.reorderChamaMembers(oldIndex, newIndex);
                  },
                ),
              ),
            ],
          );
        }
        return const Text("No members added yet");
      },
    );
  }

// Add these methods in your ChamaController class

  Future<void> _submitOrder() async {
    final List<member> membersOrder = [];
    for (var indexValue = 0;
        indexValue < chamaController.membersOrder.length;
        indexValue++) {
      final memberId = chamaController.membersOrder[indexValue];
      final order =
          member(id: memberId.id ?? 0, receivingOrder: indexValue + 1);
      membersOrder.add(order);
    }
    final setOrderDto = SetOrderDto(
        chamaId: chamaDataController.chama.value.chama?.id ?? 0,
        members: membersOrder);
    final res =
        await chamaController.setReceivingOrder(setOrderDto: setOrderDto);
    if (res) {
      ToastUtils.showSuccessToast(
          context, chamaController.apiMessage.string, "success");
    } else {
      ToastUtils.showErrorToast(
          context, chamaController.apiMessage.string, "Error");
    }
  }
}
