// To parse this JSON data, do
//
//     final verifyTicketConfirm = verifyTicketConfirmFromJson(jsonString);

import 'dart:convert';

import 'tickets_model.dart';

VerifyTicketConfirm verifyTicketConfirmFromJson(String str) =>
    VerifyTicketConfirm.fromJson(json.decode(str));

String verifyTicketConfirmToJson(VerifyTicketConfirm data) =>
    json.encode(data.toJson());

class VerifyTicketConfirm {
  final VerifyTicketConfirmTransaction? transaction;

  VerifyTicketConfirm({
    this.transaction,
  });

  factory VerifyTicketConfirm.fromJson(Map<String, dynamic> json) =>
      VerifyTicketConfirm(
        transaction: json["transaction"] != null
            ? VerifyTicketConfirmTransaction.fromJson(json["transaction"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "transaction": transaction?.toJson() ?? '',
      };
}

class VerifyTicketConfirmTransaction {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String phoneNumber;
  final int amount;
  final String status;
  final String transactionType;
  final int eventId;
  final String verificationCode;
  final String internalReference;
  final String externalReference;
  final String transactionCode;
  final String channelCode;
  final String firstName;
  final List<TransactionTicket>? transactionTicket;
  final String secondName;
  final String deviceAgent;
  final String ipaddress;

  VerifyTicketConfirmTransaction({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber = '',
    this.amount = 0,
    this.status = '',
    this.transactionType = '',
    this.eventId = 0,
    this.verificationCode = '',
    this.internalReference = '',
    this.externalReference = '',
    this.transactionCode = '',
    this.channelCode = '',
    this.firstName = '',
    this.transactionTicket = const [],
    this.secondName = '',
    this.deviceAgent = '',
    this.ipaddress = '',
  });

  factory VerifyTicketConfirmTransaction.fromJson(Map<String, dynamic> json) =>
      VerifyTicketConfirmTransaction(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        amount: json["amount"] ?? 0,
        status: json["status"] ?? '',
        transactionType: json["transaction_type"] ?? '',
        eventId: json["event_id"] ?? 0,
        verificationCode: json["verification_code"] ?? '',
        internalReference: json["internal_reference"] ?? '',
        externalReference: json["external_reference"] ?? '',
        transactionCode: json["transaction_code"] ?? '',
        channelCode: json["channel_code"] ?? '',
        firstName: json["first_name"] ?? "" ?? '',
        transactionTicket: json["transaction_ticket"] != null
            ? List<TransactionTicket>.from((json["transaction_ticket"] ?? [])
                .map((x) => TransactionTicket.fromJson(x)))
            : null,
        secondName: json["second_name"] ?? "" ?? '',
        deviceAgent: json["device_agent"] ?? '',
        ipaddress: json["ipaddress"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber,
        "amount": amount,
        "status": status,
        "transaction_type": transactionType,
        "event_id": eventId,
        "verification_code": verificationCode,
        "internal_reference": internalReference,
        "external_reference": externalReference,
        "transaction_code": transactionCode,
        "channel_code": channelCode,
        "first_name": firstName,
        "transaction_ticket": transactionTicket != null
            ? List<dynamic>.from(transactionTicket!.map((x) => x.toJson()))
            : [],
        "second_name": secondName,
        "device_agent": deviceAgent,
        "ipaddress": ipaddress,
      };
}

class TransactionTicket {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  final int amount;
  final int quantity;
  final dynamic confirmedById;
  ConfirmedBy? confirmedBy;
  final String status;
  final String verificationStatus;
  final int ticketId;
  Ticket? ticket;
  final int transactionId;
  TransactionTicketTransaction? transaction;
  final String internalReference;

  TransactionTicket({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt = '',
    this.amount = 0,
    this.quantity = 0,
    this.confirmedById = '',
    this.confirmedBy,
    this.status = '',
    this.verificationStatus = '',
    this.ticketId = 0,
    this.ticket,
    this.transactionId = 0,
    this.transaction,
    this.internalReference = '',
  });

  factory TransactionTicket.fromJson(Map<String, dynamic> json) =>
      TransactionTicket(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] ?? '',
        amount: json["amount"] ?? 0,
        quantity: json["quantity"] ?? 0,
        confirmedById: json["confirmed_by_id"] ?? '',
        confirmedBy: json['confirmed_by'] != null
            ? ConfirmedBy.fromJson(json["confirmed_by"] as Map<String, dynamic>)
            : null,
        status: json["status"] ?? '',
        verificationStatus: json["verification_status"] ?? '',
        ticketId: json["ticket_id"] ?? 0,
        ticket: json['ticket'] != null
            ? Ticket.fromJson(json["ticket"] as Map<String, dynamic>)
            : null,
        transactionId: json["transaction_id"] ?? 0,
        transaction: json['transation'] != null
            ? TransactionTicketTransaction.fromJson(
                json["transaction"] as Map<String, dynamic>)
            : null,
        internalReference: json["internal_reference"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "amount": amount,
        "quantity": quantity,
        "confirmed_by_id": confirmedById,
        "confirmed_by": confirmedBy?.toJson(),
        "status": status,
        "verification_status": verificationStatus,
        "ticket_id": ticketId,
        "ticket": ticket?.toJson(),
        "transaction_id": transactionId,
        "transaction": transaction?.toJson(),
        "internal_reference": internalReference,
      };
}

class ConfirmedBy {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String phoneNumber;
  final String firstName;
  final String secondName;
  final String email;
  final int idNumber;
  final double? balance;
  final String birthDate;
  final String countryCode;
  final String county;
  final String subCounty;
  final String latitude;
  final String longitude;
  final String secondaryNumber;
  final String profileUrl;
  final int status;

  ConfirmedBy({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber = '',
    this.firstName = '',
    this.secondName = '',
    this.email = '',
    this.idNumber = 0,
    this.balance = 0.0,
    this.birthDate = '',
    this.countryCode = '',
    this.county = '',
    this.subCounty = '',
    this.latitude = '',
    this.longitude = '',
    this.secondaryNumber = '',
    this.profileUrl = '',
    this.status = 0,
  });

  factory ConfirmedBy.fromJson(Map<String, dynamic> json) => ConfirmedBy(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        firstName: json["first_name"] ?? "" ?? '',
        secondName: json["second_name"] ?? "" ?? '',
        email: json["email"] ?? '',
        idNumber: json["id_number"] ?? 0,
        balance: json["balance"] ?? 0.0,
        birthDate: json["birth_date"] ?? '',
        countryCode: json["country_code"] ?? '',
        county: json["county"] ?? '',
        subCounty: json["sub_county"] ?? '',
        latitude: json["latitude"] ?? '',
        longitude: json["longitude"] ?? '',
        secondaryNumber: json["secondary_number"] ?? '',
        profileUrl: json["profile_url"] ?? '',
        status: json["status"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber,
        "first_name": firstName,
        "second_name": secondName,
        "email": email,
        "id_number": idNumber,
        "balance": balance,
        "birth_date": birthDate,
        "country_code": countryCode,
        "county": county,
        "sub_county": subCounty,
        "latitude": latitude,
        "longitude": longitude,
        "secondary_number": secondaryNumber,
        "profile_url": profileUrl,
        "status": status,
      };
}

class TransactionTicketTransaction {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  TransactionTicketTransaction({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory TransactionTicketTransaction.fromJson(Map<String, dynamic> json) =>
      TransactionTicketTransaction(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt ?? '',
      };
}
