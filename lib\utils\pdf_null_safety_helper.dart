import 'package:intl/intl.dart';

/// Helper class for PDF generation with null safety
class PDFNullSafetyHelper {
  static final DateFormat _dateFormat = DateFormat('EE, dd MMMM h:mm a');

  /// Safely format date with null check
  static String formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    try {
      return _dateFormat.format(date.toLocal());
    } catch (e) {
      return 'Invalid Date';
    }
  }

  /// Safely get transaction code with null check
  static String getTransactionCode(String? code) {
    return code?.isNotEmpty == true ? code! : 'N/A';
  }

  /// Safely get amount with null check
  static String getAmount(dynamic amount) {
    if (amount == null) return '0';
    try {
      return amount.toString();
    } catch (e) {
      return '0';
    }
  }

  /// Safely get full name with null checks
  static String getFullName(String? firstName, String? secondName) {
    final first = firstName?.trim() ?? '';
    final second = secondName?.trim() ?? '';

    if (first.isEmpty && second.isEmpty) return 'N/A';
    if (first.isEmpty) return second;
    if (second.isEmpty) return first;

    return '$first $second';
  }

  /// Safely get email with null check
  static String getEmail(String? email) {
    return email?.isNotEmpty == true ? email! : '';
  }

  /// Safely get balance with null check
  static String getBalance(dynamic balance) {
    if (balance == null) return '0';
    try {
      return balance.toString();
    } catch (e) {
      return '0';
    }
  }

  /// Safely get phone number with null check
  static String getPhoneNumber(String? phone) {
    return phone?.isNotEmpty == true ? phone! : 'N/A';
  }

  /// Safely get ID with null check for table row coloring
  static int getId(int? id) {
    return id ?? 0;
  }

  /// Safely get title with null check
  static String getTitle(String? title) {
    return title?.isNotEmpty == true ? title! : 'N/A';
  }

  /// Safely get username with null check
  static String getUsername(String? username) {
    return username?.isNotEmpty == true ? username! : 'N/A';
  }
}
