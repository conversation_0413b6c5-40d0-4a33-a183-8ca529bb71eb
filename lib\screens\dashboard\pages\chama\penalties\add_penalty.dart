import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/add_penalty_request_model.dart';
import 'package:onekitty/models/chama/penalty_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class AddPenalty extends StatefulWidget {
  final bool isUpdating;
  final PenaltyModel? penaltyModel;
  final bool? isGeneral;
  const AddPenalty(
      {super.key, required this.isUpdating, this.penaltyModel, this.isGeneral});

  @override
  State<AddPenalty> createState() => _AddPenaltyState();
}

class _AddPenaltyState extends State<AddPenalty> {
  TextEditingController titleController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController desController = TextEditingController();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    if (widget.isUpdating && widget.penaltyModel != null) {
      titleController.text = widget.penaltyModel?.title ?? "";
      amountController.text = widget.penaltyModel?.amount.toString() ?? "";
      desController.text = widget.penaltyModel?.description ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: Padding(
          padding: EdgeInsets.only(left: 15.0.w, right: 15.w),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const RowAppBar(),
                SizedBox(
                  height: 20.h,
                ),
                Text(
                  widget.isUpdating ? "Update Penalty" : "Set Penalties",
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
                ),
                SizedBox(
                  height: 5.h,
                ),
                Container(
                  width: 350.w,
                  margin: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Text(
                    widget.isUpdating
                        ? ""
                        : "Create penalties which can be issed to members later",
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                widget.isUpdating
                    ? const SizedBox.shrink()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SingleLineRow(
                            text: "Select from common penalties",
                            popup: KtStrings.generalPenalty,
                          ),
                          InkWell(
                            onTap: () {
                              showGeneralPenaltiesBottomSheet();
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: AppColors.blueButtonColor),
                                  borderRadius: BorderRadius.circular(12)),
                              child: const Text("Click to select penalty",
                                  style: TextStyle(
                                      color: AppColors.blueButtonColor,
                                      fontWeight: FontWeight.bold)),
                            ),
                          ),
                        ],
                      ),
                const SizedBox(
                  height: 10,
                ),
                _buildTextField(context),
                SizedBox(
                  height: 20.h,
                ),
                _buildButton(context),
              ],
            ),
          ),
        ),
        // floatingActionButton: FloatingActionButton.extended(
        //   backgroundColor: Colors.blueAccent,
        //   onPressed: () {
        //     showGeneralPenaltiesBottomSheet();
        //   },
        //   label: Text("Add a general Penalty"),
        // ),
      ),
    );
  }

  /// Section Widget

  Widget _buildButton(BuildContext context) {
    return Obx(() => CustomKtButton(
          isLoading: chamaController.isAddPenaltyLoading.isTrue,
          onPress: () async {
            await addPenalty();
          },
          width: SizeConfig.screenWidth,
          height: 60,
          btnText: widget.isUpdating ? "Update" : "Save",
        ));
  }

  Widget _buildTextField(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customText("Enter penalty name", context),
          CustomTextField(
            controller: titleController,
            hintText: "e.g Missing meeting",
            labelText: "e.g Missing meeting",
            isRequired: true,
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Filed cannot be empty";
              }
              return null;
            },
          ),
          customText("Enter penalty description(Optional)", context),
          CustomTextField(
            controller: desController,
            hintText: "e.g description",
            labelText: "e.g description",
          ),
          customText("Enter penalty amount", context),
          CustomTextField(
            controller: amountController,
            showNoKeyboard: true,
            hintText: "e.g 100",
            labelText: "e.g 100",
            isRequired: true,
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Filed cannot be empty";
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  addPenalty() async {
    if (formKey.currentState!.validate()) {
      dynamic request;
      if (widget.isUpdating) {
        request = UpdatePenaltyRequest(
            id: widget.penaltyModel?.id ?? 0,
            chamaId: chamaDataController.chama.value.chama?.id ?? 0,
            title: titleController.text.trim(),
            description: desController.text.trim(),
            amount: int.tryParse(amountController.text.trim()),
            severity: 5,
            status: "ACTIVE");
      } else {
        request = AddPenaltyRequest(
            chamaId: chamaDataController.chama.value.chama?.id ?? 0,
            title: titleController.text.trim(),
            description: desController.text.trim(),
            amount: int.tryParse(amountController.text.trim()),
            severity: 5,
            status: "ACTIVE");
      }
      bool res;
      if (widget.isUpdating) {
        res =
            await chamaController.addPenalty(request: request, isUpdate: true);
      } else {
        res = await chamaController.addPenalty(request: request);
      }

      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        Get.offAndToNamed(NavRoutes.penalties);
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }

  showGeneralPenaltiesBottomSheet() {
    return showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.7,
              expand: false,
              builder: (context, scrollController) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Text(
                          "COMMON PENALTIES",
                          style: context.titleText
                              ?.copyWith(decoration: TextDecoration.underline),
                        ),
                      ),
                      const SizedBox(height: 7),
                      Expanded(
                        child: GetX(
                            init: ChamaController(),
                            initState: (state) {
                              Future.delayed(Duration.zero, () async {
                                try {
                                  await state.controller?.getGeneralPenalties(
                                      chamaId: chamaDataController
                                              .chama.value.chama?.id ??
                                          0);
                                } catch (e) {
                                  throw e;
                                }
                              });
                            },
                            builder: (ChamaController chamaController) {
                              if (chamaController
                                  .isGetGeneralPenalties.isTrue) {
                                return SizedBox(
                                  height: SizeConfig.screenHeight * .33,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SpinKitDualRing(
                                          color: ColorUtil.blueColor,
                                          lineWidth: 4.sp,
                                          size: 40.0.sp,
                                        ),
                                        const Text(
                                          "loading..",
                                          style: TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              } else if (chamaController
                                  .generalPenalties.isEmpty) {
                                return const Text(
                                  "No general penalties set, kindly add one.",
                                  textAlign: TextAlign.center,
                                );
                              } else if (chamaController
                                  .generalPenalties.isNotEmpty) {
                                return ListView.separated(
                                    separatorBuilder: (context, index) =>
                                        const Divider(),
                                    itemCount:
                                        chamaController.generalPenalties.length,
                                    itemBuilder: (context, index) {
                                      final generalPenalty = chamaController
                                          .generalPenalties[index];
                                      return InkWell(
                                        onTap: () {
                                          titleController.text =
                                              generalPenalty.title ?? "";
                                          amountController.text =
                                              generalPenalty.amount.toString();
                                          desController.text =
                                              generalPenalty.description ?? "";
                                          Navigator.pop(context);
                                        },
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                                padding:
                                                    const EdgeInsets.all(8),
                                                margin: const EdgeInsets.only(
                                                    right: 10),
                                                decoration: const BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.neutral),
                                                child: Text(
                                                  "${index + 1}",
                                                  style:
                                                      context.dividerTextLarge,
                                                )),
                                            Expanded(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      generalPenalty.title ??
                                                          "",
                                                      style: context.titleText),
                                                  Text(generalPenalty
                                                          .description ??
                                                      ""),
                                                  Text(
                                                    "${generalPenalty.amount}",
                                                    style: context
                                                        .dividerTextLarge
                                                        ?.copyWith(
                                                            color: Colors.red),
                                                  ),
                                                  const SizedBox(
                                                    height: 15,
                                                  ),
                                                  // CustomKtButton(
                                                  //   width: SizeConfig.screenWidth * 0.8,
                                                  //   btnText: "Save",
                                                  //   onPress: () async {

                                                  // AddPenaltyRequest request =
                                                  //     AddPenaltyRequest(
                                                  //         chamaId:
                                                  //             chamaDataController.chama
                                                  //                     .value.chama?.id ??
                                                  //                 0,
                                                  //         title: generalPenalty.title,
                                                  //         description:
                                                  //             generalPenalty.description,
                                                  //         amount: generalPenalty.amount,
                                                  //         severity: generalPenalty.severity,
                                                  //         status: generalPenalty.status);
                                                  // bool res = await chamaController
                                                  //     .addPenalty(request: request);
                                                  // if (res) {
                                                  //   if (!mounted) return;
                                                  //   Snack.show(res,
                                                  //       chamaController.apiMessage.string);
                                                  //   Get.toNamed(NavRoutes.penalties);
                                                  // } else {
                                                  //   if (!mounted) return;
                                                  //   Snack.show(res,
                                                  //       chamaController.apiMessage.string);
                                                  // }
                                                  //   },
                                                  // )
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    });
                              }
                              return const Text(
                                "No general penalties set, kindly add one.",
                                textAlign: TextAlign.center,
                              );
                            }),
                      ),
                    ],
                  ),
                );
              });
        });
  }
}
