import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:get/get.dart';

import '../../../../../../utils/utils_exports.dart';

class ChamaWhatsappWidget extends StatefulWidget {
  final NotificationCls whatsapp;
  const ChamaWhatsappWidget({super.key, required this.whatsapp});

  @override
  State<ChamaWhatsappWidget> createState() => _ChamaWhatsappWidgetState();
}

class _ChamaWhatsappWidgetState extends State<ChamaWhatsappWidget> {
  final ChamaController chamaController = Get.put(ChamaController());
  bool value = true;
  bool isLoading = false;
  bool isRloading = false;

  setValue() async {
    if (widget.whatsapp.whatsappStatus == "ACTIVE") {
      value = true;
    } else {
      value = false;
    }
  }

  @override
  void initState() {
    super.initState();
    setValue();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 7.w,
        vertical: 4.h,
      ),
      decoration: AppDecoration.outlineBlueGray.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          widget.whatsapp.whatsappProfile != null &&
                  widget.whatsapp.whatsappProfile!.isNotEmpty
              ? CircleAvatar(
                  backgroundColor: Colors.transparent,
                  backgroundImage: CachedNetworkImageProvider(
                    widget.whatsapp.whatsappProfile??'',
                    maxHeight: 30,
                    maxWidth: 30,
                    errorListener: (p0) {
                      return;
                    },
                  ),
                )
              : CustomImageView(
                  imagePath: AssetUrl.whatsapp,
                  height: 40.h,
                  width: 40.w,
                  radius: BorderRadius.circular(
                    20.w,
                  ),
                ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: 6.w,
                top: 8.h,
                bottom: 10.h,
              ),
              child: Text(
                widget.whatsapp.whatsappGroupName ?? "",
                overflow: TextOverflow.ellipsis,
                style: CustomTextStyles.titleSmallGray900,
              ),
            ),
          ),
          isLoading
              ? SpinKitDualRing(
                  color: Theme.of(context).primaryColor,
                  size: 25.0,
                )
              : Switch.adaptive(
                  value: value,
                  onChanged: (value) async {
                    setState(() {
                      isLoading = true;
                    });
                    bool res = await chamaController.toggleWhatsapp(
                        nId: widget.whatsapp.id ?? 0,
                        status: value ? "ACTIVE" : "INACTIVE");

                    if (res) {
                      setState(() {
                        isLoading = false;
                        this.value = value;
                      });
                      Snack.show(res, chamaController.apiMessage.string);
                    } else {
                      Snack.show(res, chamaController.apiMessage.string);
                      setState(() {
                        isLoading = false;
                      });
                    }
                  },
                ),
          SizedBox(
            width: 20.w,
          ),
          InkWell(
            onTap: () async {
              bool confirm = await _showConfirmationDialog(context);
              if (confirm) {
                setState(() {
                  isRloading = true;
                });

                bool res = await chamaController.RmWhatsapp(
                  nId: widget.whatsapp.id ?? 0,
                  chId: widget.whatsapp.chamaId ?? 0,
                );

                if (res) {
                  setState(() {
                    isRloading = false;
                  });
                  Snack.show(res, chamaController.apiMessage.string);
                } else {
                  Snack.show(res, chamaController.apiMessage.string);
                  setState(() {
                    isRloading = false;
                  });
                }
              }
            },
            child: isRloading
                ? SpinKitDualRing(
                    color: Theme.of(context).primaryColor,
                    size: 25.0,
                  )
                : CustomImageView(
                    height: 20.h,
                    color: Colors.red,
                    imagePath: AssetUrl.imgIconoirCancel,
                  ),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Confirm Removal'),
              content: const Text(
                  'Are you sure you want to remove this WhatsApp?\nYou will no longer be receiving transaction updates in your WhatsApp group'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  child: const Text('Confirm'),
                ),
              ],
            );
          },
        ) ??
        false;
  }
}
