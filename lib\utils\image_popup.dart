import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/showCachedNetworkImage.dart';

class ImagePopup extends StatefulWidget {
  final List<String>? imageUrl, imageAsset, imageFile;
  final int? pos;
  final String title;
  const ImagePopup(
      {super.key,
      this.pos = 1,
      this.imageUrl,
      this.imageAsset,
      this.imageFile,
      required this.title});

  @override
  State<ImagePopup> createState() => _ImagePopupState();
}

class _ImagePopupState extends State<ImagePopup> {
  final pageController = PageController();
  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        pageController.jumpToPage(widget.pos ?? 0);
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final pageNo = (widget.pos?.obs ?? 1.obs) == 0.obs ? 1.obs : (widget.pos?.obs ?? 1.obs);
    if (widget.imageUrl == null &&
        widget.imageAsset == null &&
        widget.imageFile == null) {
      throw 'not all image sources should be null';
    }
    List<String> combinedImages = [
      ...widget.imageUrl ?? [],
      ...widget.imageAsset ?? [],
      ...widget.imageFile ?? [],
    ];
    return Scaffold(
      backgroundColor: const Color(0xff0e0e0e),
      appBar: AppBar(
        backgroundColor: const Color(0xff0e0e0e),
        title: Text(widget.title),
        leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close)),
      ),
      body: Stack(
        children: [
          Positioned(
            child: PageView.builder(
              onPageChanged: (page) {
                pageNo(page + 1);
              },
              controller: pageController,
              itemCount: combinedImages.length,
              itemBuilder: (context, index) {
                return ShowCachedNetworkImage(
                    telegramLoading: true,
                    zoomImage: true,
                    black: true,
                    fit: BoxFit.contain,
                    width: MediaQuery.sizeOf(context).width,
                    imageurl: combinedImages[index]);
              },
            ),
          ),
          Positioned(
              right: 8,
              top: 8,
              child: Container(
                  width: 40,
                  alignment: Alignment.center,
                  // padding: EdgeInsets.all(4),
                  height: 30,
                  decoration: BoxDecoration(
                      color: Colors.black12,
                      borderRadius: BorderRadius.circular(24)),
                  child: Obx(
                    () => Text('${pageNo.value}/${combinedImages.length}',
                        style: const TextStyle(color: Colors.white)),
                  )))
        ],
      ),
    );
  }
}
