// ignore_for_file: prefer_const_constructors, prefer_const_literals_to_create_immutables

import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw_bottomsheet.dart';
import 'package:onekitty/main.dart' show isLight;
import '../../../../../utils/utils_exports.dart';

class WithdrawPage extends StatefulWidget {
  const WithdrawPage({super.key});

  @override
  State<WithdrawPage> createState() => _WithdrawPageState();
}

class _WithdrawPageState extends State<WithdrawPage> {
  //final ContributeController singleKitty = Get.put(ContributeController());
  KittyController kittyController = Get.put(KittyController());
  int? kittyId;
  TextEditingController amountController = TextEditingController();
  final DataController dataController = Get.find<DataController>();
  bool kittyActive = true;
  bool isFromWithDrawPage = false;
  UserKittyController controller = Get.put(UserKittyController());

  setValues() async {
    final kittyCreated = dataController.kitty.value.kitty;
    kittyId = kittyCreated?.iD;
  }

  @override
  void initState() {
    String kittyStatus = dataController.kitty.value.kittyStatus ?? '';
    if (!kittyStatus.toLowerCase().contains("active")) {
      kittyActive = false;
    }
    setValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final endDate = dataController.kitty.value.kitty?.endDate;
    //DateTime now = DateTime.now();
    //final remDays = endDate?.difference(now);
    //final text = remDays?.inDays.toString();
    return Scaffold(
      body: Container(
        margin: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
        child: SingleChildScrollView(
          child: Column(
            children: [
              RowAppBar(),
              SizedBox(
                height: 20,
              ),
              Text(
                dataController.kitty.value.kitty?.title ?? '',
                style:
                    context.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Container(
                margin: EdgeInsets.symmetric(vertical: 15),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.neutralGrey,
                      //spreadRadius: 2,
                      blurRadius: 3,
                      //offset: Offset(3, 0),
                    )
                  ],
                  borderRadius: BorderRadius.circular(7),
                  color: isLight.value ? Colors.white : appTheme.gray900,
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            SvgPicture.asset(AssetUrl.imgCalendar),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              "Created:",
                              style: context.dividerTextSmall
                                  ?.copyWith(fontSize: 15),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(dataController.kitty.value.kitty == null ||
                                    dataController
                                            .kitty.value.kitty?.createdAt ==
                                        null
                                ? ''
                                : DateFormat('MMM dd, yyyy').format(
                                    dataController
                                        .kitty.value.kitty!.createdAt!)),
                          ],
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Row(
                          children: [
                            OutlinedButton(
                                onPressed: () {},
                                child: Text(
                                    dataController.kitty.value.kittyStatus ??
                                        '')),
                          ],
                        )
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SvgPicture.asset(AssetUrl.imgClock),
                        SizedBox(
                          width: 4,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "End Date:",
                              style: context.dividerTextSmall
                                  ?.copyWith(fontSize: 15),
                            ),
                            Text(
                              "${DateTimeFormat.format(endDate ?? DateTime.now(), format: 'D, M j, H:i')}\n",
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Row(
                      children: [
                        SvgPicture.asset(AssetUrl.money),
                        SizedBox(
                          width: 4,
                        ),
                        Text("Balance:"),
                        SizedBox(
                          width: 4,
                        ),
                        Text(
                            FormattedCurrency.getFormattedCurrency(
                                dataController.kitty.value.kitty!.balance
                                    .toString()),
                            style: context.dividerTextSmall
                                ?.copyWith(fontSize: 15)),
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    /*   Row(
                      children: [
                        SvgPicture.asset(AssetUrl.imgUser),
                        SizedBox(
                          width: 4,
                        ),
                        Text("Beneficiary:"),
                        SizedBox(
                          width: 4,
                        ),
                        Expanded(
                          child: dataController
                                      .kitty.value.kitty?.settlementType ==
                                  2
                              ? Text(
                                  "${dataController.kitty.value.kitty?.beneficiaryAccount} ${dataController.kitty.value.kitty?.bennefAccRef ?? ''}  ${getNetworkName(dataController.kitty.value.kitty?.beneficiaryChannel ?? "")} (${getSettlementType(dataController.kitty.value.kitty?.settlementType ?? 0)})",
                                  style: context.dividerTextSmall
                                      ?.copyWith(fontSize: 15),
                                  overflow: TextOverflow.fade,
                                )
                              : Text(
                                  "${dataController.kitty.value.kitty?.beneficiaryAccount} ${getNetworkName(dataController.kitty.value.kitty?.beneficiaryChannel ?? "")} (${getSettlementType(dataController.kitty.value.kitty?.settlementType ?? 0)})",
                                  style: context.dividerTextSmall
                                      ?.copyWith(fontSize: 15),
                                  overflow: TextOverflow.fade,
                                ),
                        ),
                      ],
                    ), */
                    SizedBox(
                      height: 5,
                    ),
                  ],
                ),
              ),
              Image.asset(AssetUrl.manTop),
              SizedBox(
                height: 15,
              ),
              Text(
                "Make a withdrawal request",
                style:
                    context.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(
                height: 8,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "Funds shall be transferred to the specified beneficiary channel",
                  textAlign: TextAlign.center,
                  style: context.titleSmall?.copyWith(
                      color: AppColors.greyTextColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 15),
                ),
              ),
              SizedBox(
                height: 7,
              ),
              Container(
                margin: EdgeInsets.symmetric(vertical: 10, horizontal: 50.sp),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.neutralGrey,
                      //spreadRadius: 2,
                      blurRadius: 3,
                      //offset: Offset(3, 0)
                    )
                  ],
                  borderRadius: BorderRadius.circular(7),
                  color: isLight.value ? Colors.white : appTheme.gray900,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Amount to withdraw",
                      style: context.titleText
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 7,
                    ),
                    SizedBox(
                      height: 22,
                    ),
                    Center(
                      child: CustomKtButton(
                        onPress: () async {
                          if (!kittyActive) {
                            ToastUtils.showErrorToast(
                                context, "Kitty is not Active", "");
                            return;
                          }
                          var results = await showWBeneficiariesBottomSheet(
                              context,
                              kittyId: kittyId!);
                          print(results);
                          if (results != null) {
                            showWithdrawBottomSheet(
                                beneficiaryId: results['id'],
                                beneficiaryTitle: results['title'],
                                context,
                                kittyId!,
                                dataController.kitty.value.kitty!,
                                isFromWithDrawPage);
                          }
                        },
                        width: 120.w,
                        height: 45.sp,
                        btnText: "Withdraw",
                        isActive: kittyActive,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
