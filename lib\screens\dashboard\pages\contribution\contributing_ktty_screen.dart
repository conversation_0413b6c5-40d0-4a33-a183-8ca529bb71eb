import 'dart:async'; 
import 'package:date_time_format/date_time_format.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart'; 
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart'; 
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/confirm_payment_screen.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
// import '../../../../utils/my_quill_editor.dart';
import '../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class ContributingToAKitty extends StatefulWidget {
  const ContributingToAKitty({super.key});

  @override
  State<ContributingToAKitty> createState() => _ContributingToAKittyState();
}

class _ContributingToAKittyState extends State<ContributingToAKitty> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController nameEditTextController = TextEditingController();
  TextEditingController amountEditTextController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  TextEditingController identifierController = TextEditingController();

  // Report form controllers
  TextEditingController reportPhoneController = TextEditingController();
  TextEditingController reportEmailController = TextEditingController();
  TextEditingController reportTitleController = TextEditingController();
  TextEditingController reportDescriptionController = TextEditingController();

  ContributeController contributeController = Get.find();
  String? _number;
  String? _reportNumber;
  List<String> _uploadedFiles = [];
  bool _isUploading = false;
  bool _isSubmittingReport = false;
  late Timer _timer;
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));

  // Add a value notifier for time-dependent UI elements
  final ValueNotifier<DateTime> _currentTimeNotifier =
      ValueNotifier<DateTime>(DateTime.now());
  getKittyData() async {
    var params = Get.parameters;
    String kittyId = params['id'] ?? "";
    int? id = int.tryParse(kittyId);
    if (id != null) {
      await contributeController.getKitty(id: id);
    } else {
      contributeController.apiMessage.value = "Invalid Kitty ID : $kittyId";
    }
  }

  @override
  void initState() {
    super.initState();
    // Replace the setState timer with a more targeted approach
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _currentTimeNotifier.value = DateTime.now();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getKittyData();
    });
  }

  bool isNameChecked = false;
  bool isNumberChecked = false;

  String? selectedChannel = "M-Pesa";
  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer to prevent memory leaks
    _currentTimeNotifier.dispose(); // Dispose the ValueNotifier
    nameEditTextController.dispose();
    amountEditTextController.dispose();
    phoneNumberController.dispose();
    emailTextController.dispose();
    identifierController.dispose();
    reportPhoneController.dispose();
    reportEmailController.dispose();
    reportTitleController.dispose();
    reportDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return ScreenUtilInit(
        splitScreenMode: true,
        designSize: const Size(392.72727272727275, 850.9090909090909),
        builder: (context, child) {
          return Scaffold(
              appBar: AppBar(
                title: Text('Contribute to Kitty',
                    style: textTheme.titleLarge?.copyWith(color: Colors.white)),
                centerTitle: true,
                elevation: 1,
                actions: [
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'report') {
                        _showReportBottomSheet(context);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'report',
                        child: Row(
                          children: [
                            Icon(Icons.report_outlined),
                            SizedBox(width: 8),
                            Text('Report'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              body:
                  // Obx(() => contributeController.apiMessage.value.isNotEmpty
                  //     ? _buildErrorWidget(context)
                  //     :
                  Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ColoredBox(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                child: _buildKittyHeader(context)),
                            SizedBox(height: 16.h),
                            _buildKittyInfoCard(context),
                            SizedBox(height: 16.h),
                            _buildContributionForm(context),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: _buildActionButton(context),
                    ),
                  ],
                ),
              ));
        });
  }

  Widget _buildStatusChip() {
    final statusColor = _getStatusColor(
        contributeController.kittStatus.value.isEmpty
            ? "Active"
            : contributeController.kittStatus.value);
    return Obx(
      () => Container(
        width: 120.w,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
            color: statusColor.withOpacity(0.15),
            borderRadius: BorderRadius.circular(30)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              contributeController.kittStatus.value.isEmpty
                  ? "Active"
                  : contributeController.kittStatus.value,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: statusColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF00C853);
      case "completed":
        return const Color(0xFF6200EA);
      case "settlement initiated":
        return const Color(0xFFFFD600);
      default:
        return const Color(0xFFE53935);
    }
  }

  Widget _buildCategoriesDisplay() {
    // Check if categories exist and are not empty
    if (contributeController.kittGoten.value.categories == null ||
        contributeController.kittGoten.value.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories:',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          SizedBox(height: 8.h),
          SizedBox(
            height: 32.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: contributeController.kittGoten.value.categories!.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) {
                final category = contributeController.kittGoten.value.categories![index];
                return _buildCategoryChip(category.name ?? '');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String categoryName) {
    if (categoryName.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF2196F3).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: const BoxDecoration(
              color: Color(0xFF2196F3),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            categoryName,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2196F3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKittyHeader(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            Obx(
              () => contributeController.isgetkittyloading.isTrue
                  ? const Center(child: CircularProgressIndicator())
                  : Text.rich(TextSpan(children: [
                      TextSpan(
                        text: contributeController.kittGoten.value.title ?? '',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                    ])),
            ),
            Row(
              children: [
                // Expanded(child: _buildProgressIndicator()),
                const Spacer(),
                SizedBox(
                  width: 120.w,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: _buildStatusChip(),
                  ),
                ),
              ],
            )
          ],
        ),
        _buildCategoriesDisplay(),
        SizedBox(height: 8.h),
        Obx(
          () => contributeController.isgetkittyloading.isTrue
              ? const Center(child: SizedBox())
              : QuillEditorWidget(
                  text: extractText(
                      contributeController.kittGoten.value.description ?? ""),
                  tag: 'desc', // Add unique tag
                ),
        ),
      ],
    );
  }

  Widget _buildKittyInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'Created',
              value: contributeController.kittGoten.value.createdAt == null
                  ? 'N/A'
                  : DateFormat('MMM dd, yyyy').format(
                      contributeController.kittGoten.value.createdAt ??
                          DateTime.now()),
            ),
            Divider(height: 16.h),
            if (contributeController.volumes.toString() != "0") ...[
              _buildInfoRow(
                icon: Icons.people_alt_outlined,
                label: 'Volumes',
                value: contributeController.volumes.toString(),
              ),
              Divider(height: 16.h),
            ],
            Row(
              children: [
                Icon(Icons.timer_outlined,
                    size: 18.sp, color: Colors.grey[600]),
                SizedBox(width: 8.w),
                Text(
                  'Ends:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800],
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: _buildTimeWidget(
                    contributeController.kittGoten.value.endDate,
                  ),
                ),
              ],
            ),
         if (contributeController.kittGoten.value.percentage != null) _buildProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: LinearProgressIndicator(
        value: contributeController.kittGoten.value.percentage ?? 0,
        minHeight: 3.h,
        backgroundColor: Colors.grey.shade300,
        valueColor:
            AlwaysStoppedAnimation(_getStatusColor(contributeController.kittGoten.value.status.toString())),
      ),
    );
  }

  Widget _buildInfoRow(
      {required IconData icon, required String label, required String value}) {
    return Row(
      children: [
        Icon(icon, size: 18.sp, color: Colors.grey[600]),
        SizedBox(width: 8.w),
        Text(
          '$label:',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Colors.grey[800],
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  // Add a time-dependent widget that uses ValueListenableBuilder
  Widget _buildTimeWidget(DateTime? endDate) {
    return ValueListenableBuilder<DateTime>(
      valueListenable: _currentTimeNotifier,
      builder: (context, currentTime, child) {
        return Text(
          endDate == null
              ? 'N/A'
              : DateTimeFormat.relative(
                  endDate,
                  levelOfPrecision: 2,
                  prependIfBefore: 'Ends In',
                  ifNow: "Now",
                  appendIfAfter: 'ago',
                ),
          textAlign: TextAlign.right,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).primaryColor,
          ),
        );
      },
    );
  }

  Widget _buildContributionForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Your Information',
            style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 16.h),
        _buildNameInput(),
        SizedBox(height: 16.h),
        _buildPhoneInput(),
        SizedBox(height: 16.h),
        Obx(
          () => (contributeController.singleKitty.value.hasMembership ?? false)
              ? Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: _buildIdentifierInput(),
                )
              : const SizedBox.shrink(),
        ),
        
        _buildAmountInput(),
        SizedBox(height: 24.h),
        Text('Payment Method', style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 12.h),
        ContributeChannelsBuilder(
          selectedChannel: selectedChannel ?? "",
          onChange: (String? newlySelectedChannel) {
            setState(() => selectedChannel = newlySelectedChannel);
          },
        ),
        if (selectedChannel == 'Visa') _buildEmailInput(),
      ],
    );
  }

  Widget _buildNameInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: nameEditTextController,
                decoration: InputDecoration(
                  labelText: 'Full Name',
                  hintText: 'John Doe',
                  suffixIcon: IconButton(
                    icon: Icon(Icons.info_outline, size: 20.w),
                    onPressed: () => _showTooltip(KtStrings.hideNames),
                  ),
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text('Hide  name in contributions',
                style: Theme.of(context).textTheme.bodySmall),
            Checkbox(
              value: isNameChecked,
              onChanged: (v) => setState(() => isNameChecked = v!),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPhoneInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: CustomInternationalPhoneInput(
                controller: phoneNumberController,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value!.isEmpty) return 'Required';
                  if (value.length < 9) return 'Invalid number';
                  return null;
                },
                onInputChanged: (PhoneNumber p1) {
                  _number = p1.phoneNumber?.replaceAll("+", "");
                },
                initialValue: PhoneNumber(dialCode: '+254', isoCode: "KE"),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text('hide number in contributions',
                style: Theme.of(context).textTheme.bodySmall),
            Checkbox(
              value: isNumberChecked,
              onChanged: (v) => setState(() => isNumberChecked = v!),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIdentifierInput() {
    return TextFormField(
      controller: identifierController,
      decoration: InputDecoration(
        labelText: contributeController.singleKitty.value.paymentRefLabel ??
            'Payment Ref',
        hintText:
            'Enter your ${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}',
        prefixIcon: const Icon(Icons.person_outline),
        suffixIcon: Tooltip(
            triggerMode: TooltipTriggerMode.tap,
            message:
                'Please provide your ${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}',
            child: Icon(Icons.info_outline, size: 20.w)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '"${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}" is required';
        }
        return null;
      },
    );
  }

  Widget _buildAmountInput() {
    return TextFormField(
      controller: amountEditTextController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'Amount (KES)',
        hintText: '5000',
        prefixIcon: Padding(
          padding: EdgeInsets.all(12.w),
          child: Text('KES', style: Theme.of(context).textTheme.bodyLarge),
        ),
      ),
      validator: (value) {
        if (value!.isEmpty) return 'Enter amount';
        if (int.tryParse(value) == null) return 'Invalid amount';
        return null;
      },
    );
  }

  Widget _buildEmailInput() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: TextFormField(
        controller: emailTextController,
        keyboardType: TextInputType.emailAddress,
        decoration: const InputDecoration(
          labelText: 'Email Address',
          hintText: '<EMAIL>',
          prefixIcon: Icon(Icons.email_outlined),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Obx(
          () => FilledButton.icon(
            icon: contributeController.isContributeloading.value
                ? SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  )
                : const Icon(Icons.attach_money_outlined),
            label: Text(
              contributeController.isContributeloading.value
                  ? 'Processing...'
                  : 'Make Contribution',
            ),
            onPressed: _handleContribution,
            style: FilledButton.styleFrom(
              minimumSize: Size(double.infinity, 56.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showTooltip(String message) {
    final tooltipKey = GlobalKey<TooltipState>();
    final tooltip = Tooltip(
      key: tooltipKey,
      message: message,
      child: Container(),
    );
    final overlay = Overlay.of(context);
    final entry = OverlayEntry(
      builder: (context) => tooltip,
    );
    overlay.insert(entry);
    tooltipKey.currentState?.ensureTooltipVisible();
    Future.delayed(const Duration(seconds: 2), () {
      entry.remove();
    });
  }

  void _handleContribution() async {
    if (_formKey.currentState!.validate()) {
      String fullName = nameEditTextController.text.trim();
      String? firstName;
      String? lastName;
      List<String> names = fullName.split(' ');
      if (names.length == 1) {
        firstName = names[0];
      } else if (names.length >= 2) {
        firstName = names[0];
        // last name should be from zero index onwards
        lastName = names.sublist(1).join(' ');
      }
      //-----------GET CHANNEL--------
      final chan = KittyController().getNetworkCode(
        networkTitle: selectedChannel ?? "",
      );

      //----------TRANSACT-----------

      final kitty = contributeController.kittGoten.value;

      bool res = await contributeController.contribute(
        phoneNumber: _number ?? '',
        firstName: firstName,
        secondName: lastName,
        amount: int.parse(amountEditTextController.text.trim()),
        channel: chan!,
        kittyId: kitty.iD!,
        shownames: !isNameChecked,
        shownumber: !isNumberChecked,
        email: emailTextController.text,
        paymentRef:
            contributeController.singleKitty.value.hasMembership ?? false
                ? identifierController.text
                : null,
      );

      if (!mounted) return;
      //----------NAVIGATE USER----------
      if (res) {
        if (chan == 0) {
          ToastUtils.showSuccessToast(
            context,
            contributeController.apiMessageContri.string,
            "success",
          );
          Get.to(() => const ProcessPaymentOtp(isChamaContribute: false));
        }
        if (chan == 63902) {
          ToastUtils.showSuccessToast(
            context,
            contributeController.apiMessageContri.string,
            "success",
          );

          try {
            contributeController.confirmpayLoading.value = true;
            int attempts = 0;
            bool result;
            bool success = false; // Flag to track success

            while (attempts < 5) {
              // Maximum 5 attempts
              contributeController.confirmpayLoading.value = true;

              result = await contributeController.confirmContribution(
                  checkoutId: contributeController
                      .contributeData["checkout_request_id"]);

              await Future.delayed(const Duration(
                  seconds: 5)); // Wait for 5 seconds before next attempt
              attempts++;

              if (result) {
                var status = contributeController.status.value;
                if (status == "SUCCESS") {
                  success = true;
                  break;
                }
                if (status == "FAILED") {
                  success = false;
                  break;
                }
              }
            }
            contributeController.confirmpayLoading.value = false;

            if (success) {
              Get.toNamed(
                  NavRoutes.contrSuccessScreen); // Navigate to success screen
            } else {
              Get.toNamed(NavRoutes.contrErrScreen); // Navigate to error screen
            }
          } catch (e) {}
        }
        if (chan == 55) {
          Get.off(() => const CardPayment(isChamaContribute: false));
          //Get.toNamed(NavRoutes.cardpayment);
        }
      } else {
        ToastUtils.showErrorToast(
          context,
          contributeController.apiMessageContri.string,
          "Error",
        );
      }
    } else {
      ToastUtils.showErrorToast(
        context,
        "Check Your Values",
        "Error",
      );
    }
    _formKey.currentState?.reset();
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off_rounded,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              'Kitty Not Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
            ),
            SizedBox(height: 12.h),
            Text(
              contributeController.apiMessage.value,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            SizedBox(height: 32.h),
            FilledButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back),
              label: const Text('Go Back'),
              style: FilledButton.styleFrom(
                minimumSize: Size(200.w, 48.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            SizedBox(height: 16.h),
            OutlinedButton.icon(
              onPressed: () => getKittyData(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: OutlinedButton.styleFrom(
                minimumSize: Size(200.w, 48.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showReportBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16.w,
            right: 16.w,
            top: 16.h,
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Report Incident',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                CustomInternationalPhoneInput(
                  controller: reportPhoneController,
                  labelText: 'Phone Number *',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Phone number is required';
                    }
                    if (value.length < 9) return 'Invalid phone number';
                    return null;
                  },
                  onInputChanged: (PhoneNumber p1) {
                    _reportNumber = p1.phoneNumber?.replaceAll("+", "");
                  },
                  initialValue: PhoneNumber(dialCode: '+254', isoCode: "KE"),
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: reportEmailController,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    hintText: '<EMAIL>',
                    prefixIcon: Icon(Icons.email_outlined),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: reportTitleController,
                  decoration: const InputDecoration(
                    labelText: 'Title *',
                    hintText: 'Brief description of the issue',
                    prefixIcon: Icon(Icons.title_outlined),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Title is required';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: reportDescriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Detailed description of the incident',
                    prefixIcon: Icon(Icons.description_outlined),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Description is required';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                Text(
                  'Supporting Evidence',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8.h),
                GestureDetector(
                  onTap: _isUploading ? null : () => _pickFiles(setModalState),
                  child: Container(
                    height: 55.h,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Upload Files',
                          style: TextStyle(fontSize: 14.sp),
                        ),
                        _isUploading
                            ? const CupertinoActivityIndicator()
                            : const Icon(Icons.upload_file),
                      ],
                    ),
                  ),
                ),
                if (_uploadedFiles.isNotEmpty) ...[
                  SizedBox(height: 8.h),
                  Wrap(
                    spacing: 8.w,
                    children: _uploadedFiles
                        .map((file) => Chip(
                              label: Text(file.split('/').last),
                              deleteIcon: const Icon(Icons.close, size: 16),
                              onDeleted: () => setModalState(
                                  () => _uploadedFiles.remove(file)),
                            ))
                        .toList(),
                  ),
                ],
                SizedBox(height: 24.h),
                SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    onPressed: (_isUploading || _isSubmittingReport)
                        ? null
                        : () => _submitReport(context, setModalState),
                    child: _isSubmittingReport
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 16.w,
                                height: 16.w,
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(width: 8.w),
                              const Text('Submitting...'),
                            ],
                          )
                        : Text(_isUploading ? 'Uploading...' : 'Submit Report'),
                  ),
                ),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _pickFiles(StateSetter setModalState) async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      setModalState(() => _isUploading = true);

      for (final file in result.files) {
        if (file.path != null) {
          // Simulate upload - replace with actual upload logic
          await Future.delayed(const Duration(seconds: 1));
          _uploadedFiles.add(file.path!);
        }
      }

      setModalState(() => _isUploading = false);
    }
  }

  void _submitReport(BuildContext context, StateSetter setModalState) async {
    // Validate required fields
    if (reportTitleController.text.trim().isEmpty ||
        reportDescriptionController.text.trim().isEmpty ||
        (_reportNumber?.isEmpty ?? true)) {
      ToastUtils.showErrorToast(
          context, 'Please fill all required fields', 'Error');
      return;
    }

    setModalState(() => _isSubmittingReport = true);

    try {
      final kittyId = contributeController.kittGoten.value.iD;
      final _httpService = Get.find<HttpService>();
      final payload = {
        "kitty_id": kittyId,
        "title": reportTitleController.text.trim(),
        "description": reportDescriptionController.text.trim(),
        "phone_number": _reportNumber ?? '',
        "email": reportEmailController.text.trim(),
        "media": _uploadedFiles,
        "imei_code": Get.find<GetStorage>().read(CacheKeys.imeiNumber),
        "latitude": Get.find<GetStorage>().read(CacheKeys.lat),
        "longitude": Get.find<GetStorage>().read(CacheKeys.long),
        "type": "REPORT_KITTY",
        "device_model": Get.find<GetStorage>().read(CacheKeys.deviceModel),
      };
      final response = await _httpService.request(
        url: ApiUrls.report_kitty,
        params: payload,
        method: Method.POST,
      );

      if (response.data['status'] ?? false) {
        Navigator.pop(context);
        ToastUtils.showSuccessToast(
            context, 'Report submitted successfully', 'Success');
        _clearReportForm();
      } else {
        ToastUtils.showErrorToast(context, 'Failed to submit report', 'Error');
      }
    } catch (e) {
      ToastUtils.showErrorToast(
          context, 'Error submitting report: $e', 'Error');
    } finally {
      setModalState(() => _isSubmittingReport = false);
    }
  }

  void _clearReportForm() {
    reportPhoneController.clear();
    reportEmailController.clear();
    reportTitleController.clear();
    reportDescriptionController.clear();
    _uploadedFiles.clear();
    _reportNumber = null;
  }

  /// Navigates back to the previous screen.
  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
