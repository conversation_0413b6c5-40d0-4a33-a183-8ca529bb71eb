import 'dart:convert';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';
/// Checks if the given string contains WYSIWYG formatting
/// Returns true if the string contains HTML tags or rich text markers
/// 

bool isWysiwygFormat(String text) {
    if (text.isEmpty) return false;

    // Regular expression to match common HTML tags
    final htmlRegex = RegExp(
        r'<\/?[a-z][\s\S]*>|&[a-z]+;',
        caseSensitive: false,
    );

    // Check for HTML tags
    if (htmlRegex.hasMatch(text)) {
        return true;
    }

    // Common rich text markers
    final List<String> richTextMarkers = [
        '**', // Bold in Markdown
        '*',  // Italic in Markdown
        '__', // Underline in some formats
        '##', // Headers
        '==', // Highlighting in some formats
        '```', // Code blocks
        '>[', // Blockquotes
    ];

    // Check for rich text markers
    for (final marker in richTextMarkers) {
        if (text.contains(marker)) {
            return true;
        }
    }

    return false;
}
String markdownToDelta(String markdown) {
    if (markdown.isEmpty) return '';

    final Map<String, dynamic> delta = {
        'ops': <Map<String, dynamic>>[]
    };

    // Split markdown into lines
    final lines = markdown.split('\n');

    for (var line in lines) {
        var attributes = <String, dynamic>{};
        var text = line;

        // Handle bold
        if (text.contains('**')) {
            text = text.replaceAll('**', '');
            attributes['bold'] = true;
        }

        // Handle italic
        if (text.contains('*')) {
            text = text.replaceAll('*', '');
            attributes['italic'] = true;
        }

        // Handle headers
        if (text.startsWith('##')) {
            text = text.replaceAll('##', '').trim();
            attributes['header'] = 2;
        }

        // Add the operation to delta
        delta['ops'].add({
            'insert': text,
            if (attributes.isNotEmpty) 'attributes': attributes,
        });
    }

    return jsonEncode(delta);
}

String deltaToWysiwyg(String deltaString) {
  if (deltaString.isEmpty) return '';
  try {
    final delta = jsonDecode(deltaString);
    final converter = QuillDeltaToHtmlConverter(
      List<Map<String, dynamic>>.from(delta['ops']),
      ConverterOptions(
        multiLineBlockquote: true,
        multiLineHeader: true,
        multiLineCodeblock: true,
      ),
    );
    
    return converter.convert();
  } catch (e) {
    return deltaString;
  }
}

String quilltoHtml(q.QuillController quillController) {
  // Retrieve the Delta from the controller
  final delta = quillController.document.toDelta();
// Convert Delta to a JSON-compatible format
  final deltaJson = delta.toJson();
// Create an instance of the converter
  final converter = QuillDeltaToHtmlConverter(
    deltaJson,
  );
// Perform the conversion
  return converter.convert();
}
