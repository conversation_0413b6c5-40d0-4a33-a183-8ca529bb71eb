// To parse this JSON data, do
//
//     final kittySettings = kittySettingsFromJson(jsonString);

import 'dart:convert';

KittySettings kittySettingsFromJson(String str) =>
    KittySettings.fromJson(json.decode(str));

String kittySettingsToJson(KittySettings data) => json.encode(data.toJson());

class KittySettings {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int? kittyId;
  num? minimumAmount;
  num? maximumAmount;
  bool? hideAmount;
  bool? hideNames;
  dynamic historyLimit;
  String? groupBy;
  String? beneficiarySplitConfig;
  double? tarrifWithdrawPercentage;
  DateTime? startDate;
  String? customMessage;
  dynamic signatoryThreshold;

  String? paymentRefLabel;
  bool? hasMembership;

  KittySettings({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.kittyId,
    this.minimumAmount,
    this.maximumAmount,
    this.hideAmount = false,
    this.hideNames = false,
    this.historyLimit,
    this.groupBy,
    this.beneficiarySplitConfig,
    this.tarrifWithdrawPercentage,
    this.startDate,
    this.customMessage,
    this.signatoryThreshold,
  
    this.hasMembership,
    this.paymentRefLabel
  });

  factory KittySettings.fromJson(Map<String, dynamic> json) => KittySettings(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        kittyId: json["kitty_id"],
        minimumAmount: num.tryParse(json["minimum_amount"].toString()),
        maximumAmount: num.tryParse(json["maximum_amount"].toString()),
        hideAmount: json["hide_amount"] ?? false,
        hideNames: json["hide_names"] ?? false,
        historyLimit: json["history_limit"],
        groupBy: json["group_by"] ?? '',
        beneficiarySplitConfig: json["beneficiary_split_config"] ?? '',
        tarrifWithdrawPercentage: json["tarrif_withdraw_percentage"],
        startDate: json["start_date"] == null
            ? null
            : DateTime.parse(json["start_date"]),
        customMessage: json["custom_message"] ?? '',
        signatoryThreshold: json["signatory_threshold"],
        
        hasMembership: json['has_membership'],
        paymentRefLabel: json['payment_ref_label']
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "kitty_id": kittyId,
        "minimum_amount": minimumAmount,
        "maximum_amount": maximumAmount,
        "hide_amount": hideAmount,
        "hide_names": hideNames,
        "history_limit": historyLimit,
        "group_by": groupBy,
        "beneficiary_split_config": beneficiarySplitConfig,
        "tarrif_withdraw_percentage": tarrifWithdrawPercentage,
        "start_date": startDate,
        "custom_message": customMessage,
        "signatory_threshold": signatoryThreshold,
        "has_membership" : hasMembership,
        "payment_ref_label" : paymentRefLabel
      };
}
