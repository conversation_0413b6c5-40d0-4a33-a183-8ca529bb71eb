import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart' as path;
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:onekitty/utils/asset_urls.dart';

class CustomVideoPlayer extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final bool showControls;
  final BoxFit fit;

  const CustomVideoPlayer({
    super.key,
    required this.url,
    this.width,
    this.height,
    this.showControls = true,
    this.fit = BoxFit.contain,
  });

  @override
  State<CustomVideoPlayer> createState() => _CustomVideoPlayerState();
}

class _CustomVideoPlayerState extends State<CustomVideoPlayer> {
  late VideoPlayerController _videoPlayerController;
  bool _isInitialized = false;
  bool _isFullScreen = false;
  bool _isPlaying = false;
  bool _isControlsVisible = false;
  String? _cachedVideoPath;
  bool _hasError = false;
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
    super.dispose();
  }

  String _generateMd5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }

  Future<String?> _getCachedVideoPath() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final fileName = '${_generateMd5(widget.url)}.mp4';
      final file = File(path.join(cacheDir.path, fileName));
      return await file.exists() ? file.path : null;
    } catch (e) {
      if (mounted) setState(() {_hasError = true; _isLoading = false;});
      return null;
    }
  }

  Future<void> _cacheVideo(String url) async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final fileName = '${_generateMd5(url)}.mp4';
      final filePath = path.join(cacheDir.path, fileName);
      final file = File(filePath);
      if (!await file.exists()) {
        _cachedVideoPath = filePath;
      } else {
        _cachedVideoPath = filePath;
      }
    } catch (e) {
      if (mounted) setState(() => _hasError = true);
    }
  }

  Future<void> _initializePlayer() async {
    setState(() => _isLoading = true);
    try {
      final cachedPath = await _getCachedVideoPath();
      
      if (cachedPath != null) {
        _videoPlayerController = VideoPlayerController.file(File(cachedPath));
      } else {
        _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.url));
        _cacheVideo(widget.url);
      }

      await _videoPlayerController.initialize();
      
      if (mounted && _videoPlayerController.value.isInitialized) {
        // Add listener to update UI based on player state
        _videoPlayerController.addListener(_videoListener);
        
        // Add listener to show controls temporarily when video is tapped
        if (widget.showControls) {
          setState(() {
            _isControlsVisible = true;
            Future.delayed(const Duration(seconds: 3), () {
              if (mounted && _isControlsVisible) {
                setState(() {
                  _isControlsVisible = false;
                });
              }
            });
          });
        }
        
        setState(() {
          _isInitialized = true;
          _isLoading = false;
        });
      } else if (mounted) {
        setState(() {_hasError = true; _isLoading = false;});
      }
    } catch (e) {
      if (mounted) setState(() {_hasError = true; _isLoading = false;});
    }
  }
  
  void _videoListener() {
    if (!mounted) return;
    
    final bool isPlaying = _videoPlayerController.value.isPlaying;
    
    // Update internal state if needed
    if (_isPlaying != isPlaying) {
      setState(() {
        _isPlaying = isPlaying;
      });
      
      // Auto-exit fullscreen when video ends
      if (!isPlaying && _isFullScreen && 
          _videoPlayerController.value.position >= _videoPlayerController.value.duration) {
        _exitFullScreen();
      }
    }
  }

  void _enterFullScreen() {
    if (!_isFullScreen) {
      setState(() => _isFullScreen = true);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  void _exitFullScreen() {
    if (_isFullScreen) {
      setState(() => _isFullScreen = false);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
  }

  void _togglePlayPause() {
    if (!_isInitialized || _hasError) return;
    
    setState(() {
      if (_videoPlayerController.value.isPlaying) {
        _videoPlayerController.pause();
      } else {
        _videoPlayerController.play();
      }
      _isPlaying = !_isPlaying;
      
      // Show controls briefly when toggling play state
      if (widget.showControls) {
        _isControlsVisible = true;
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _isControlsVisible) {
            setState(() {
              _isControlsVisible = false;
            });
          }
        });
      }
    });
  }

  void _toggleControls() {
    if (!widget.showControls) return;
    
    setState(() {
      _isControlsVisible = !_isControlsVisible;
      
      // Auto-hide controls after a few seconds
      if (_isControlsVisible) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _isControlsVisible) {
            setState(() {
              _isControlsVisible = false;
            });
          }
        });
      }
    });
  }

  void _playFromThumbnail() {
    if (!_isInitialized || _hasError) return;
    
    _videoPlayerController.play();
    setState(() {
      _isPlaying = true;
      
      // Show controls briefly
      if (widget.showControls) {
        _isControlsVisible = true;
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _isControlsVisible) {
            setState(() {
              _isControlsVisible = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) return _buildErrorWidget();
    if (_isLoading || !_isInitialized) return _buildLoadingWidget();
    return _buildPlayerUI();
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 200.h,
      color: Colors.black,
      child: Center(
        child: Image.asset(
          AssetUrl.launcher,
          width: 80.w,
          height: 80.w,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 200.h,
      color: Colors.black,
      child: Center(
        child: Icon(Icons.error_outline, color: Colors.red.shade400, size: 40.w),
      ),
    );
  }

  Widget _buildPlayerUI() {
    final containerWidth = _isFullScreen ? MediaQuery.of(context).size.width : (widget.width ?? double.infinity);
    final containerHeight = _isFullScreen ? MediaQuery.of(context).size.height : (widget.height ?? 200.h);

    return WillPopScope(
      onWillPop: () async {
        if (_isFullScreen) {
          _exitFullScreen();
          return false; 
        }
        return true;
      },
      child: Container(
        width: containerWidth,
        height: containerHeight,
        color: Colors.black,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Main Video Player or Thumbnail
            GestureDetector(
              onTap: _isPlaying ? _toggleControls : _playFromThumbnail,
              behavior: HitTestBehavior.opaque,
              child: Center(
                child: AspectRatio(
                  aspectRatio: _isFullScreen 
                      ? MediaQuery.of(context).size.aspectRatio 
                      : _videoPlayerController.value.aspectRatio,
                  child: _isPlaying 
                      ? VideoPlayer(_videoPlayerController)
                      : _buildThumbnailView(),
                ),
              ),
            ),
            
            // Progress Indicator
            if (_isPlaying && widget.showControls)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: AnimatedOpacity(
                  opacity: _isControlsVisible ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    padding: EdgeInsets.only(bottom: 8.h),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.5),
                        ],
                      ),
                    ),
                    child: VideoProgressIndicator(
                      _videoPlayerController,
                      allowScrubbing: true,
                      colors: const VideoProgressColors(
                        playedColor: Colors.blue,
                        bufferedColor: Colors.grey,
                        backgroundColor: Colors.black45,
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    ),
                  ),
                ),
              ),
            
            // Play/Pause Button Overlay
            if (widget.showControls)
              AnimatedOpacity(
                opacity: _isControlsVisible ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: GestureDetector(
                  onTap: _togglePlayPause,
                  child: Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                      size: 30.w,
                    ),
                  ),
                ),
              ),
            
            // Fullscreen Button
            if (widget.showControls)
              Positioned(
                bottom: 10,
                right: 10,
                child: AnimatedOpacity(
                  opacity: _isControlsVisible ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: _buildFullscreenButton(_isFullScreen),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailView() {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (_videoPlayerController.value.isInitialized)
          AspectRatio(
            aspectRatio: _videoPlayerController.value.aspectRatio,
            child: VideoPlayer(_videoPlayerController),
          )
        else 
          Container(color: Colors.black.withOpacity(0.5)),
          
        Container(color: Colors.black.withOpacity(0.4)),
        Icon(
          Icons.play_arrow_rounded,
          size: 60.w,
          color: Colors.white.withOpacity(0.9),
        ),
      ],
    );
  }

  Widget _buildFullscreenButton(bool isFullscreen) {
    return IconButton(
      icon: Icon(
        isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
        color: Colors.white,
        size: 28,
      ),
      onPressed: isFullscreen ? _exitFullScreen : _enterFullScreen,
      style: IconButton.styleFrom(
        backgroundColor: Colors.black.withOpacity(0.5),
        padding: const EdgeInsets.all(8),
      ),
      tooltip: isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen',
    );
  }
}