// ignore_for_file: control_flow_in_finally
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:mime/mime.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/contr_kitty_model.dart';
import 'package:onekitty/models/kitty/kitty_categories_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/kitty_payload.dart';
import 'package:onekitty/models/auth/regDTO_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/whatsapp_error_dialog.dart';

class KittyController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString whatsappApiMessage = ''.obs;
  RxString errorMessage = ''.obs;
  RxString reasons = "".obs;
  RxInt currentPage = 0.obs;
  RxString whatsappnumber = KtStrings.phoneNumberOnekitty.obs;
  Rx<NewUser> user = NewUser().obs;
  final balance = 0.0.obs;
  final thirdPartyCharges = 0.0.obs;
  final charges = 0.0.obs;
  final totalAmount = 0.0.obs;
  final isFetchingBalance = false.obs;
  RxString urlKit = ''.obs;
  RxString insta = ''.obs;
  RxBool isloading = false.obs;
  RxBool isLinkloading = false.obs;
  RxBool isNameloading = false.obs;
  RxBool isEditEndDateloading = false.obs;
  RxBool isEditloading = false.obs;
  RxBool loadingTransactions = false.obs;
  RxList<TransactionModel> transactionsKitty = <TransactionModel>[].obs;
  RxList bannerList = [].obs;

  RxList<TransactionModel> filtrtransactions = <TransactionModel>[].obs;
  RxBool loadingfiltrTransactions = false.obs;
  // Rx<Results?> results = Results(items: []).obs;
  Rx<Results?> results = Results().obs;

  RxBool status = false.obs;
  RxBool whtsappStatus = false.obs;
  RxBool loadingWithdraw = false.obs;

  RxString apiMessageProcess = ''.obs;
  RxString beneficiaryName = ''.obs;
  RxString beneficiaryNumber = ''.obs;
  RxString reciveChannel = ''.obs;
  RxString benefAccRef = ''.obs;
  RxString reciveAmount = ''.obs;
  RxString whatsappStatus = ''.obs;
  RxMap withdrawData = {}.obs;

  RxString textmessage = ''.obs;
  final isUploadingImage = false.obs;
  Rx<Kitty> kittCreated = Kitty().obs;
  final media = <KittyMediaModel>[].obs;
  RxMap socials = {}.obs;
  ContributeController singleKitty = Get.put(ContributeController());

  final ImagePicker _picker = ImagePicker();

  // Kitty categories
  RxList<KittyCategoriesModel> kittyCategories = <KittyCategoriesModel>[].obs;
  RxBool isLoadingKittyCategories = false.obs;
  final RxList<int> selectedKittyCategories = <int>[].obs;
  final RxList<KittyCategoriesModel> selectedKittyCategoryModels = <KittyCategoriesModel>[].obs;
  final RxList<KittyCategoriesModel> filteredKittyCategories = <KittyCategoriesModel>[].obs;
  final RxString categorySearchQuery = ''.obs;

  void toggleCategorySelection(KittyCategoriesModel category) {
    final isSelected = selectedKittyCategoryModels.any((c) => c.id == category.id);
    if (isSelected) {
      selectedKittyCategoryModels.removeWhere((c) => c.id == category.id);
      selectedKittyCategories.remove(category.id);
    } else if (selectedKittyCategoryModels.length < 3) {
      selectedKittyCategoryModels.add(category);
      selectedKittyCategories.add(category.id!);
    }
    update();
  }

  void filterCategories(String query) {
    categorySearchQuery.value = query;
    if (query.isEmpty) {
      filteredKittyCategories.assignAll(kittyCategories);
    } else {
      filteredKittyCategories.assignAll(
        kittyCategories.where((category) => 
          category.name?.toLowerCase().contains(query.toLowerCase()) ?? false
        ).toList()
      );
    }
    update();
  }

  bool isCategorySelected(KittyCategoriesModel category) {
    return selectedKittyCategoryModels.any((c) => c.id == category.id);
  }

  bool canSelectMoreCategories() {
    return selectedKittyCategoryModels.length < 3;
  }

  void clearSelectedCategories() {
    selectedKittyCategoryModels.clear();
    selectedKittyCategories.clear();
    update();
  }

  //Rx<Kitty> kitty = Kitty().obs;
  @override
  void onInit() {
    getLocalUser();
    super.onInit();
  }

  NewUser? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(NewUser.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  Future fetchBalance(double amount,
      {required Map<String, dynamic> data}) async {
    try {
      isFetchingBalance(true);
      balance.value = 0.0;
      charges.value = 0.0;
      totalAmount.value = 0.0;
      var resp = await apiProvider.request(
        url: ApiUrls.fetchBalance,
        method: Method.POST,
        params: data,
      );
      if (resp.data['status'] ?? false) {
        balance(
            double.tryParse(resp.data['data']['balance_charges'].toString()));
        charges(double.tryParse(resp.data['data']['charges'].toString()));
        totalAmount(
            double.tryParse(resp.data['data']['kitty_balance'].toString()));
        thirdPartyCharges(double.tryParse(
            resp.data['data']['third_party_charges'].toString()));
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      return false;
    } finally {
      isFetchingBalance(false);
    }
  }

  Future<bool> createKitty({required CreateKitPayload payload}) async {
    isloading(true);
    update();
    // try {
      var resp = await apiProvider.request(
        url: ApiUrls.create_kitty,
        method: Method.POST,
        params: payload.toJson(),
      );
      apiMessage(resp.data["message"]);
      whatsappApiMessage(resp.data["whatsapp_message"]);
      whtsappStatus(resp.data["whatsapp_status"]);
      logger.log(Level.debug, resp.data);
      if (resp.data["whatsapp_data"] != null) {
        if (resp.data["whatsapp_data"]['whatsapp_number'] != null) {
          whatsappnumber(resp.data["whatsapp_data"]['whatsapp_number']);
        }
        if (resp.data["whatsapp_data"]['reasons'] != null) {
          reasons(resp.data["whatsapp_data"]['reasons']);
        }
      }
      if (resp.data['status'] ?? false) {
        kittCreated(Kitty.fromJson(resp.data["data"]["kitty"]));
        socials(resp.data["social_mesages"]);
        urlKit(resp.data["data"]['url']);
        insta(resp.data["social_mesages"]["instagram"]);
      } else {
        Get.snackbar('Error', resp.data['message'] ?? 'couldnt create kitty',
            backgroundColor: Colors.red);
      }
      isloading(false);
      update();

      return resp.data['status'];
    // } catch (e) {

    //   logger.e(e);
    //   isloading(false);
    //   apiMessage('An error occured');
    //   rethrow;
    //   return false;
    // }
  }

  Future<bool?> pickImage(
      {required int kittyId,
      required String name,
      required BuildContext context}) async {
    isUploadingImage(true);
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
      );

      if (image != null) {
        final _eventsController = Get.find<CreateEventController>();
        final file = image.path;
        final fileSize = await File(file).length();
        final fileSizeInMB = fileSize / (1024 * 1024);
        final maxSize = Get.find<GlobalControllers>().imageSize;

        final String mimeType = lookupMimeType(file) ?? '';
        if (!mimeType.startsWith('image/')) {
          ToastUtils.showToast('Please select an image file');
          isUploadingImage(false);
          return false;
        }

        if (fileSizeInMB <= maxSize) {
          final url = await _eventsController.uploadFile(
              path: file,
              fileName:
                  "${DateTime.now().millisecondsSinceEpoch}${file.split(RegExp(r'[/\\]')).last}");
          final title = "$name-${DateTime.now().millisecondsSinceEpoch}";
          var resp = await apiProvider.request(
            url: ApiUrls.create_kitty_media,
            method: Method.POST,
            params: {
              "url": url,
              "title": title,
              "type": "IMAGE",
              "category": "KITTY",
              "kitty_id": kittyId,
            },
          );

          if (resp.data['status'] == true) {
            singleKitty.kittyMedia.add(KittyMediaModel(
                url: url, type: "image", title: title, status: 'ACTIVE'));
            apiMessage(resp.data["message"]);
            return true;
          } else {
            ToastUtils.showToast(resp.data["message"] ?? 'Upload failed');
            return false;
          }
        } else {
          ToastUtils.showToast(
              'Image size must be ${maxSize}MB or less (Current size: ${fileSizeInMB.toStringAsFixed(1)}MB)');
          return false;
        }
      } else {
        ToastUtils.showToast('No image selected');
        return false;
      }
    } catch (e) {
      logger.e('General error in pickImage: $e');
      ToastUtils.showToast('Failed to process image. Please try again.');
      return false;
    } finally {
      isUploadingImage(false);
    }
  }

  Future<void> deleteMedia(int mediaId, {int? pos}) async {
    var res = await apiProvider.request(
        url: "${ApiUrls.create_kitty_media}$mediaId", method: Method.DELETE);
    if (res.data["status"] ?? false) {
      singleKitty.kittyMedia.removeAt(pos ?? 0);
    } else {}
  }

  Future<bool> updateKitty({required CreateKitPayload request}) async {
    isEditloading(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.update_kitty,
        method: Method.POST,
        params: request.toJson(),
      );

      apiMessage(resp.data["message"]);
      //errorMessage(resp.data["data"]["errors"]["title"]);

      if (resp.data['status'] ?? false) {
        kittCreated(Kitty.fromJson(resp.data["data"]['kitty']));
        urlKit(resp.data["data"]['url']);
      }
      isEditloading(false);
      // Get.snackbar('', '${resp.data}');
      update();
      return resp.data['status'];
    } catch (e) {
      logger.e(e);
      isEditloading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  updateEndDate({required DateTime newDate, required int KittyId}) async {
    isEditEndDateloading(true);
    update();
    try {
      var resp = await apiProvider.request(
          url: ApiUrls.updateEndDate,
          method: Method.POST,
          params: {
            "kitty_id": KittyId,
            "end_date": newDate.toIso8601String().removeAllWhitespace
          });
      logger.w(resp);
      apiMessage(resp.data["message"]);
      status(resp.data["status"]);
      isEditEndDateloading(false);
      update();

      return resp.data["status"];
    } catch (e) {
      logger.e(e);
      isEditEndDateloading(false);
      apiMessage("Error, Please try again later");
      update();

      return false;
    }
  }

  withdrawRequest({
    required String amount,
    required int kittyId,
    required bool isconfirm,
    required int benficiaryId,
    required String remarks,
  }) async {
    loadingWithdraw(true);
    update();
    try {
      var resp = await apiProvider.request(
          url: isconfirm ? ApiUrls.withdrawConfirm : ApiUrls.withdrawRequest,
          method: Method.POST,
          params: {
            "beneficiary_id": benficiaryId,
            "phone_number": getLocalUser()?.phoneNumber ?? "",
            "kitty_id": kittyId.toString(),
            "amount": amount,
            "reason": remarks
          });
      apiMessage(resp.data["message"]);
      status(resp.data["status"]);
      if (resp.data["status"] && !isconfirm) {
        var dat = resp.data["data"];
        beneficiaryName(dat["beneficiary_name"]);
        beneficiaryNumber(dat["beneficiary_account"]);
        reciveAmount(dat["amount_received"].toString());
        reciveChannel(dat["channel"]);
        //benefAccRef(dat["beneficiary_account_ref"]);
      }
      if (status.isFalse) {
        ToastUtils.showToast(apiMessage.string, toastType: ToastType.error);
      }
      loadingWithdraw(false);
      update();
    } catch (e) {
      loadingWithdraw(false);
      logger.e(e);
      apiMessage("Error!,Please try again");
      update();
    }
  }

  Future<bool> joinGroup(
      {required BuildContext context,
      required int id,
      required String link}) async {
    isLinkloading(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.join_group,
        method: Method.POST,
        params: {"kitty_id": id, "group_link": link},
      );

      if (!context.mounted) return false;
      apiMessage(res.data["message"]);
      if (res.data["data"] != null) {
        if (res.data["data"]["whatsapp_number"] != null) {
          whatsappnumber(res.data["data"]["whatsapp_number"]);
        }
        if (res.data["data"]["reasons"] != null) {
          reasons(res.data["data"]["reasons"]);
        }
      }
      if (res.data["status"]) {
        await Get.find<ContributeController>().getWhatsapp(
            id: Get.put(DataController()).kitty.value.kitty?.iD ?? 0);

        ToastUtils.showInfoToast(context, res.data["message"], "Success");
        apiMessage(res.data["message"]);
        Navigator.pop(context);
      } else {
        // Get.defaultDialog(
        //     content: WhatsAppErrorDialog(
        //   msg: res.data['message'],
        //   whatsappNo: whatsappnumber.value,
        //   rsn: reasons.string,
        // ));
        Get.to(() => ErrorPage(
              link: link,
              reasons: reasons.string,
              messages: res.data['message'],
              whatsAppNo: whatsappnumber.value,
            ),);
        ToastUtils.showErrorToast(context, "Error", "${res.data['message']}");
        return false;
      }
      update();
      return true;
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      isLinkloading(false);
      update();
    } finally {
      isLinkloading(false);
      return false;
    }
  }

  // /// Get the matching network codes of each service provider
  int? getNetworkCode({required String networkTitle}) {
    //remove spaces, dashes,fullstops,trim the string then to lowercase
    String netw = networkTitle
        .replaceAll(" ", "")
        .replaceAll("-", "")
        .replaceAll(".", "")
        .trim()
        .toLowerCase();

    switch (netw) {
      case "sasapay":
        return 0;
      case "mpesa":
        return 63902;
      case "airtelmoney":
        return 63903;
      case "visa":
        return 55;
      default:
        return null;
    }
  }

  String getNetworkName({required String code}) {
    //remove spaces, dashes,fullstops,trim the string then to lowercase

    switch (code) {
      case "0":
        return "Sasapay";
      case "63902":
        return "M-Pesa";
      case "63903":
        return "Airtel Money";
      case "63907":
        return "T-kash";
      default:
        return "UNKNOWN";
    }
  }

  getKittyContributions(
      {required int kittyId,
      int? page = 0,
      int? size = 20,
      int? eventId}) async {
    update();
    loadingTransactions(true);
    try {
      update();

      var resp = await apiProvider.request(
        url: eventId != null
            ? "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&size=$size&page=$page"
            : "${ApiUrls.contribsTransactions}?kitty_id=$kittyId&page=$page&size=$size",
        method: Method.GET,
      );
      if (resp.data["status"]) {
        Results fetchedResults = eventId != null
            ? Results.fromJson(resp.data["data"])
            : Results.fromJson(resp.data["data"]["results"]);
        transactionsKitty([]);
        if (eventId != null) {
          for (var element in resp.data["data"]["items"] ?? []) {
            transactionsKitty.add(TransactionModel.fromJson(element));
          }
        } else {
          for (var element in resp.data["data"]["results"]["items"] ?? []) {
            transactionsKitty.add(TransactionModel.fromJson(element));
          }
        }
        fetchedResults.items = transactionsKitty;
        results.value = fetchedResults;
        // transactionsKitty([]);
        // for (var element in resp.data["data"]["results"]["items"]) {
        //   transactionsKitty.add(TransactionModel.fromJson(element));
        // }
      } else {
        transactionsKitty([]);
      }
      update();

      loadingTransactions(false);
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      loadingTransactions(false);
    }
    update();
  }

  getKittyFiltrContributions(
      {required int kittyId,
      int? eventId,
      int? page = 1,
      int? size = 100,
      String? startDate,
      String? endDate,
      String? code,
      String? search,
      String? phoneNumber}) async {
    update();
    loadingfiltrTransactions(true);
    try {
      update();
      // Common base at this point
      String url = eventId != null
          ? "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId"
          : "${ApiUrls.filterContribs}?kitty_id=$kittyId";

      // Checking if startDate and endDate are both provided
      if (!(startDate?.isEmpty ?? true) && !(endDate?.isEmpty ?? true)) {
        url += "&start-date=$startDate&end-date=$endDate";
      } else if (!(phoneNumber?.isEmpty ?? true)) {
        url += "&phone_number=254$phoneNumber";
      } else if (!(code?.isEmpty ?? true)) {
        url += "&transaction_code=$code";
      } else if (!(search?.isEmpty ?? true)) {
        url += "&search=$search";
      }

      var resp = await apiProvider.request(
        url: url,
        method: Method.GET,
      );

      if (resp.statusCode == 200) {
        filtrtransactions([]);
        if (eventId != null) {
          for (var element in resp.data["data"]["items"]) {
            filtrtransactions.add(TransactionModel.fromJson(element));
          }
        } else {
          for (var element in resp.data["data"]["results"]["items"]) {
            filtrtransactions.add(TransactionModel.fromJson(element));
          }
        }
      } else {}
      loadingfiltrTransactions(false);
    } catch (e) {
      loadingfiltrTransactions(false);
      throw e;
    }
  }

  Future<bool> shareKittyTrans({required int id}) async {
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.getKittyText}/$id',
        method: Method.GET,
      );

      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        textmessage(res.data["data"]["whatsapp_message"]);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      logger.e(e);
      return false;
    }
  }

  Future<void> getKittyCategories() async {
    try {
      kittyCategories.clear();
      kittyCategories([]);
      isLoadingKittyCategories(true);
      final httpService = Get.find<HttpService>(); 
      final response = await httpService.request(
        url: ApiUrls.kitty_categories,
        method: Method.GET,
      );
      if (response.data['status'] ?? false) {
        final _returneddata = response.data['data']['categories'] as List;
        kittyCategories = _returneddata
            .map((item) {
              return KittyCategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
        filteredKittyCategories.assignAll(kittyCategories);
      } else {
        logger.v('No data or unexpected response structure');
      }
      isLoadingKittyCategories(false);
    } catch (e) {
      logger.e('Error fetching kitty categories: $e');
      isLoadingKittyCategories(false);
    }
  }
}

class KittyDataController extends GetxController {
  Rx<KittyData> singleKitty = KittyData().obs;
}

class LinkController extends GetxController {
  Rx<CreateKittyResponse> kittyResponse = CreateKittyResponse().obs;
  var whatsappStatus = RxBool(false);
}