import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/whatsapp_validator.dart';
import 'package:onekitty/widgets/whatsapp_groups/whatsapp_group_type.dart';

/// Unified Add WhatsApp Group Dialog
class AddWhatsAppGroupDialog extends StatefulWidget {
  final WhatsAppGroupType type;
  final int? entityId;
  final String? entityTitle;
  final VoidCallback? onSuccess;

  const AddWhatsAppGroupDialog({
    super.key,
    required this.type,
    this.entityId,
    this.entityTitle,
    this.onSuccess,
  });

  @override
  State<AddWhatsAppGroupDialog> createState() => _AddWhatsAppGroupDialogState();
}

class _AddWhatsAppGroupDialogState extends State<AddWhatsAppGroupDialog> {
  final formKey = GlobalKey<FormState>();
  final linkController = TextEditingController();
  final emailController = TextEditingController();
  
  bool isLoading = false;

  // Controllers
  KittyController? kittyController;
  ChamaController? chamaController;
  ChamaDataController? chamaDataController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    switch (widget.type) {
      case WhatsAppGroupType.kitty:
      case WhatsAppGroupType.event:
        kittyController = Get.put(KittyController());
        break;
      case WhatsAppGroupType.chama:
        chamaController = Get.put(ChamaController());
        chamaDataController = Get.put(ChamaDataController());
        break;
    }
  }

  @override
  void dispose() {
    linkController.dispose();
    emailController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (!formKey.currentState!.validate() || widget.entityId == null) return;

    setState(() {
      isLoading = true;
    });

    try {
      bool success = false;
      String message = '';

      switch (widget.type) {
        case WhatsAppGroupType.kitty:
        case WhatsAppGroupType.event:
          if (kittyController != null) {
            final normalizedLink = WhatsAppValidator.normalizeWhatsAppLink(
              linkController.text.trim(),
            );
            success = await kittyController!.joinGroup(
              context: context,
              id: widget.entityId!,
              link: normalizedLink,
            );
            message = success ? 'WhatsApp group connected successfully' : 'Failed to connect WhatsApp group';
          }
          break;
        case WhatsAppGroupType.chama:
          if (chamaController != null) {
            success = await chamaController!.addGroup(
              chId: widget.entityId!,
              link: linkController.text.trim(),
              email: emailController.text.trim(),
            );
            message = chamaController!.apiMessage.string;
          }
          break;
      }

      if (success) {
        if (mounted) {
          ToastUtils.showSuccessToast(context, message, "Success");
          Navigator.of(context).pop();
          widget.onSuccess?.call();
        }
      } else {
        if (mounted) {
          ToastUtils.showErrorToast(context, message, "Error");
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showErrorToast(context, 'An error occurred: $e', "Error");
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        padding: const EdgeInsets.all(20),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                "Connect WhatsApp Group",
                style: context.titleText?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.entityTitle != null) ...[
                const SizedBox(height: 8),
                Text(
                  widget.entityTitle!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
              const SizedBox(height: 20),

              // WhatsApp Link Field
              Text(
                "WhatsApp group link",
                style: context.titleText,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                labelText: "Enter WhatsApp link",
                hintText: widget.type == WhatsAppGroupType.chama
                    ? "e.g https://chat.whatsapp.com/K8jDByxIyv4HLjCn32bEp0"
                    : "Enter WhatsApp link",
                controller: linkController,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Provide a link";
                  } else if (!WhatsAppValidator.isValidWhatsAppLink(value)) {
                    return WhatsAppValidator.getValidationErrorMessage();
                  }
                  return null;
                },
              ),

              // Email Field (only for Chama)
              if (widget.type == WhatsAppGroupType.chama) ...[
                const SizedBox(height: 16),
                Text(
                  "Chama Email",
                  style: context.titleText,
                ),
                const SizedBox(height: 8),
                CustomTextField(
                  controller: emailController,
                  hintText: "e.g <EMAIL>",
                  labelText: "Enter Your Chama Email",
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Email cannot be empty";
                    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return "Enter a valid email address";
                    }
                    return null;
                  },
                ),
              ],

              const SizedBox(height: 24),

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: isLoading ? null : () => Navigator.of(context).pop(),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Text(
                        "Cancel",
                        style: context.dividerTextLarge?.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  ),
                  CustomKtButton(
                    width: 100.w,
                    isLoading: isLoading,
                    onPress: _handleSubmit,
                    btnText: "Submit",
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}