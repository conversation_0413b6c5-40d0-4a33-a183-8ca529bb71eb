import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/controller/notification_controller.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/notifications.dart';

class NotificationIconWithPulse extends StatefulWidget {
  const NotificationIconWithPulse({super.key});

  @override
  _NotificationIconWithPulseState createState() => _NotificationIconWithPulseState();
}

class _NotificationIconWithPulseState extends State<NotificationIconWithPulse> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(_animationController);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Obx(() {
        final hasUnreadNotifications = Get.put(NotificationController()).hasUnreadNotifications();
        return Stack(
          alignment: Alignment.center,
          children: [
            // Three-layer pulse rings
            if (hasUnreadNotifications)
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer ring (lightest)
                      Transform.scale(
                        scale: _animation.value,
                        child: Container(
                          width: 48.w,
                          height: 48.h,
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1), // Lightest ring
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      // Middle ring
                      Transform.scale(
                        scale: _animation.value,
                        child: Container(
                          width: 36.w,
                          height: 36.h,
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.3), // Medium opacity
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      // Inner ring (darkest)
                      Transform.scale(
                        scale: _animation.value,
                        child: Container(
                          width: 24.w,
                          height: 24.h,
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.5), // Darkest ring
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            // Notification icon
            IconButton(
              icon: Icon(
                Icons.notifications,
                color: Get.isDarkMode ? Colors.white : Colors.black,
              ),
              onPressed: () => Get.to(() => const NotificationsView()),
            ),
          ],
        );
      }),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}