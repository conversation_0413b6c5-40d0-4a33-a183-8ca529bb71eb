import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';

import '../../../../utils/utils_exports.dart';

class CrtChama extends StatefulWidget {
  const CrtChama({super.key});

  @override
  State<CrtChama> createState() => _CrtChamaState();
}

class _CrtChamaState extends State<CrtChama> {
  final ChamaController _chamaController = Get.put(ChamaController());
  @override
  void initState() {
    _chamaController.getConfigs();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(
          horizontal: 25.w,
          vertical: 42.h,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              CustomImageView(
                color: Colors.blue,
                imagePath: AssetUrl.imgGroup10,
                height: 265.h,
                width: 366.w,
              ),
              SizedBox(height: 30.h),
              Text(
                "Ready To Grow Together?",
                style: CustomTextStyles.titleLargeGray900,
              ),
              SizedBox(height: 7.h),
              Container(
                width: 350.w,
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  "Start your chama, build wealth, and support each other.",
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                ),
              ),
              SizedBox(height: 20.h),
              CustomElevatedButton(
                onPressed: () {
                  Get.toNamed(NavRoutes.chamaStepper);
                },
                text: "Create a chama",
                buttonTextStyle: CustomTextStyles.titleSmallWhiteA700,
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }
}
