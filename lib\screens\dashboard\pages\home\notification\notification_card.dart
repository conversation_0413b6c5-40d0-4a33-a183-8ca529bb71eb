
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/model/notification_model.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/notifications_details.dart';

import 'controller/notification_controller.dart';

class NotificationCard extends StatelessWidget {
  final AppNotification notification;

  const NotificationCard({super.key, required this.notification});

  Color _getTypeColor(String? type) {
    switch (type) {
      case 'delegate_added':
        return Colors.blue;
      case 'transaction_out':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String? type) {
    switch (type) {
      case 'delegate_added':
        return Icons.admin_panel_settings;
      case 'transaction_out':
        return Icons.payment;
      default:
        return Icons.notifications;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(15),
        ),
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 20.w),
        child: Icon(Icons.delete, color: Colors.red, size: 24.w),
      ),
      onDismissed: (_) => Get.find<NotificationController>()
          .deleteNotification(notification.id),
      confirmDismiss: (direction) async {
        return await Get.dialog<bool>(
              AlertDialog(
                title: const Text('Delete Notification'),
                content: const Text(
                    'Are you sure you want to delete this notification?'),
                actions: [
                  TextButton(
                    onPressed: () => Get.back(result: false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Get.back(result: true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            ) ??
            false;
      },
      child: OpenContainer(
        closedColor: Theme.of(context).scaffoldBackgroundColor,
        transitionDuration: const Duration(milliseconds: 500),
        openBuilder: (context, _) =>
            NotificationDetailView(notification: notification),
        closedElevation: 0,
        closedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        closedBuilder: (context, openContainer) => Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(15),
            onTap: () {
              Get.find<NotificationController>().markAsRead(notification.id);
              openContainer();
            },
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: notification.read
                    ? null
                    : Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 1,
                      ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: _getTypeColor(notification.type).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getTypeIcon(notification.type),
                      color: _getTypeColor(notification.type),
                      size: 24.w,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                notification.title,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: notification.read
                                      ? FontWeight.normal
                                      : FontWeight.bold,
                                  color: notification.read
                                      ? Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color
                                      : Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ),
                            if (!notification.read)
                              Container(
                                width: 8.w,
                                height: 8.w,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          notification.description ?? notification.subtitle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.color
                                ?.withOpacity(0.7),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 14.w,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.color,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  TimeOfDay.fromDateTime(notification.created)
                                      .format(context),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.color,
                                  ),
                                ),
                              ],
                            ),
                            // if (notification.phoneNumber != null)
                            // if (notification.phoneNumber!.isNotEmpty)
                            //   Row(
                            //     children: [
                            //       Icon(
                            //         Icons.phone,
                            //         size: 14.w,
                            //         color: Theme.of(context)
                            //             .textTheme
                            //             .bodySmall
                            //             ?.color,
                            //       ),
                            //       SizedBox(width: 4.w),
                            //       Text(
                            //         notification.phoneNumber!,
                            //         style: TextStyle(
                            //           fontSize: 12.sp,
                            //           color: Theme.of(context)
                            //               .textTheme
                            //               .bodySmall
                            //               ?.color,
                            //         ),
                            //       ),
                            //     ],
                            //   ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
