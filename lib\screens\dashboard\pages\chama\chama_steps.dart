import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:flutter_contacts/properties/phone.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/widgets/getx_contact_picker.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/models/chama/configs_model.dart';
import 'package:onekitty/controllers/contact_picker_controller.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/iswysiwyg.dart';
import '../../../../utils/utils_exports.dart';
import '../contribution/widgets/date_picker.dart';
import '../contribution/widgets/success.dart';

enum Role {
  admin,
  treasurer,
  secretary,
  member,
}

// Controller class for ChamaStepper
class ChamaStepperController extends GetxController {
  final RxInt currentStep = 0.obs;
  final RxMap<String, Role> roleStatusMap = <String, Role>{}.obs;
  final Rx<Role> selectedStatus = Role.member.obs;
  final RxString selectedRole = "MEMBER".obs;
  final RxString selectedvalue = "".obs;
  final RxList<String> dropdownItems = <String>[].obs;
  final RxList<String> roleItems = <String>[].obs;
  final RxString deviceModel = "".obs;
  final RxString imeiCode = "".obs;
  final RxString invitePhone = "".obs;
  final RxBool isInvite = true.obs;
  final RxBool isFormValid = false.obs;
  
  // Text controllers
  final TextEditingController chamaNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController descrController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController linkController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController freqcyController = TextEditingController();
  final TextEditingController accountrefController = TextEditingController();
  final TextEditingController refController = TextEditingController();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  final TextEditingController timeController = TextEditingController();
  
  // Other properties
  final q.QuillController quillController = q.QuillController.basic();
  final Rx<PhoneNumber> number = PhoneNumber(isoCode: 'KE').obs;
  final Rx<DateTime> combinedDateTime = DateTime.now().obs;
  final countryPicker = const FlCountryCodePicker();
  Rx<CountryCode?> countryCode = Rx<CountryCode?>(null);
  final box = GetStorage();
  
  // Stream controller for timer
  final _streamController = StreamController<void>();
  Stream<void> get stream => _streamController.stream;
  
  @override
  void onInit() {
    super.onInit();
    refController.text = Get.find<GlobalControllers>().getCode();
    initializeData();
  }
  
  @override
  void onClose() {
    _streamController.close();
    chamaNameController.dispose();
    emailController.dispose();
    descrController.dispose();
    amountController.dispose();
    linkController.dispose();
    phoneController.dispose();
    freqcyController.dispose();
    accountrefController.dispose();
    refController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    dateController.dispose();
    timeController.dispose();
    super.onClose();
  }
  
  void initializeData() {
    final chamaController = Get.find<ChamaController>();
    chamaController.getConfigs();
    dateController.text = DateFormat.yMd().format(DateTime.now());
    timeController.text = DateFormat().add_jm().format(DateTime.now());
    roleItems.value = chamaController.roles.map((role) => role.role).toList();
    getfrequency();
    getDeviceInfo();
    startTimer();
  }
  
  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _streamController.add(null);
    });
  }
  
  void getfrequency() {
    final chamaController = Get.find<ChamaController>();
    dropdownItems.value = chamaController.frequencies
        .map((frequency) => frequency.frequency)
        .toList();
    if (chamaController.frequencies.isNotEmpty) {
      selectedvalue.value = chamaController.frequencies.first.frequency;
    }
  }
  
  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      imeiCode.value = androidInfo.id;
      deviceModel.value = androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      imeiCode.value = iosInfo.identifierForVendor!;
      deviceModel.value = iosInfo.model;
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      imeiCode.value = webBrowserInfo.userAgent!;
    }
  }
  
  void updateContact(String id, String newFirstName, String newLastName, String role) {
    final contactController = Get.find<ContactPickerController>();
    final selectedContacts = contactController.selectedContacts;
    
    final contact = selectedContacts.firstWhere(
      (contact) => contact.phones.first.normalizedNumber == id
    );
    contact.name.first = newFirstName;
    contact.name.last = newLastName;
    contact.name.prefix = role;
  }
}

class ChamaStepper extends StatefulWidget {
  const ChamaStepper({super.key});

  @override
  _ChamaStepperState createState() => _ChamaStepperState();
}

class _ChamaStepperState extends State<ChamaStepper> {
  final ChamaStepperController controller = Get.put(ChamaStepperController());
  final ChamaController _chamaController = Get.find<ChamaController>();
  final UserKittyController _userController = Get.find<UserKittyController>();
  final ContactPickerController contactController = Get.put(ContactPickerController());

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey1 = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey2 = GlobalKey<FormState>();
  final GlobalKey _tooltipKey1 = GlobalKey();
  final GlobalKey _tooltipKey2 = GlobalKey();
  final GlobalKey _tooltipKey3 = GlobalKey();

  var params = Get.parameters;

  void selectContact(Contact selectedContact, BuildContext context) {
    contactController.selectContact(selectedContact);
  }

  void addMyPhone() {
    String phoneNumber = _userController.getLocalUser()!.phoneNumber!;
    Phone phone = Phone(phoneNumber, normalizedNumber: phoneNumber);
    Contact contact = Contact(phones: [phone]);
    selectContact(contact, context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            const RowAppBar(),
            Text("Create a Chama",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            const SizedBox(height: 10),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        const ColorScheme.light(primary: AppColors.primary)),
                child: Obx(() => Stepper(
                  connectorThickness: 2,
                  margin: const EdgeInsets.all(1),
                  elevation: 0,
                  currentStep: controller.currentStep.value,
                  type: StepperType.horizontal,
                  steps: getSteps(),
                  onStepContinue: () {
                    final isLastStep = controller.currentStep.value == getSteps().length - 1;
                    if (!isLastStep) {
                      controller.currentStep.value += 1;
                    }
                  },
                  onStepCancel: controller.currentStep.value == 0
                      ? null
                      : () {
                          controller.currentStep.value -= 1;
                        },
                  controlsBuilder: (context, details) {
                    return buildControls(context, details);
                  },
                )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Step> getSteps() => [
        Step(
            state: controller.currentStep.value > 0 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Text("Chama\n Details"),
            content: buildStepOne(context),
            isActive: controller.currentStep.value >= 0),
        Step(
            state: controller.currentStep.value > 1 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("Details"),
              ],
            ),
            content: buildStepTwo(context),
            isActive: controller.currentStep.value >= 1),
        Step(
            state: controller.currentStep.value == 1 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Center(child: Text("Members")),
            content: buildStepThree(context),
            isActive: controller.currentStep.value >= 2),
      ];

  Widget buildControls(BuildContext context, ControlsDetails details) {
    return SizedBox(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          controller.currentStep.value != 0
              ? OutlinedButton(
                  onPressed: details.onStepCancel,
                  child: const Text("Back"))
              : Container(),
          if (controller.currentStep.value == 2)
            Obx(
              () => CustomKtButton(
                isLoading: _chamaController.isAdding.isTrue,
                onPress: () async {
                  AnalyticsEngine.userCreatesKitty();
                  final selected = contactController.selectedContacts;
                  try {
                    final List<Member> members = [];
                    for (var index = 0 + 1; index < selected.length; index++) {
                      final contact = selected[index];
                      final phoneNumber = contact.phones.first.normalizedNumber;

                      final member = Member(
                        phoneNumber: phoneNumber,
                        firstName: contact.name.first,
                        secondName: contact.name.last,
                        role: contact.name.prefix,
                        receivingOrder: 1 + index,
                        status: "ACTIVE",
                      );
                      members.add(member);
                    }
                    final chamaMembers = MembersDto(
                        chamaId: _chamaController.createRes["ID"],
                        members: members);

                    final resp = await _chamaController
                        .addMember(memebersDto: chamaMembers);
                    if (resp) {
                      ToastUtils.showSuccessToast(
                        context,
                        _chamaController.apiMessage.string,
                        "success",
                      );

                      Get.to(()=> SucessPage(
                        text: 'created',
                        kittyName: controller.chamaNameController.text,
                      ));
                      contactController.clearSelection();
                    } else {
                      ToastUtils.showErrorToast(
                        context,
                        _chamaController.apiMessage.string,
                        "Error",
                      );
                    }
                  } catch (e) {
                    ToastUtils.showErrorToast(
                      context,
                      "An error occurred while adding member.",
                      "Error",
                    );
                  }
                },
                width: 100,
                height: 40,
                btnText: "Finish",
              ),
            ),
          if (controller.currentStep.value == 1)
            Obx(
              () => Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomKtButton(
                  isLoading: _chamaController.isloading.isTrue,
                  onPress: () async {
                    if (formKey1.currentState!.validate()) {
                      int? referCode;
                      if (params.isNotEmpty) {
                        referCode = int.tryParse(params["id"].toString());
                      }
                      DateTime date = DateFormat.yMd()
                          .parse(controller.dateController.text);

                      TimeOfDay time = TimeOfDay.fromDateTime(
                          DateFormat.Hm().parse(controller.timeController.text));

                      DateTime combinedDateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                      try {
                        final createDto = CreateDto(
                            title: controller.chamaNameController.text,
                            description: quilltoHtml(controller.quillController),
                            phoneNumber:
                                '+${_userController.getLocalUser()?.phoneNumber ?? ''}',
                            countryCode: _userController
                                    .getLocalUser()!
                                    .countryCode ??
                                "KE",
                            email: controller.emailController.text,
                            refererCode: int.tryParse(
                                controller.refController.text.trim()),
                            whatsAppLink: controller.linkController.text,
                            frequency: controller.freqcyController.text,
                            nextOccurrence: combinedDateTime.toUtc(),
                            amount: num.parse(
                              controller.amountController.text.trim(),
                            ),
                            imeiCode: controller.imeiCode.value,
                            deviceModel: controller.deviceModel.value,
                            latitude: controller.box.read(CacheKeys.lat),
                            longitude: controller.box.read(CacheKeys.long));

                        final res = await _chamaController
                            .createChama(createDto: createDto);
                        if (res) {
                          Get.find<GlobalControllers>().clearCode();
                          ToastUtils.showSuccessToast(
                            context,
                            _chamaController.apiMessage.string,
                            "success",
                          );
                          addMyPhone();
                          details.onStepContinue!();
                        } else {
                          ToastUtils.showErrorToast(
                            context,
                            _chamaController.apiMessage.string,
                            "Error",
                          );
                        }
                      } catch (e) {}
                    }
                  },
                  height: 45.h,
                  width: 90.w,
                  btnText: "Next",
                ),
              ),
            ),
          if (controller.currentStep.value == 0)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomKtButton(
                onPress: () {
                  if (formKey.currentState!.validate()) {
                    details.onStepContinue!();
                  }
                },
                height: 45.h,
                width: 90.w,
                btnText: "Next",
              ),
            ),
        ],
      ),
    );
  }

  Widget buildStepOne(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Chama Name",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: controller.chamaNameController,
            hintText: "e.g Wasafi Chama",
            labelText: "Chama Name",
            isRequired: true,
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Chama Name cannot be empty";
              } else if (p0.length < 5) {
                return "Chama Name must be between 5 and 300";
              }
              return null;
            },
          ),
          Text("Chama Description",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(height: 5.h),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blueAccent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: AppColors.blueButtonColor,
                  width: 1.0,
                ),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  q.QuillSimpleToolbar(
                    config: const q.QuillSimpleToolbarConfig(
                      multiRowsDisplay: false,
                    ),
                    controller: controller.quillController,
                  ),
                  const SizedBox(height: 15),
                  q.QuillEditor.basic(
                    controller: controller.quillController,
                    config: const q.QuillEditorConfig(
                      placeholder: "e.g purpose of chama",
                      autoFocus: false,
                      enableInteractiveSelection: true,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            "Whatsapp group link(optional)",
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(height: 5.h),
          CustomTextField(
            labelText: "Group link",
            controller: controller.linkController,
          ),
          SizedBox(height: 10.h),
          Text("Enter Referer code(Optional)",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(height: 5.h),
          CustomTextField(
            controller: controller.refController,
            hintText: "e.g 12",
            labelText: "Referral code",
          ),
        ],
      ),
    );
  }

  Widget buildStepTwo(BuildContext context) {
    String? description;
    for (Frequency freq in _chamaController.frequencies) {
      if (freq.frequency == controller.freqcyController.text) {
        description = freq.description;
        break;
      }
    }
    return Form(
      key: formKey1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Enter Chama Email",
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(height: 5.h),
          CustomTextField(
            controller: controller.emailController,
            hintText: "e.g <EMAIL>",
            labelText: "",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Email cannot be empty";
              } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                  .hasMatch(p0)) {
                return "Enter a valid email address";
              }
              return null;
            },
          ),
          Row(
            children: [
              Text("How Often Do Members Contribute",
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey1.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey1,
                  message: KtStrings.often,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 5.h),
          Obx(() => DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: "Select Frequency",
              fillColor: Colors.blueAccent.withOpacity(0.1),
            ),
            isExpanded: true,
            items: controller.dropdownItems
                .map(
                  (String item) => DropdownMenuItem<String>(
                    value: item,
                    child: Text(
                      item,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                )
                .toList(),
            value: controller.selectedvalue.value.isEmpty ? null : controller.selectedvalue.value,
            onChanged: (String? value) {
              controller.selectedvalue.value = value!;
              controller.freqcyController.text = value;
            },
          )),
          Text(
            description ?? "",
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
          SizedBox(height: 5.h),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text("How Much Does each Member Contribute",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey2.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey2,
                  message: KtStrings.amount,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          CustomTextField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
            ],
            controller: controller.amountController,
            hintText: "1000",
            isRequired: true,
            labelText: "Amount",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Amount is required";
              }
              return null;
            },
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                    "Set a Deadline For Your ${controller.freqcyController.text} Contribution",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey3.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey3,
                  message: KtStrings.deadline,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          DatePick(
            date: controller.dateController,
            time: controller.timeController,
            combinedDateTime: controller.combinedDateTime.value,
          ),
          SizedBox(height: 10.h),
        ],
      ),
    );
  }

  Widget buildStepThree(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(2),
        child: Column(
          children: [
            Text("Invite Chama Members", style: theme.textTheme.titleLarge),
            SizedBox(height: 8.h),
            _buildFrame(context),
            SizedBox(height: 8.h),
            StreamBuilder<void>(
              stream: controller.stream,
              builder: (context, snapshot) {
                return buildInviteContacts(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 2.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [SizedBox(height: 20.h), _inputPhoneNumber(context)],
      ),
    );
  }

  Widget _inputPhoneNumber(BuildContext context) {
    return Form(
      key: formKey2,
      child: Column(
        children: [
          CustomInternationalPhoneInput(
            onInputChanged: (num) {
              controller.invitePhone.value = num.phoneNumber!;
            },
            onInputValidated: (bool value) {},
            ignoreBlank: false,
            inputDecoration: InputDecoration(
              suffixIcon: _buildAddNumber(context),
            ),
            initialValue: controller.number.value,
            controller: controller.phoneController,
            formatInput: true,
          ),
          SizedBox(height: 5.h),
          CustomElevatedButton(
              leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.imgPlus, height: 15.h, width: 15.w),
              ),
              onPressed: () {
                if (formKey2.currentState!.validate()) {
                  String phoneNumber = controller.invitePhone.value;

                  Phone phone =
                      Phone(phoneNumber, normalizedNumber: phoneNumber);

                  Contact contact = Contact(
                    phones: [phone],
                  );
                  selectContact(contact, context);
                  controller.phoneController.clear();
                }
              },
              height: 45.h,
              width: 300.w,
              text: "Add",
              buttonStyle: CustomButtonStyles.fillIndigR,
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700),
        ],
      ),
    );
  }

  Widget _buildAddNumber(BuildContext context) {
    return IconButton(
        icon: const Icon(Icons.contacts_outlined),
        onPressed: () async {
          final List<Contact>? selectedContacts = await Get.to(() => const GetXContactPicker(
            mode: ContactPickerMode.multiple,
            display: ContactPickerDisplay.fullScreen,
            title: 'Select Contacts',
          ));

          if (selectedContacts != null && selectedContacts.isNotEmpty) {
            for (var contact in selectedContacts) {
              selectContact(contact, context);
            }
          }
        });
  }

  Widget buildInviteContacts(BuildContext context) {
    return Obx(() {
      final selectedContacts = contactController.selectedContacts;

      // Check if selected contacts list is empty
      if (selectedContacts.isEmpty) {
        return CustomImageView(
          imagePath: AssetUrl.imgGroup13,
          height: 150.h,
          width: 254.w,
        );
      } else {
        final myheight = selectedContacts.length * 80.h;
        return Container(
          height: myheight <= 240.h
              ? 240.h
              : myheight >= 500.h
                  ? 500.h
                  : myheight,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
          decoration: AppDecoration.outlineGray
              .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
          child: ListView.builder(
            itemCount: selectedContacts.length,
            itemBuilder: (context, index) {
              final selectedContact = selectedContacts[index];

              Role roleStatus =
                  controller.roleStatusMap[selectedContact.phones.first.normalizedNumber] ??
                      Role.member;

              return Container(
                decoration:
                    BoxDecoration(borderRadius: BorderRadiusStyle.roundedBorder8),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomImageView(
                          imagePath: AssetUrl.dotSix,
                          height: 25.h,
                          width: 25.w,
                          margin: EdgeInsets.only(right: 3.h),
                        ),
                        Opacity(
                          opacity: 0.5,
                          child: Padding(
                            padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                            child: Text(
                              "${index + 1}",
                              style: theme.textTheme.titleSmall!,
                            ),
                          ),
                        ),
                        CustomImageView(
                          imagePath: AssetUrl.imgPerson,
                          height: 25.h,
                          width: 25.w,
                          margin: EdgeInsets.only(left: 3.h),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(
                                  index == 0
                                      ? _userController.getLocalUser()!.firstName!
                                      : "${selectedContact.name.first} ${selectedContact.name.last}",
                                  overflow: TextOverflow.ellipsis,
                                  style: CustomTextStyles.titleSmallGray90001),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(selectedContact.phones.first.number,
                                  overflow: TextOverflow.ellipsis,
                                  style: CustomTextStyles.titleSmallGray90001),
                            ),
                            selectedContact.name.prefix == "CHAIRPERSON"
                                ? CustomImageView(
                                    imagePath: AssetUrl.crownsv,
                                    height: 18.h,
                                    width: 18.w,
                                    margin: EdgeInsets.symmetric(vertical: 9.h),
                                  )
                                : const SizedBox.shrink(),
                            index == 0
                                ? Text(
                                    "CHAIRPERSON",
                                    style: TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: getRoleColors("CHAIRPERSON"),
                                        fontWeight: FontWeight.bold),
                                  )
                                : Text(
                                    selectedContact.name.prefix,
                                    style: context.dividerTextLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: getRoleColors(
                                            selectedContact.name.prefix)),
                                  ),
                          ],
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: () {
                            contactController.removeContact(selectedContact);
                            controller.roleStatusMap.remove(
                                selectedContact.phones.first.normalizedNumber);
                          },
                          child: index == 0
                              ? const SizedBox()
                              : CustomImageView(
                                  imagePath: AssetUrl.imgIconoirCancel,
                                  height: 18.h,
                                  width: 18.w,
                                  margin: EdgeInsets.symmetric(
                                      vertical: 9.h, horizontal: 5.h),
                                ),
                        ),
                        index == 0
                            ? const SizedBox.shrink()
                            : IconButton(
                                icon: const Icon(
                                  Icons.edit,
                                  color: AppColors.blueButtonColor,
                                ),
                                padding: EdgeInsets.symmetric(vertical: 10.h),
                                onPressed: () {
                                  _chamaOptionsDialog(context, roleStatus, index,
                                      selectedContact);
                                },
                              )
                      ],
                    ),
                    SizedBox(height: 2.h),
                    const Divider()
                  ],
                ),
              );
            },
          ),
        );
      }
    });
  }

  void _chamaOptionsDialog(BuildContext context, Role roleStatus, int index,
      Contact selectedContact) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        controller.firstNameController.text = selectedContact.name.first;
        controller.lastNameController.text = selectedContact.name.last;
        controller.selectedRole.value = selectedContact.name.prefix;

        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Align(
              alignment: Alignment.centerRight,
              child: AlertDialog(
                title: const Text(
                  "Update Member Details",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: SizedBox(
                  height: 250.h,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: controller.firstNameController,
                        labelText: "Enter First Name",
                      ),
                      SizedBox(height: 8.h),
                      CustomTextField(
                        controller: controller.lastNameController,
                        labelText: "Enter Last Name",
                      ),
                      SizedBox(height: 8.h),
                      Obx(() => DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: "Select Role",
                          fillColor: Colors.blueAccent.withOpacity(0.1),
                        ),
                        isExpanded: true,
                        items: controller.roleItems
                            .map(
                              (String item) => DropdownMenuItem<String>(
                                value: item,
                                child: Text(
                                  item,
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                            )
                            .toList(),
                        value: controller.selectedRole.value,
                        onChanged: (String? value) {
                          controller.selectedRole.value = value!;
                        },
                      )),
                    ],
                  ),
                ),
                actions: [
                  CustomElevatedButton(
                    text: "Save",
                    onPressed: () {
                      try {
                        controller.updateContact(
                            selectedContact.phones.first.normalizedNumber,
                            controller.firstNameController.text,
                            controller.lastNameController.text,
                            controller.selectedRole.value);
                      } catch (e) {}

                      Navigator.of(context).pop(); // Close the dialog
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
