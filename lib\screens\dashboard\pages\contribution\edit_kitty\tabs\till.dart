import 'package:flutter/material.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';

class TillPage2 extends StatelessWidget {
  final TextEditingController tillController;
  const TillPage2(
      {super.key,
      required this.tillController,});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Mpesa Till number",
              style: context.titleText,
            ),
          ),
          CustomTextField(
            labelText: "Till number",
            controller: tillController,
            showNoKeyboard: true,
            isRequired: true,
            hintText: "till number",
            validator: (value) {
              RegExp regex = RegExp(r'[a-zA-Z]');
              if (value!.isEmpty) {
                return "Enter Till Number";
              } else if (regex.hasMatch(value)) {
                return "Till number can not contain Alphabets";
              } else {
                return null;
              }
            },
          ),
        ],
      ),
    );
  }
}
