import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/utils_exports.dart';

class CreateAnyKittiesPage extends StatefulWidget {
  const CreateAnyKittiesPage({super.key});

  @override
  State<CreateAnyKittiesPage> createState() => _CreateAnyKittiesPageState();
}

class _CreateAnyKittiesPageState extends State<CreateAnyKittiesPage> {
  getRefferalCode() async {
    var queryParameters = Get.parameters;
    debugPrint(" queryParameters: ${queryParameters.toString()}");
    if (queryParameters.containsKey("code")) {
      await GetStorage.init();
      GetStorage().write('code', queryParameters['code']);
    }
  }

  @override
  void initState() {
    super.initState();
    getRefferalCode();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Select the Kitty Type", style: TextStyle()),
      ),
      body: const CreateTabs(),
    );
  }
}

class CreateTabs extends StatelessWidget {
  const CreateTabs({super.key});

  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      minScale: 0.5,
      maxScale: 4,
      child: AlertDialog(
        content: _DialogContent(),
        actions: [_CloseButton(context)],
      ),
    );
  }
}

class _DialogContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // width: 0.8.sw,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: _ActionGrid(),
          ),
        ],
      ),
    );
  }
}

class _ActionGrid extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Wrap(
      //  spacing: 10.w,
      children: [
        _ActionCard(
          iconPath: AssetUrl.createKitty,
          label: "General Contribution Kitty",
          route: NavRoutes.createkittyScreen,
        ),
        _ActionCard(
          iconPath: AssetUrl.createChama,
          label: "Create Chama",
          route: NavRoutes.chamaStepper,
        ),
        _ActionCard(
          iconPath: AssetUrl.createChama,
          label: "Create Event-ticketing",
          route: NavRoutes.createEvent,
        ),
      ],
    );
  }
}

class _ActionCard extends StatelessWidget {
  final String iconPath;
  final String label;
  final String route;

  const _ActionCard({
    required this.iconPath,
    required this.label,
    required this.route,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(8.r),
      onTap: () => Get.toNamed(route),
      child: Container(
        height: 140.h,
        margin: EdgeInsets.all(8.h),
        width: 110.w,
        padding: EdgeInsets.all(8.w),
        decoration: _cardDecoration(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              iconPath,
              height: 42.h,
              width: 42.w,
              // colorFilter: ColorFilter.mode(
              //   Theme.of(context).primaryColor,
              //   BlendMode.srcIn,
              // ),
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: AppColors.blueButtonColor,
                    fontSize: 14.sp,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  BoxDecoration _cardDecoration(BuildContext context) => BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1.r,
        ),
      );
}

class _CloseButton extends StatelessWidget {
  final BuildContext context;

  const _CloseButton(this.context);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: Theme.of(context).primaryColor,
      ),
      onPressed: () => Navigator.of(context).pop(),
      child: const Text('Close'),
    );
  }
}

class BuildCard extends StatelessWidget {
  final String edit;
  final String resources;
  final VoidCallback onTap;
  final Color? color;
  const BuildCard(
      {super.key,
      required this.edit,
      required this.resources,
      required this.onTap,
      this.color});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 110.w,
        height: 85.h,
        alignment: Alignment.center,
        margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 4.h,
        ),
        decoration: AppDecoration.outlineIndigo.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (color != null)
              SvgPicture.asset(
                edit,
                height: 30.h,
                width: 30.w,
                colorFilter: const ColorFilter.mode(
                    AppColors.blueButtonColor, BlendMode.srcIn),
              )
            else
              SvgPicture.asset(
                edit,
                height: 30.h,
                width: 30.w,
                colorFilter: const ColorFilter.mode(
                    AppColors.blueButtonColor, BlendMode.srcIn),
              ),
            SizedBox(height: 2.h),
            Text(
              resources,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.indigo500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
