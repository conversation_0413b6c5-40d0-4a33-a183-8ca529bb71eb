import 'package:flutter_test/flutter_test.dart';
import 'package:onekitty/utils/pdf_null_safety_helper.dart';

void main() {
  group('PDFNullSafetyHelper Tests', () {
    test('formatDate handles null values correctly', () {
      expect(PDFNullSafetyHelper.formatDate(null), 'N/A');
      expect(PDFNullSafetyHelper.formatDate(DateTime(2024, 1, 1)), isNotEmpty);
    });

    test('getTransactionCode handles null values correctly', () {
      expect(PDFNullSafetyHelper.getTransactionCode(null), 'N/A');
      expect(PDFNullSafetyHelper.getTransactionCode(''), 'N/A');
      expect(PDFNullSafetyHelper.getTransactionCode('TXN123'), 'TXN123');
    });

    test('getAmount handles null values correctly', () {
      expect(PDFNullSafetyHelper.getAmount(null), '0');
      expect(PDFNullSafetyHelper.getAmount(100), '100');
      expect(PDFNullSafetyHelper.getAmount('150'), '150');
    });

    test('getFullName handles null values correctly', () {
      expect(PDFNullSafetyHelper.getFullName(null, null), 'N/A');
      expect(PDFNullSafetyHelper.getFullName('John', null), 'John');
      expect(PDFNullSafetyHelper.getFullName(null, 'Doe'), 'Doe');
      expect(PDFNullSafetyHelper.getFullName('John', 'Doe'), 'John Doe');
      expect(PDFNullSafetyHelper.getFullName('  ', '  '), 'N/A');
    });

    test('getEmail handles null values correctly', () {
      expect(PDFNullSafetyHelper.getEmail(null), '');
      expect(PDFNullSafetyHelper.getEmail(''), '');
      expect(PDFNullSafetyHelper.getEmail('<EMAIL>'), '<EMAIL>');
    });

    test('getBalance handles null values correctly', () {
      expect(PDFNullSafetyHelper.getBalance(null), '0');
      expect(PDFNullSafetyHelper.getBalance(1000), '1000');
      expect(PDFNullSafetyHelper.getBalance('2000'), '2000');
    });

    test('getPhoneNumber handles null values correctly', () {
      expect(PDFNullSafetyHelper.getPhoneNumber(null), 'N/A');
      expect(PDFNullSafetyHelper.getPhoneNumber(''), 'N/A');
      expect(PDFNullSafetyHelper.getPhoneNumber('+254712345678'), '+254712345678');
    });

    test('getId handles null values correctly', () {
      expect(PDFNullSafetyHelper.getId(null), 0);
      expect(PDFNullSafetyHelper.getId(123), 123);
    });

    test('getTitle handles null values correctly', () {
      expect(PDFNullSafetyHelper.getTitle(null), 'N/A');
      expect(PDFNullSafetyHelper.getTitle(''), 'N/A');
      expect(PDFNullSafetyHelper.getTitle('My Kitty'), 'My Kitty');
    });

    test('getUsername handles null values correctly', () {
      expect(PDFNullSafetyHelper.getUsername(null), 'N/A');
      expect(PDFNullSafetyHelper.getUsername(''), 'N/A');
      expect(PDFNullSafetyHelper.getUsername('john_doe'), 'john_doe');
    });
  });
}