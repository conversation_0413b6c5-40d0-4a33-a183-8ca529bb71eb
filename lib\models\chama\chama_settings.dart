// To parse this JSON data, do
//
//     final chamaSettingsModel = chamaSettingsModelFromJson(jsonString);

import 'dart:convert';

ChamaSettingsModel chamaSettingsModelFromJson(String str) =>
    ChamaSettingsModel.fromJson(json.decode(str));

String chamaSettingsModelToJson(ChamaSettingsModel data) =>
    json.encode(data.toJson());

class ChamaSettingsModel {
  bool? status;
  String? message;
  ChamaSetting? data;

  ChamaSettingsModel({
    this.status,
    this.message,
    this.data,
  });

  factory ChamaSettingsModel.fromJson(Map<String, dynamic> json) =>
      ChamaSettingsModel(
        status: json["status"],
        message: json["message"],
        data: ChamaSetting.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
      };
}

class ChamaSetting {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? chamaId;
  int? beneficiariesPerCycle;
  double? beneficiaryPercentage;
  int? signatureThreshold;

  ChamaSetting({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.chamaId,
    this.beneficiariesPerCycle,
    this.beneficiaryPercentage,
    this.signatureThreshold,
  });

  factory ChamaSetting.fromJson(Map<String, dynamic> json) => ChamaSetting(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        beneficiariesPerCycle:
            int.tryParse(json["beneficiaries_per_cycle"]?.toString() ?? '0'),
        beneficiaryPercentage: double.tryParse(
            json["beneficiary_percentage"]?.toString() ?? '0.0'),
        signatureThreshold: json["signature_threshold"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "beneficiaries_per_cycle": beneficiariesPerCycle,
        "beneficiary_percentage": beneficiaryPercentage,
        "signature_threshold": signatureThreshold,
      };
}
