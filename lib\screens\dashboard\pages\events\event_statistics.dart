import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/services/http_service.dart';

class EventStatistics extends StatelessWidget {
  final int eventId;
  const EventStatistics({super.key, required this.eventId});

  @override
  Widget build(BuildContext context) {
    Future<dynamic> _loadStatistics() async {
      final HttpService _httpService = Get.find();
      final response = await _httpService.request(url: 'tickets/event/statistics/', method: Method.GET);
      return response;
    }
    return Scaffold(
      appBar: AppBar(),
      body: FutureBuilder(
        future: _loadStatistics(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData) {
            return const Center(child: Text('No data available'));
          } else {
            return  SelectableText('${snapshot.data}');
          }
        },
      ),
    );
  }
}