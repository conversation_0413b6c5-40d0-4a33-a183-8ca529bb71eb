class Calc {
  double calculateCharges(String text, num contacts) {
    double unit = 0.8;
    double atChar = 160.0;

    int characterCount = text.length;

    double mfactor = (characterCount / atChar).toDouble();

    if (mfactor % 1 != 0) {
      mfactor = mfactor.ceilToDouble(); // Round up to the next whole number
    }

    double charge = mfactor * unit * (contacts);
    charge = double.parse(charge.toStringAsFixed(2));
    return charge;
  }
}
