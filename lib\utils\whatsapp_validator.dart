/// Utility class for WhatsApp link validation across all modules
class WhatsAppValidator {
  /// Enhanced WhatsApp link validation supporting multiple formats
  /// Maintains backward compatibility while supporting new formats
  static bool isValidWhatsAppLink(String? link) {
    if (link == null || link.trim().isEmpty) {
      return false;
    }

    // Remove any whitespace
    final cleanLink = link.trim();

    // List of valid WhatsApp URL patterns
    final patterns = [
      // Standard chat.whatsapp.com format with optional query parameters
      // Supports variable length group IDs (10-50 characters) with optional query params
      r'^https:\/\/chat\.whatsapp\.com\/[A-Za-z0-9_-]{10,50}(\?.*)?$',

      // Legacy 22-character format for backward compatibility
      r'^https:\/\/chat\.whatsapp\.com\/[A-Za-z0-9_-]{22}$',

      // WhatsApp invite links (wa.me format)
      r'^https:\/\/wa\.me\/[A-Za-z0-9_-]+(\?.*)?$',

      // WhatsApp group invite with phone number
      r'^https:\/\/api\.whatsapp\.com\/send\?phone=\d{10,15}.*$',

      // WhatsApp group invite with text parameter
      r'^https:\/\/api\.whatsapp\.com\/send\?text=.*$',

      // WhatsApp web format
      r'^https:\/\/web\.whatsapp\.com\/.*$',

      // Alternative WhatsApp domains
      r'^https:\/\/whatsapp\.com\/.*$',

      // Mobile WhatsApp links (app protocol)
      r'^whatsapp:\/\/.*$',

      // WhatsApp Business links
      r'^https:\/\/wa\.me\/c\/\d{10,15}(\?.*)?$',

      // WhatsApp group invite with additional parameters
      r'^https:\/\/chat\.whatsapp\.com\/invite\/[A-Za-z0-9_-]+(\?.*)?$',
    ];

    // Check against all patterns
    for (String pattern in patterns) {
      try {
        if (RegExp(pattern, caseSensitive: false).hasMatch(cleanLink)) {
          return true;
        }
      } catch (e) {
        // Continue to next pattern if regex fails
        continue;
      }
    }

    return false;
  }

  /// Get user-friendly error message for invalid WhatsApp links
  static String getValidationErrorMessage() {
    return 'Please enter a valid WhatsApp link. Supported formats:\n'
        '• https://chat.whatsapp.com/...\n'
        '• https://wa.me/...\n'
        '• https://api.whatsapp.com/send?phone=...\n'
        '• whatsapp://...';
  }

  /// Normalize WhatsApp link (ensure https protocol)
  static String normalizeWhatsAppLink(String link) {
    final cleanLink = link.trim();

    // If it starts with whatsapp://, convert to https format
    if (cleanLink.startsWith('whatsapp://')) {
      return cleanLink.replaceFirst('whatsapp://', 'https://wa.me/');
    }

    // If it doesn't start with http, add https
    if (!cleanLink.startsWith('http://') && !cleanLink.startsWith('https://')) {
      return 'https://$cleanLink';
    }

    return cleanLink;
  }

  /// Extract group ID from WhatsApp link if possible
  static String? extractGroupId(String link) {
    final patterns = [
      // Extract group ID from chat.whatsapp.com links (ignoring query parameters)
      r'https:\/\/chat\.whatsapp\.com\/([A-Za-z0-9_-]+)(?:\?.*)?',
      // Extract from wa.me links
      r'https:\/\/wa\.me\/([A-Za-z0-9_-]+)(?:\?.*)?',
    ];

    for (String pattern in patterns) {
      final match = RegExp(pattern).firstMatch(link);
      if (match != null && match.groupCount > 0) {
        return match.group(1);
      }
    }

    return null;
  }
}
