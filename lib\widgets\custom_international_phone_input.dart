import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomInternationalPhoneInput extends StatefulWidget {
  final TextEditingController? controller;
  final TextEditingController? textFieldController;
  final String hintText;
  final String labelText;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;
  final Function(PhoneNumber) onInputChanged;
  final Function(bool)? onInputValidated;
  final PhoneNumber initialValue;
  final Widget? suffixIcon;
  final bool formatInput;
  final AutovalidateMode? autovalidateMode;
  final String? Function(String?)? validator;
  final InputDecoration? inputDecoration;
  final bool ignoreBlank;
  final Function(PhoneNumber)? onSaved;
  final TextInputType? keyboardType;
  final InputBorder? inputBorder;
  
  const CustomInternationalPhoneInput({
    super.key, 
    this.controller,
    this.textFieldController,
    required this.onInputChanged,
    required this.initialValue,
    this.hintText = '',
    this.labelText = 'Phone Number',
    this.hintStyle,
    this.labelStyle,
    this.onInputValidated,
    this.suffixIcon,
    this.formatInput = true,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.validator,
    this.inputDecoration,
    this.ignoreBlank = false,
    this.onSaved,
    this.keyboardType,
    this.inputBorder,
  });

  @override
  State<CustomInternationalPhoneInput> createState() => _CustomInternationalPhoneInputState();
}

class _CustomInternationalPhoneInputState extends State<CustomInternationalPhoneInput> {
  final GlobalKey<FormFieldState> _phoneNumberKey = GlobalKey<FormFieldState>();
  final countryPicker = const FlCountryCodePicker();
  CountryCode? selectedCountry;
  String? number;
  
  @override
  void initState() {
    super.initState();
    selectedCountry = CountryCode(
      name: widget.initialValue.isoCode ?? 'Kenya',
      code: widget.initialValue.isoCode ?? 'KE',
      dialCode: widget.initialValue.dialCode ?? '+254',
    );
    
    // Initialize phone number from controller if available
    // First check the main controller, then fallback to the textFieldController
    TextEditingController? activeController = widget.controller ?? widget.textFieldController;
    
    if (activeController?.text.isNotEmpty ?? false) {
      // Format phone number if it has a leading zero
      String value = activeController!.text;
      if (value.isNotEmpty && value.startsWith('0')) {
        number = value.substring(1); // Remove leading zero
      } else {
        number = value;
      }
      
      // Pass initial value to onInputChanged callback
      final dialCode = selectedCountry?.dialCode ?? '+254';
      final fullNumber = "$dialCode$number";
      
      final phoneNumber = PhoneNumber(
        phoneNumber: fullNumber,
        isoCode: selectedCountry?.code,
        dialCode: dialCode,
      );
      
      // Only call this if it's not empty to avoid unwanted initial callbacks
      if (number!.isNotEmpty) {
        widget.onInputChanged(phoneNumber);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.labelText.isNotEmpty && widget.inputDecoration == null)
            Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: Text(
                widget.labelText,
                style: widget.labelStyle ?? Theme.of(context).textTheme.titleSmall,
              ),
            ),
          TextFormField(
            key: _phoneNumberKey,
          
            controller: widget.controller ?? widget.textFieldController,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(15),
            ],
            decoration: widget.inputDecoration ?? InputDecoration(
              hintText: widget.hintText,
              hintStyle: widget.hintStyle,
              prefixIcon: _buildCountrySelector(),
              suffixIcon: widget.suffixIcon,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
            ),
            onChanged: (value) {
              // Format phone number to international format
              String formattedValue = value;
              if (value.isNotEmpty && value.startsWith('0')) {
                formattedValue = value.substring(1); // Remove leading zero
              }
              
              setState(() {
                number = formattedValue;
              });
              
              // Create and return a PhoneNumber object with the full international number
              // This ensures phoneNumber property contains the complete number with country code
              final dialCode = selectedCountry?.dialCode ?? '+254';
              final fullNumber = "$dialCode$formattedValue";
              
              final phoneNumber = PhoneNumber(
                phoneNumber: fullNumber,
                isoCode: selectedCountry?.code,
                dialCode: dialCode,
              );
              
              widget.onInputChanged(phoneNumber);
              
              if (widget.onInputValidated != null) {
                // Simple validation - you can enhance this
                final isValid = formattedValue.length >= 9;
                widget.onInputValidated!(isValid);
              }
            },
            validator: widget.validator ?? (value) {
              if (value == null || value.isEmpty) {
                return 'Phone number is required';
              }
              
              // Remove leading zero if present for validation
              String formattedValue = value;
              if (value.isNotEmpty && value.startsWith('0')) {
                formattedValue = value.substring(1);
              }
              
              // Most international phone numbers are between 9-12 digits
              if (formattedValue.length < 9) {
                return 'Please enter a valid phone number';
              }
              
              return null;
            },
            autovalidateMode: widget.autovalidateMode,
            onSaved: widget.onSaved != null ? (value) {
              if (value != null) {
                // Format phone number the same way as in onChanged
                String formattedValue = value;
                if (value.isNotEmpty && value.startsWith('0')) {
                  formattedValue = value.substring(1); // Remove leading zero
                }
                
                final dialCode = selectedCountry?.dialCode ?? '+254';
                final fullNumber = "$dialCode$formattedValue";
                
                final phoneNumber = PhoneNumber(
                  phoneNumber: fullNumber,
                  isoCode: selectedCountry?.code,
                  dialCode: dialCode,
                );
                widget.onSaved!(phoneNumber);
              }
            } : null,
          ),
        ],
      ),
    );
  }

  Widget _buildCountrySelector() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      margin: EdgeInsets.only(right: 8.w),
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: Colors.grey.shade300,
            width: 1.5,
          ),
        ),
      ),
      child: GestureDetector(
        onTap: _openCountryPicker,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Add country flag
            if (selectedCountry != null)
              Padding(
                padding: EdgeInsets.only(right: 4.w),
                child: SizedBox(
                  width: 24.w,
                  height: 16.h,
                  child: selectedCountry!.flagImage(),
                ),
              ),
            Text(
              selectedCountry?.dialCode ?? '+254',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 4.w),
            Icon(Icons.arrow_drop_down, size: 16.sp),
          ],
        ),
      ),
    );
  }

  void _openCountryPicker() async {
    final code = await countryPicker.showPicker(context: context);
    if (code != null) {
      setState(() {
        selectedCountry = code;
      });
      
      // Update the phone number with new country code
      if (number != null && number!.isNotEmpty) {
        final fullNumber = "${code.dialCode}$number";
        
        final phoneNumber = PhoneNumber(
          phoneNumber: fullNumber,
          isoCode: code.code,
          dialCode: code.dialCode,
        );
        widget.onInputChanged(phoneNumber);
      }
    }
  }
} 