import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class MapScreenController extends GetxController {
  late MapController mapController;
  late LatLng initialPosition;
  final Rx<LatLng?> selectedLocation = Rx<LatLng?>(null);
  
  final double? latitude;
  final double? longitude;
  final bool viewOnly;
  
  MapScreenController({
    this.latitude,
    this.longitude,
    this.viewOnly = false,
  });
  
  @override
  void onInit() {
    super.onInit();
    mapController = MapController();
    initialPosition = latitude != null && longitude != null
        ? LatLng(latitude!, longitude!)
        : const LatLng(-1.2921, 36.8219);
    selectedLocation.value = latitude != null && longitude != null
        ? LatLng(latitude!, longitude!)
        : null;
    
    if (kDebugMode) {
      print(
          '###########################\ninitialPosition: $initialPosition\nselectedLocation: ${selectedLocation.value}');
    }
  }
  
  @override
  void onClose() {
    mapController.dispose();
    super.onClose();
  }
  
  void onMapTap(LatLng latLng) {
    if (viewOnly) return;
    selectedLocation.value = latLng;
  }
  
  Map<String, double>? getSelectedLocationData() {
    if (selectedLocation.value != null) {
      return {
        "latitude": selectedLocation.value!.latitude,
        "longitude": selectedLocation.value!.longitude,
      };
    }
    return null;
  }
}