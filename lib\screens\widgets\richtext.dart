import 'package:flutter/material.dart';
import 'package:onekitty/helpers/colors.dart';

class RichTextWidget extends StatelessWidget {
  final String text1;
  final VoidCallback onPress;
  final String text2;
  const RichTextWidget({super.key, required this.text1, required this.onPress, required this.text2});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
         Text(text1,
            style: const TextStyle(color: AppColors.greyTextColor, fontSize: 18)),
        TextButton(
            onPressed: onPress,
            child: Text(text2,
                style: const TextStyle(color: AppColors.primary, fontSize: 18)))
      ],
    );
  }
}
