import 'dart:async';
import 'package:animations/animations.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_contacts/flutter_contacts.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/models/bulkSms/messagesDTO.dart';
import 'package:onekitty/models/bulk_sms/sms_groups.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';

import 'create_msg_group.dart';
import 'widgets/calc.dart';

class ViewSingleGroup extends StatefulWidget {
  final SmsGroups smsGroup;
  const ViewSingleGroup({super.key, required this.smsGroup});

  @override
  State<ViewSingleGroup> createState() => _ViewSingleGroupState();
}

class _ViewSingleGroupState extends State<ViewSingleGroup> {
  TextEditingController messageController = TextEditingController();
  UserKittyController userController = Get.put(UserKittyController());
  BulkSMSController smsController = Get.put(BulkSMSController());
  final _calc = Calc();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController phoneController = TextEditingController();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  String myPhone = "";
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();
  final countryPicker = const FlCountryCodePicker();
  final controller = Get.put(CreateMsgController());

  @override
  Widget build(BuildContext context) {
    final isSending = false.obs;
    return Scaffold(
      bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(8),
          child: Obx(
            () => MyButton(
              showLoading: isSending.value,
              onClick: () async {
                isSending(true);
                final recepients = controller.groupMembers
                    .map((e) => Contact(
                            phones: [
                              Phone(e.phoneNumber ?? '',
                                  normalizedNumber: e.phoneNumber ?? '')
                            ],
                            name: Name(
                                first: e.firstName ?? '',
                                last: e.secondName ?? ''),
                            displayName: "${e.firstName} ${e.secondName}"))
                    .toList();
                if (_formKey.currentState!.validate()) {
                  final messagedto = MessagesDto(
                      userId: userController.user.value.id ?? 0,
                      message: messageController.text,
                      recipient: recepients);
                  final res =
                      await smsController.ConfirmMessages(message: messagedto);
                  if (res) {
                    showConfirmDialog(
                        context: context,
                        price: smsController.resData["price"],
                        smscount: smsController.resData["sms_count"],
                        recipients: recepients.length,
                        charactercount:
                            smsController.resData["characters_count"],
                        messageDto: messagedto);
                    isSending(false);
                  } else {
                    if (smsController.apiMessage.string ==
                        "insufficient balance,top up to continue") {
                      Get.toNamed(NavRoutes.topup);
                    }
                    ToastUtils.showInfoToast(
                        context,
                        smsController.apiMessage.string,
                        "Please top up to send the message");
                    isSending(false);
                  }
                }
              },
              label: 'Send Message',
            ),
          )),
      appBar: AppBar(
        title: Text('${widget.smsGroup.name}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              controller.groupMembers.value =
                  widget.smsGroup.smsGroupMembers ?? [];
              Get.to(()=> CreateMessageGroup(smsGroup: widget.smsGroup));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          OpenContainer(
              closedColor: Theme.of(context).scaffoldBackgroundColor,
              openBuilder: (context, action) =>
                  SelectContacts(smsGroups: widget.smsGroup),
              closedBuilder: (context, call) {
                return Container(
                  width: 360.w,
                  height: 150.h,
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: isLight.value ? Colors.white : Colors.grey[800],
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: (isLight.value ? Colors.grey : Colors.black)
                            .withOpacity(0.1),
                        spreadRadius: 2,
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Total Contacts',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            size: 32,
                            Icons.groups_outlined,
                            color: Theme.of(context).primaryColor,
                          ),
                          Text(
                            '  ${controller.groupMembers.length}', // Replace with actual contact count
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }),
          _buildRow(context),
          _buildMessage(context),
          SizedBox(height: 6.h),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 2.w),
              child: Text("Messages cost 0.8 Ksh per 160 characters",
                  style: CustomTextStyles.bodySmallBluegray700),
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    // final selectedContact = ref
    //     .watch(selectContactControllerProvider.notifier)
    //     .getSelectedContacts();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text("Message", style: CustomTextStyles.titleSmallGray90001),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 3.h),
            child: Text("Est. cost:", style: theme.textTheme.bodySmall),
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.w, bottom: 3.h),
            child: Text(
                "${_calc.calculateCharges(messageController.text, controller.groupMembers.length)} Ksh",
                style: CustomTextStyles.labelLargeff545963),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage(BuildContext context) {
    return Form(
      key: _formKey,
      child: TextFormField(
        controller: messageController,
        decoration: InputDecoration(
          hintText: "e.g Hello, prepare for a meeting...",
          hintStyle: CustomTextStyles.bodyMediumBluegray400,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 9.h),
        ),
        maxLines: 5,
        validator: (value) {
          if (value!.isEmpty) {
            return "Message is Empty, Add messages";
          } else {
            return null;
          }
        },
        onChanged: (value) {
          setState(() {
            // _message.reSmessage.value = value;
            _calc.calculateCharges(value, controller.groupMembers.length);
          });
        },
      ),
    );
  }

  showConfirmDialog({
    required BuildContext context,
    required price,
    required smscount,
    required recipients,
    required charactercount,
    required messageDto,
  }) {
    return showAnimatedDialog(
      barrierDismissible: true,
      animationType: DialogTransitionType.sizeFade,
      curve: Curves.fastOutSlowIn,
      duration: const Duration(milliseconds: 900),
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            height: SizeConfig.screenHeight * .36,
            width: SizeConfig.screenWidth * .8,
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: getProportionalScreenWidth(10)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Center(
                    child: Text(
                      "Message Summary",
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                  SizedBox(height: 7.h),

                  Text(
                    "Number of messages: ${smscount.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 7.h),

                  Text(
                    "character Count: ${charactercount.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),

                  Text(
                    "Recipients: ${recipients.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 15.h),
                  // Text(title, style: CustomTextStyles.titleSmallIndigo500),
                  Text(
                    "SMS will Charge: ${price.toString()}KSH",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                    // style: TextStyle(color: Colors.green),
                  ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).canvasColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.sp),
                            side: BorderSide(
                                width: 2.sp,
                                color: Theme.of(context).primaryColor),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text(
                          "cancel",
                          style: TextStyle(color: Colors.black),
                        ),
                      ),
                      const Spacer(),
                      Obx(
                        () => CustomKtButton(
                          isLoading: smsController.isSending.isTrue,
                          width: 90.w,
                          height: 30.h,
                          btnText: "Confirm",
                          onPress: () async {
                            try {
                              final res = await smsController.sendMessages(
                                  message: messageDto);
                              if (res) {
                                // ref
                                //     .read(selectContactControllerProvider
                                //         .notifier)
                                //     .removeAllSelectedContacts();
                                setState(() {
                                  messageController.clear();
                                });
                                Get.offNamed(NavRoutes.SmSsent);
                              } else {
                                ToastUtils.showInfoToast(
                                    context,
                                    smsController.apiMessage2.string,
                                    "Add recipients");
                              }
                            } catch (e) {}
                          },
                        ),
                      ),
                      SizedBox(
                        height: 10.sp,
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class SelectContacts extends StatefulWidget {
  final SmsGroups smsGroups;
  const SelectContacts({super.key, required this.smsGroups});

  @override
  State<SelectContacts> createState() => _SelectContactsState();
}

class _SelectContactsState extends State<SelectContacts> {

  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  final phoneController = TextEditingController();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  String myPhone = "";
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateMsgController>();

    /// Section Widget
    Widget _buildPhoneNumber(BuildContext context) {
      return Form(
        key: _formKey2,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              CustomInternationalPhoneInput(
  onInputChanged: (num,
) {
                  setState(() {
                    myPhone = num.phoneNumber!;
                  });
                },
                onInputValidated: (bool value) {},
                 
                ignoreBlank: false,
                 
                //  
                initialValue: num,
                textFieldController: phoneController,
                formatInput: true,
                keyboardType: const TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                inputBorder: const OutlineInputBorder(),
                onSaved: (PhoneNumber number) {},
              ),
              const SizedBox(height: 8),
              Obx(
                () => MyButton(
                  showLoading: controller.isAddingMember.value,
                  onClick: () {
                    if (_formKey2.currentState!.validate()) {
                      String phoneNumber = myPhone;
                      Phone phone =
                          Phone(phoneNumber, normalizedNumber: phoneNumber);
                      if (controller.hasDuplicate(phone.number)) {
                        ToastUtils.showErrorToast(
                            context, 'Error', 'duplicate found');
                        return;
                      }
                      controller.addGroupMember(
                        {
                          'phone_number': phone.number,
                          'first_name': "",
                          'second_name': "",
                          "sms_group_id": widget.smsGroups.id,
                        },
                      ).onError((e, s) {
                        Get.snackbar('Error', 'An error occured');
                        return;
                      });

                      setState(() {
                        phoneController.clear();
                      });
                    } else {
                      ToastUtils.showErrorToast(
                          context, "Enter a number to continue", "Error");
                    }
                  },
                  label: ('Add Member'),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 24),
            const Row(
              children: [
                Text(
                  'Group Members',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Spacer(),

                //  IconButton(
                //   onPressed: () async {
                //     FilePickerResult? result =
                //         await FilePicker.platform.pickFiles(
                //       type: FileType.custom,
                //       allowedExtensions: ['csv', 'xlsx'],
                //     );

                //     if (result != null) {
                //       File file = File(result.files.single.path!);
                //       String extension =
                //           result.files.single.extension!.toLowerCase();

                //       if (extension == 'csv') {
                //     //     final members =  await controller.getContactsFromCSV(file);
                //     //  controller.groupMembers.value = [...controller.groupMembers, ...members ];
                //         final input = file.openRead();
                //         final fields = await input
                //             .transform(utf8.decoder)
                //             .transform(const CsvToListConverter())
                //             .toList();

                //         setState(() {
                //           for (var row in fields.skip(1)) {
                //             controller.groupMembers.add(
                //               SmsGroupMember(
                //                 firstName: row[0] ?? '',
                //               secondName: row[1] ?? '',
                //               phoneNumber: row[2] ?? '',
                //               )

                //             );
                //           }
                //         });
                //       } else if (extension == 'xlsx') {
                //         var bytes = file.readAsBytesSync();
                //         var excel = ex.Excel.decodeBytes(bytes);
                //         var sheet = excel.tables[excel.tables.keys.first]!;

                //      for (var row in sheet.rows.skip(1)) {
                //           controller.groupMembers.add(
                //               SmsGroupMember(
                //                 firstName: row[0]?.value?.toString() ?? '',
                //               secondName: row[1]?.value?.toString() ?? '',
                //               phoneNumber: row[2]?.value?.toString() ?? ''
                //               )
                //             );
                //         }
                //       }
                //     }
                //   },
                //   icon:  const Icon(Icons.upload_file),
                // ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPhoneNumber(context),
            const SizedBox(height: 16),
            Obx(
              () => ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.groupMembers.length,
                itemBuilder: (context, index) {
                  final member = controller.groupMembers[index];
                  return Card(
                    child: ListTile(
                      title: Text('${member.firstName} ${member.secondName}'),
                      subtitle: Text(member.phoneNumber ?? ''),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () {
                              final firstNameController =
                                  TextEditingController(text: member.firstName);
                              final secondNameController =
                                  TextEditingController(
                                      text: member.secondName);
                              final phoneController = TextEditingController(
                                  text: member.phoneNumber);

                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Edit Member'),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      TextField(
                                        controller: firstNameController,
                                        decoration: const InputDecoration(
                                            labelText: 'First Name'),
                                      ),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: secondNameController,
                                        decoration: const InputDecoration(
                                            labelText: 'Second Name'),
                                      ),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: phoneController,
                                        decoration: const InputDecoration(
                                            labelText: 'Phone Number'),
                                      ),
                                      const SizedBox(height: 8)
                                    ],
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: const Text('Cancel'),
                                    ),
                                    Obx(
                                      () => MyButton(
                                        onClick: () {
                                          controller.groupMembers[index] =
                                              SmsGroupMember(
                                                  firstName:
                                                      firstNameController.text,
                                                  secondName:
                                                      secondNameController.text,
                                                  phoneNumber:
                                                      phoneController.text,
                                                  id: controller
                                                      .groupMembers[index].id,
                                                  createdAt: controller
                                                      .groupMembers[index]
                                                      .createdAt,
                                                  updatedAt: controller
                                                      .groupMembers[index]
                                                      .updatedAt,
                                                  deletedAt: null,
                                                  smsGroupId: controller
                                                      .groupMembers[index]
                                                      .smsGroupId);
                                          controller.editGroupMember(controller
                                              .groupMembers[index]
                                              .toJson());
                                        },
                                        label: 'Save',
                                        width: 160.w,
                                        showLoading:
                                            controller.isEditingMember.value,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Delete Member'),
                                  content: const Text(
                                      'Are you sure you want to delete this member?'),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: const Text('Cancel'),
                                    ),
                                    Obx(
                                      () => MyButton(
                                        width: 160.w,
                                        showLoading:
                                            controller.isDeletingMember.value,
                                        onClick: () {
                                          controller.deleteGroupMember(
                                              controller.groupMembers[index]
                                                  .toJson(),
                                              widget.smsGroups.id ?? 0);
                                          setState(() {});
                                        },
                                        label: 'Delete',
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
