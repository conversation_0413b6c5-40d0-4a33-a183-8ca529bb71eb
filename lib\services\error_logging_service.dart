import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// Comprehensive error logging service
class ErrorLoggingService {
  static final Logger _logger = Logger();
  static bool _isFirebaseAvailable = false;

  /// Initialize error logging
  static Future<void> initialize() async {
    try {
      if (Firebase.apps.isNotEmpty) {
        _isFirebaseAvailable = true;
        _logger.i('Error logging initialized with Firebase');
      }
    } catch (e) {
      _logger.w('Firebase not available for error logging: $e');
      _isFirebaseAvailable = false;
    }
  }

  /// Log KYC specific errors
  static void logKYCError(String operation, dynamic error, {
    String? userId,
    String? imageType,
    Map<String, dynamic>? additionalData,
  }) {
    final errorData = {
      'operation': operation,
      'error': error.toString(),
      'userId': userId,
      'imageType': imageType,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    _logger.e('KYC Error: $operation', error: error);

    if (_isFirebaseAvailable && !kDebugMode) {
      try {
        FirebaseCrashlytics.instance.recordError(
          error,
          StackTrace.current,
          fatal: false,
          information: [
            DiagnosticsProperty('operation', operation),
            DiagnosticsProperty('userId', userId),
            DiagnosticsProperty('imageType', imageType),
          ],
        );
      } catch (e) {
        _logger.w('Failed to log to Firebase: $e');
      }
    }
  }

  /// Log upload errors
  static void logUploadError(String filename, dynamic error, {
    int? attempt,
    String? uploadMethod,
  }) {
    logKYCError('upload_error', error, additionalData: {
      'filename': filename,
      'attempt': attempt,
      'uploadMethod': uploadMethod,
    });
  }

  /// Log validation errors
  static void logValidationError(String validationType, dynamic error, {
    String? filename,
    int? fileSize,
  }) {
    logKYCError('validation_error', error, additionalData: {
      'validationType': validationType,
      'filename': filename,
      'fileSize': fileSize,
    });
  }

  /// Log API errors
  static void logAPIError(String endpoint, dynamic error, {
    int? statusCode,
    String? method,
  }) {
    logKYCError('api_error', error, additionalData: {
      'endpoint': endpoint,
      'statusCode': statusCode,
      'method': method,
    });
  }
}