import 'dart:async';
import 'package:flutter/foundation.dart';

/// A service that monitors memory usage of the application
class MemoryMonitor {
  static bool _isMonitoring = false;
  static Timer? _monitoringTimer;
  static final StreamController<MemoryUsage> _memoryStreamController = 
      StreamController<MemoryUsage>.broadcast();

  /// Stream of memory usage snapshots
  static Stream<MemoryUsage> get memoryStream => _memoryStreamController.stream;

  /// Start monitoring memory usage
  static void startMonitoring({Duration frequency = const Duration(seconds: 5)}) {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(frequency, (_) {
      _checkMemoryUsage();
    });
    
    // Initial check
    _checkMemoryUsage();
  }

  /// Stop monitoring memory usage
  static void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
  }

  /// Check current memory usage
  static Future<void> _checkMemoryUsage() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      _memoryStreamController.add(memoryInfo);
      
      // Check if memory usage is high and print a warning
      if (memoryInfo.usedBytes > 100 * 1024 * 1024) { // 100 MB threshold
        print('WARNING: High memory usage detected: ${(memoryInfo.usedBytes / (1024 * 1024)).toStringAsFixed(2)} MB');
      }
    } catch (e) {
      print('Error checking memory: $e');
    }
  }

  /// Get memory information from an isolate to prevent UI thread blocking
  static Future<MemoryUsage> _getMemoryInfo() async {
    return await compute(_isolateGetMemoryInfo, null);
  }

  /// The actual memory check function that runs in the isolate
  static MemoryUsage _isolateGetMemoryInfo(_) {
    // Get current memory information
    final memoryInfo = MemoryUsage();
    
    // Return the memory usage information
    return memoryInfo;
  }

  /// Manually trigger a memory check
  static Future<MemoryUsage> checkMemory() async {
    final memoryInfo = await _getMemoryInfo();
    _memoryStreamController.add(memoryInfo);
    return memoryInfo;
  }

  /// Clean up resources
  static void dispose() {
    stopMonitoring();
    _memoryStreamController.close();
  }
}

/// Class representing memory usage information
class MemoryUsage {
  final int totalBytes;
  final int usedBytes;
  final DateTime timestamp;

  MemoryUsage({
    int? totalBytes,
    int? usedBytes,
  }) : 
    totalBytes = totalBytes ?? 0,
    usedBytes = usedBytes ?? 0,
    timestamp = DateTime.now();

  /// Format used memory as MB
  String get usedMemoryMB => '${(usedBytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  
  /// Format total memory as MB
  String get totalMemoryMB => '${(totalBytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  
  /// Get percentage of memory used
  double get percentUsed => totalBytes > 0 ? (usedBytes / totalBytes) * 100 : 0;
  
  @override
  String toString() => 'Memory Usage: $usedMemoryMB / $totalMemoryMB (${percentUsed.toStringAsFixed(1)}%)';
} 