class Ticket {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final num? price;
  final String? currency;
  final int? quantity;
  final String? ticketType;
  final String? description;
  final int? eventId;
  final Status? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? groupSize;
  // final String? slotType;
  // final DateTime? purchaseDate, purchaseEndDate;
  // final int? slotLength;

  Ticket( {
    this.groupSize = 0,
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title = '',
    this.quantity,
    this.price,
    this.status,
    this.currency = '',
    this.ticketType = '',
    this.description = '',
    this.eventId,
    this.startDate,
    this.endDate,   
  });

  factory Ticket.fromJson(Map<String, dynamic> json) => Ticket(
        groupSize: json["group_size"] ?? 0,
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        title: json["title"] ?? '',
        quantity: json["quantity"],
        price: json["price"] ?? 0.0,
        status: statusValues.map[json["status"]] ?? Status.ACTIVE,
        currency: json["currency"] ?? '',
        ticketType: json["ticket_type"] ?? '',
        description: json["description"] ?? '',
        eventId: json["event_id"] ?? 0,
        startDate: json["start_date"] != null
            ? DateTime.parse(json["start_date"])
            : null,
        endDate:
            json["end_date"] != null ? DateTime.parse(json["end_date"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "group_size": groupSize,
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "quantity": quantity,
        "price": price,
        "status": statusValues.reverse[status] ?? '',
        "currency": currency,
        "ticket_type": ticketType,
        "description": description,
        "event_id": eventId,
        "start_date": startDate?.toUtc().toIso8601String(),
        "end_date": endDate?.toUtc().toIso8601String(),
      };
}

enum Status { ACTIVE, DELETED }

final statusValues =
    EnumValues({"ACTIVE": Status.ACTIVE, "DELETED": Status.DELETED});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}

enum TicketStatus { ACTIVE, DELETED }

final ticketStatusValues = EnumValues(
    {"ACTIVE": TicketStatus.ACTIVE, 'DELETED': TicketStatus.DELETED});
