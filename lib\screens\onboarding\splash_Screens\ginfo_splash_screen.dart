// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:liquid_swipe/liquid_swipe.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/services/location_service.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:onekitty/main.dart' show isLight;
class GeneralInfoSplashScreen extends StatefulWidget {
  const GeneralInfoSplashScreen({super.key});

  @override
  State<GeneralInfoSplashScreen> createState() =>
      _GeneralInfoSplashScreenState();
} 

class _GeneralInfoSplashScreenState extends State<GeneralInfoSplashScreen>
    with SingleTickerProviderStateMixin {
  final LiquidController _liquidController = LiquidController();
  int _currentPageIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Color> _liquidColors = [
    isLight.value ? Colors.white : Color(0xFF1A1A1A),
    isLight.value ? Color(0xFFF5F5F5) : Color(0xFF2A2A2A),
    isLight.value ? Color(0xFFEAEAEA) : Color(0xFF3A3A3A),
  ];

  final LocationService _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1000),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
    
    // Request location permission during onboarding
    _requestLocationPermission();
  }
  
  /// Request location permission
  Future<void> _requestLocationPermission() async {
    // Request location permission - if denied, app will continue without location
    await _locationService.requestLocationPermission();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildDotIndicator() => AnimatedSmoothIndicator(
        activeIndex: _currentPageIndex,
        count: 3,
        effect: ExpandingDotsEffect(
          activeDotColor: theme.colorScheme.secondaryContainer,
          dotColor: appTheme.indigo50,
          dotHeight: 8.h,
          dotWidth: 8.w,
          spacing: 12,
          expansionFactor: 3,
        ),
      );

  Widget _buildLiquidPage(Widget content) => Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _liquidColors[_currentPageIndex],
              _liquidColors[(_currentPageIndex + 1) % _liquidColors.length],
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 42.h),
          child: content,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          LiquidSwipe(
            pages: [
              _buildWelcomeSplash(context),
              _CreateKittySplash(context),
              _ContributionKittySplash(context),
            ],
            liquidController: _liquidController,
            onPageChangeCallback: (index) {
              setState(() {
                _currentPageIndex = index;
                _animationController.reset();
                _animationController.forward();
              });
            },
            waveType: WaveType.liquidReveal,
            positionSlideIcon: 0.5,
            slideIconWidget: Icon(Icons.arrow_back_ios,), //color: Colors.black),
            enableSideReveal: true,
          ),
          Positioned(
            bottom: 30.h,
            left: 0,
            right: 0,
            child: Column(
              children: [
                _buildDotIndicator(),
                SizedBox(height: 20.h),
                _buildLoginButtons(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButtons(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 25.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
              onPressed: () => Get.toNamed('/loginPage'),
              child: Text(
                "Skip",
                style: theme.textTheme.bodyMedium?.copyWith(
                  //color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Get.toNamed('/loginPage'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.secondaryContainer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.w),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 25.w,
                  vertical: 12.h,
                ),
              ),
              child: Text(
                "Log in",
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSplash(BuildContext context) {
    return _buildLiquidPage(
      Column(
        children: [
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            child: CustomImageView(
              key: ValueKey<int>(_currentPageIndex),
              imagePath: AssetUrl.imgGroup6,
              height: 265.h,
              width: 366.w,
            ),
          ),
          SizedBox(height: 70.h),
          SlideTransition(
            position: Tween<Offset>(
              begin: Offset(0, 0.5),
              end: Offset.zero,
            ).animate(_fadeAnimation),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                "Welcome to OneKitty",
                style: theme.textTheme.titleLarge?.copyWith(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ),
          ),
          SizedBox(height: 14.h),
          ScaleTransition(
            scale: _fadeAnimation,
            child: Container(
              width: 336.w,
              margin: EdgeInsets.symmetric(horizontal: 0.w),
              child: Text(
                "An all in one platform that helps you create and manage social contributions from friends and family",
                textAlign: TextAlign.center,
                style: CustomTextStyles.bodyMediumGray600.copyWith(
                  fontSize: 16.sp,
                  height: 1.5,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _CreateKittySplash(BuildContext context) {
    return _buildLiquidPage(
      Column(
        children: [
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            child: CustomImageView(
              key: ValueKey<int>(_currentPageIndex),
              imagePath: AssetUrl.imgGroup9,
              height: 265.h,
              width: 366.w,
            ),
          ),
          SizedBox(height: 40.h),
          SlideTransition(
            position: Tween<Offset>(
              begin: Offset(0, 0.5),
              end: Offset.zero,
            ).animate(_fadeAnimation),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                "Create a contribution Kitty",
                style: theme.textTheme.titleLarge?.copyWith(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ),
          ),
          SizedBox(height: 7.h),
          ScaleTransition(
            scale: _fadeAnimation,
            child: Container(
              width: 350.w,
              margin: EdgeInsets.symmetric(horizontal: 10.w),
              child: Text(
                "One app, endless possibilities. Easily contribute with friends and family to anything",
                textAlign: TextAlign.center,
                style: CustomTextStyles.bodyLargeOnPrimaryContainer.copyWith(
                  fontSize: 16.sp,
                  height: 1.5,
                ),
              ),
            ),
          ),
          SizedBox(height: 20.h),
          FadeTransition(
            opacity: _fadeAnimation,
            child: CustomElevatedButton(
              onPressed: () => Get.toNamed(NavRoutes.createkittyScreen),
              text: "Create a Kitty",
              buttonStyle: ElevatedButton.styleFrom(
                elevation: 5,
                shadowColor: isLight.value ? Colors.black26 : Colors.white12,
                shape: StadiumBorder(),
                padding: EdgeInsets.symmetric(vertical: 2.h),
              ),
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _ContributionKittySplash(BuildContext context) {
    return _buildLiquidPage(
      Column(
        children: [
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            child: CustomImageView(
              key: ValueKey<int>(_currentPageIndex),
              imagePath: AssetUrl.imgGroup10,
              height: 265.h,
              width: 366.w,
            ),
          ),
          SizedBox(height: 50.h),
          SlideTransition(
            position: Tween<Offset>(
              begin: Offset(0, 0.5),
              end: Offset.zero,
            ).animate(_fadeAnimation),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                "Join hands with friends",
                style: theme.textTheme.titleLarge?.copyWith(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ),
          ),
          SizedBox(height: 13.h),
          ScaleTransition(
            scale: _fadeAnimation,
            child: Container(
              width: 322.h,
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              child: Text(
                "Join your friends in lending a hand to a common goal and be part of something special",
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontSize: 16.sp,
                  height: 1.5,
                ),
              ),
            ),
          ),
          SizedBox(height: 21.h),
          FadeTransition(
            opacity: _fadeAnimation,
            child: CustomElevatedButton(
              onPressed: () => Get.toNamed(NavRoutes.urlScreen),
              text: "Contribute to a Kitty",
              buttonStyle: ElevatedButton.styleFrom(
                elevation: 5,
                shadowColor: isLight.value ? Colors.black26 : Colors.white12,
                shape: StadiumBorder(),
                padding: EdgeInsets.symmetric(vertical: 2.h),
              ),
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}