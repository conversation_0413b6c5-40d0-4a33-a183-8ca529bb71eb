import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/pdf_null_safety_helper.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart' show rootBundle;

Future<Uint8List> makeFullContributionsPdf(
    List<TransactionModel> tra, Kitty? kitty,
    {int? eventId}) async {
  final event = eventId != null
      ? Get.find<ViewSingleEventController>().event.value
      : null;
  UserKittyController userController = Get.find();
  final DataController dataController = Get.put(DataController());
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title:
        "${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}_${event?.id ?? dataController.kitty.value.kitty?.iD ?? ''}_transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject:
        "${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}_${event?.id ?? dataController.kitty.value.kitty?.iD ?? ''}}_transaction statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );
  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                    "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}"),
                Text("${userController.getLocalUser()?.phoneNumber?.replaceRange(6, 9, "***")}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          // Wrap(
          //   children: [
          Padding(
            child: Text(
              "${event?.title ?? dataController.kitty.value.kitty?.title ?? ''} transaction statement",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(1),
          ),
          if (event?.email != null && event?.email != "")
            Text(
              PDFNullSafetyHelper.getEmail(event?.email),
              textAlign: TextAlign.center,
            ),
          pw.UrlLink(
              child: pw.Text(event != null
                  ? "Event Link: www.onekitty.co.ke/events/${event.username}"
                  : '${event != null ? "Event" : 'Kitty'} Link: onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD ?? ''}'),
              destination:
                  'https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD ?? ''}'),
          Text(
              "${event != null ? "Event" : 'Kitty'} Balance: KSH ${event?.balance ?? dataController.kitty.value.kitty?.balance ?? 0}"),
          Padding(
            child: Text(
                "Accrued Balance: KSH ${dataController.kitty.value.kitty?.balance ?? 0}"),

            // child: Text("Accrued Balance: KSH ${kitty?.availableBalance ?? 0}"),
            padding: const EdgeInsets.only(bottom: 9),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              ...tra.map(
                (e) {
                  final phone =
                      UserKittyController().maskString(e.phoneNumber ?? "");

                  return TableRow(
                    decoration: BoxDecoration(
                      color:
                          (e.id ?? 0) % 2 == 0 ? PdfColor.fromHex("#DADEF4") : null,
                    ),
                    children: [
                      Expanded(
                        child: PaddedText(
                          e.createdAt != null 
                              ? dateformat.format(e.createdAt!.toLocal())
                              : 'N/A',
                        ),
                      ),
                      Expanded(
                        child: PaddedText(
                          e.transactionCode ?? '',
                        ),
                        // flex: 2,
                      ),
                      Expanded(
                        child: PaddedText(
                            "${e.firstName ?? ''}${e.firstName != null && e.secondName != null ? ' ' : ''}${e.secondName ?? ''}\n $phone"),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(10),
                          child: Text(
                              e.typeInOut == "IN"
                                  ? "+ KSH ${e.amount ?? 0}"
                                  : "- KSH ${e.amount ?? 0}",
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  color: e.typeInOut == "IN"
                                      ? PdfColors.green
                                      : PdfColors.red)),
                        ),
                        flex: 1,
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
          Expanded(
              child: Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          )),
          Text(
            "Onekitty is a contribution patform that enables you to receive realtime contribution updates on Whatsapp.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}

Widget PaddedText(
  final String text, {
  final TextAlign align = TextAlign.left,
}) =>
    Padding(
      padding: const EdgeInsets.all(10),
      child: Text(
        text,
        textAlign: align,
      ),
    );

//USER TRANSACTION PDF
Future<Uint8List> makeFullUserContributionsPdf(List<TransactionModel> userTra) async {
  UserKittyController userController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title: "My Transaction Statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "User Statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );
  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                    "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}"),
                Text("${userController.getLocalUser()?.phoneNumber?.replaceRange(6, 9, "***")}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          // Wrap(
          //   children: [
          Padding(
            child: Text(
              "Full transaction statement",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(1),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Type",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              ...userTra.map(
                (e) {
                  UserKittyController()
                      .maskString(e.phoneNumber ?? "NO PHONE NUMBER");

                  return TableRow(
                    decoration: BoxDecoration(
                      color:
                          (e.id ?? 0) % 2 == 0 ? PdfColor.fromHex("#DADEF4") : null,
                    ),
                    children: [
                      Expanded(
                        child: PaddedText(
                          e.createdAt != null 
                              ? dateformat.format(e.createdAt!.toLocal())
                              : 'N/A',
                        ),
                      ),
                      Expanded(
                        child: PaddedText(
                          e.transactionCode ?? '',
                        ),
                        // flex: 2,
                      ),
                      Expanded(
                        child: PaddedText(
                            e.typeInOut == "IN" ? "PAID IN" : "PAID OUT"),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(10),
                          child: Text(
                              e.typeInOut == "IN"
                                  ? "+ KSH ${e.amount ?? 0}"
                                  : "- KSH ${e.amount ?? 0}",
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  color: e.typeInOut == "IN"
                                      ? PdfColors.green
                                      : PdfColors.red)),
                        ),
                        flex: 1,
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
          Expanded(
              child: Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          )),
          Text(
            "Onekitty is a contribution patform that enables you to receive realtime contribution updates on Whatsapp.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}

Future<Uint8List> makeUserSinglePdf(TransactionModel transaction) async {
  UserKittyController userController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title: "Transaction Statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "Transaction Statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );

  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                  "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}",
                ),
                Text("${userController.getLocalUser()?.phoneNumber?.replaceRange(6, 9, "***")}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          Padding(
            child: Text(
              "Transaction Details",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(10),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Type",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              TableRow(
                decoration: BoxDecoration(color: PdfColor.fromHex("#DADEF4")),
                children: [
                  Expanded(
                    child: PaddedText(
                      transaction.createdAt != null 
                          ? dateformat.format(transaction.createdAt!.toLocal())
                          : 'N/A',
                    ),
                  ),
                  Expanded(
                    child: PaddedText(transaction.transactionCode ?? ''),
                  ),
                  Expanded(
                    child: PaddedText(
                        transaction.typeInOut == "IN" ? "PAID IN" : "PAID OUT"),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Text(
                        transaction.typeInOut == "IN"
                            ? "+ KSH ${transaction.amount ?? 0}"
                            : "- KSH ${transaction.amount ?? 0}",
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: transaction.typeInOut == "IN"
                              ? PdfColors.green
                              : PdfColors.red,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          ),
          Text(
            "Onekitty is a contribution platform that enables you to receive real-time contribution updates on Whatsapp.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}

Future<Uint8List> makeKittySinglePdf(TransactionModel transaction,
    {int? eventId}) async {
  UserKittyController userController = Get.find();
  final DataController dataController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');
  final event = eventId != null
      ? Get.find<ViewSingleEventController>().event.value
      : null;

  final pdf = Document(
    title:
        "${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}_${event?.id ?? dataController.kitty.value.kitty?.iD ?? ''}_transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "Transaction Statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );

  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                  "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}",
                ),
                Text("${userController.getLocalUser()?.phoneNumber?.replaceRange(6, 9, "***")}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          Padding(
            child: Text(
              "${event?.title ?? dataController.kitty.value.kitty?.title ?? ''} transaction statement",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(1),
          ),
           if (event?.email != null && event?.email != "")
            Text(
              PDFNullSafetyHelper.getEmail(event?.email),
              textAlign: TextAlign.center,
            ),
          pw.UrlLink(
              child: pw.Text(event != null
                  ? 'Event Link: onekitty.co.ke/events/${event.username}'
                  : 'Kitty Link: onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD ?? ''}'),
              destination: event != null
                  ? 'https://onekitty.co.ke/events/${event.username}'
                  : 'https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD ?? ''}'),
          Text(
              "${event != null ? 'Event' : 'Kitty'} Balance: KSH ${event?.balance ?? dataController.kitty.value.kitty?.balance ?? 0}"),
          Padding(
            child: Text(
                "Accrued Balance: KSH ${event?.balance ?? dataController.kitty.value.kitty?.balance ?? 0}"),
            padding: const EdgeInsets.only(bottom: 9),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              TableRow(
                decoration: BoxDecoration(color: PdfColor.fromHex("#DADEF4")),
                children: [
                  Expanded(
                    child: PaddedText(
                      transaction.createdAt != null 
                          ? dateformat.format(transaction.createdAt!.toLocal())
                          : 'N/A',
                    ),
                  ),
                  Expanded(
                    child: PaddedText(transaction.transactionCode ?? ''),
                  ),
                  Expanded(
                    child: PaddedText(
                        "${transaction.firstName ?? ''}${transaction.firstName != null && transaction.secondName != null ? ' ' : ''}${transaction.secondName ?? ''}\n"),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Text(
                        transaction.typeInOut == "IN"
                            ? "+ KSH ${transaction.amount ?? 0}"
                            : "- KSH ${transaction.amount ?? 0}",
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: transaction.typeInOut == "IN"
                              ? PdfColors.green
                              : PdfColors.red,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          ),
          Text(
            "Onekitty is a contribution platform that enables you to receive real-time contribution updates on Whatsapp.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}
