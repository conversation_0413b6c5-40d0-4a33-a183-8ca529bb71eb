import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import '../views/capture_selfie.dart';
import '../widgets/clay_progress_bar.dart';
import '../controllers/kyc_controller.dart';

class CaptureBackIDPage extends StatefulWidget {
  const CaptureBackIDPage({super.key});

  @override
  State<CaptureBackIDPage> createState() => _CaptureBackIDPageState();
}

class _CaptureBackIDPageState extends State<CaptureBackIDPage> {
  final KYCController controller = Get.find<KYCController>();
  CameraController? cameraController;
  bool isCameraInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    final status = await Permission.camera.status;
    if (status.isDenied) {
      final result = await Permission.camera.request();
      if (result.isDenied) {
        Get.snackbar(
          'Permission Denied',
          'Camera permission is required to capture ID',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red[200],
        );
        return;
      }
    }

    final cameras = await availableCameras();
    if (cameras.isEmpty) return;

    cameraController = CameraController(
      cameras[0],
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await cameraController!.initialize();
      if (mounted) {
        setState(() {
          isCameraInitialized = true;
        });
      }
    } catch (e) {
      Get.snackbar(
        'Camera Error',
        'Failed to initialize camera: ',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  Future<void> _captureImage() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      Get.snackbar(
        'Error',
        'Camera not initialized. Please restart the camera.',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red[200],
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      final XFile image = await cameraController!.takePicture();
      final File imageFile = File(image.path);

      // Validate the captured image
      if (!await imageFile.exists()) {
        throw Exception('Failed to save captured image');
      }

      final fileSizeInBytes = await imageFile.length();
      if (fileSizeInBytes == 0) {
        throw Exception('Captured image is empty');
      }

      controller.backID.value = imageFile;
      controller.isBackValid.value = true;

      Get.snackbar(
        'Success',
        'Back ID captured successfully!',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.green[200],
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to capture image: ${e.toString()}',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red[200],
        duration: const Duration(seconds: 3),
      );
    }
  }

  /* Future<void> _validateDocument(File image) async {
    try {
      final textDetector = GoogleMlKit.vision.textRecognizer();
      final inputImage = InputImage.fromFile(image);
      final recognisedText = await textDetector.processImage(inputImage);
      await textDetector.close();

      if (recognisedText.text.isEmpty) {
        Get.snackbar(
          'Invalid Document',
          'No text detected in the image',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red[200],
        );
        controller.backID.value = null;
        return;
      }

      // Additional validation for back side specific features
      if (!recognisedText.text.contains(controller.idNumber.text)) {
        Get.snackbar(
          'Validation Warning',
          'ID number not found on document back',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.orange[200],
        );
      }
    } catch (e) {
      Get.snackbar(
        'Validation Error',
        'Failed to process document: ',
        snackPosition: SnackPosition.bottom,
      );
      controller.backID.value = null;
    }
  }
*/
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isLight = theme.brightness == Brightness.light;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Capture Back ID'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                const ClayProgress(
                  currentStep: 3,
                  totalSteps: 5,
                ),
                const SizedBox(height: 30),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: isLight ? Colors.white : Colors.black12,
                          offset: const Offset(-6, -6),
                          blurRadius: 12,
                        ),
                        BoxShadow(
                          color:
                              isLight ? Colors.grey.shade400 : Colors.black26,
                          offset: const Offset(6, 6),
                          blurRadius: 12,
                        ),
                      ],
                    ),
                    child: Center(
                      child: controller.backID.value == null
                          ? _buildCameraPreview()
                          : _buildImagePreview(theme),
                    ),
                  ),
                ),
              ],
            ),
          )),
      floatingActionButton:
          controller.backID.value == null ? _buildCaptureButton(theme) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildCameraPreview() {
    if (!isCameraInitialized) {
      return const Center(child: CircularProgressIndicator());
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: CameraPreview(cameraController!),
    );
  }

  Widget _buildImagePreview(ThemeData theme) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Image.file(controller.backID.value!),
        ),
        Positioned(
          bottom: 20,
          right: 20,
          child: FloatingActionButton(
            backgroundColor: theme.colorScheme.surface,
            onPressed: () => Get.to(() => const CaptureSelfiePage()),
            child: Icon(Icons.check, color: theme.colorScheme.primary),
          ),
        ),
      ],
    );
  }

  Widget _buildCaptureButton(ThemeData theme) {
    return Container(
      height: 80,
      width: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.white
                : Colors.black12,
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.grey.shade400
                : Colors.black26,
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40),
          onTap: _captureImage,
          child: Icon(
            Icons.camera_alt,
            size: 32,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }
}
