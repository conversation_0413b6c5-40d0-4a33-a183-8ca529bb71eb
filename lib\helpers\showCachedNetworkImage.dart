import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart'; 
import 'package:photo_view/photo_view.dart';
import 'package:shimmer/shimmer.dart';
// Create a constant shimmer placeholder to reuse it
class ShimmerPlaceholder extends StatelessWidget {
  final double? height;
  final double? width;

  const ShimmerPlaceholder({
    super.key,
    this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          height: height ?? 120,
          width: width ?? 120,
          color: Colors.white,
        ),
      ),
    );
  }
}

class ShowCachedNetworkImage extends StatelessWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageurl,
    this.loaderSize,
    this.fit,
    this.height,
    this.width,
    this.black = false,
    this.telegramLoading = false,
    this.zoomImage = false,
  });

  final String imageurl;
  final double? loaderSize;
  final double? height;
  final BoxFit? fit;
  final double? width;
  final bool? black;
  final bool telegramLoading;
  final bool zoomImage;

  @override
  Widget build(BuildContext context) {
    if (imageurl.isEmpty) {
      return const Icon(
        Icons.error,
        color: Colors.red,
      );
    }

    Widget imageWidget = FastCachedImage(
      url: imageurl,
      fit: fit ?? BoxFit.cover,
      height: height ?? 25,
      width: width ?? 35,
      // Use a more efficient loading builder
      loadingBuilder: (context, url) {
        if (telegramLoading) {
          return ShimmerPlaceholder(
            height: height,
            width: width,
          );
        }
        return Container(
          height: height ?? 25,
          width: width ?? 25,
          color: black ?? false ? Colors.black : Theme.of(context).primaryColor,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
      errorBuilder: (context, url, error) {
        // Don't log in release mode
        Logger().e(error);
        return const Icon(Icons.error);
      },
    );

    if (zoomImage) {
      return GestureDetector(
        onTap: () {
          _showZoomableImage(context);
        },
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  void _showZoomableImage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              PhotoView(
                imageProvider: NetworkImage(imageurl),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                initialScale: PhotoViewComputedScale.contained,
                backgroundDecoration: const BoxDecoration(
                  color: Colors.black,
                ),
              ),
              Positioned(
                top: 40,
                left: 20,
                child: IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}