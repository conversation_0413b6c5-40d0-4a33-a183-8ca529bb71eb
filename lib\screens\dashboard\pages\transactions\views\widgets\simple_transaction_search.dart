// Simple Transaction Search Widget
// Simplified search widget with working colors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onekitty/helpers/colors.dart';
import '../../models/transaction_type.dart';

class SimpleTransactionSearch extends StatelessWidget {
  final String searchQuery;
  final Function(String) onSearchChanged;
  final TransactionType transactionType;

  const SimpleTransactionSearch({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
    required this.transactionType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.slate),
      ),
      child: TextField(
        onChanged: onSearchChanged,
        decoration: InputDecoration(
          hintText: _getSearchHint(),
          hintStyle: const TextStyle(
            color: Colors.black54,
            fontSize: 14,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.neutralGrey,
          ),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: AppColors.neutralGrey,
                  ),
                  onPressed: () => onSearchChanged(''),
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 14,
        ),
      ),
    );
  }

  String _getSearchHint() {
    switch (transactionType) {
      case TransactionType.user:
        return 'Search your transactions...';
      case TransactionType.kitty:
        return 'Search kitty transactions...';
      case TransactionType.chama:
        return 'Search chama transactions...';
      case TransactionType.event:
        return 'Search event transactions...';
    }
  }
}
