/// Enum to define the different types of WhatsApp group contexts
enum WhatsAppGroupType {
  event,
  chama,
  kitty,
}

/// Extension to provide display names for WhatsApp group types
extension WhatsAppGroupTypeExtension on WhatsAppGroupType {
  String get displayName {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'Event';
      case WhatsAppGroupType.chama:
        return 'Chama';
      case WhatsAppGroupType.kitty:
        return 'Kitty';
    }
  }

  String get contextDescription {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'Connect a WhatsApp group to receive event notifications and updates';
      case WhatsAppGroupType.chama:
        return 'Connect WhatsApp groups to enable notifications and communication';
      case WhatsAppGroupType.kitty:
        return 'Connect a WhatsApp group to receive transaction notifications';
    }
  }

  String get emptyStateMessage {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'No WhatsApp groups connected to this event';
      case WhatsAppGroupType.chama:
        return 'No WhatsApp Groups Connected';
      case WhatsAppGroupType.kitty:
        return "You don't have any whatsapp groups";
    }
  }

  String get removeConfirmationMessage {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'Are you sure you want to remove this WhatsApp group?\nYou will no longer receive transaction updates for this event.';
      case WhatsAppGroupType.chama:
        return 'Are you sure you want to remove this WhatsApp?\nYou will no longer be receiving transaction updates in your WhatsApp group';
      case WhatsAppGroupType.kitty:
        return 'Are you sure you want to remove this WhatsApp?\nYou will no longer be receiving transaction updates in your WhatsApp group';
    }
  }
}