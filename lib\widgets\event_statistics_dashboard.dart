import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/event_statistics_controller.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/formatted_currency.dart';

/// Comprehensive event statistics dashboard widget
class EventStatisticsDashboard extends StatefulWidget {
  final int eventId;
  final String eventTitle;

  const EventStatisticsDashboard({
    super.key,
    required this.eventId,
    required this.eventTitle,
  });

  @override
  State<EventStatisticsDashboard> createState() => _EventStatisticsDashboardState();
}

class _EventStatisticsDashboardState extends State<EventStatisticsDashboard>
    with SingleTickerProviderStateMixin {
  late EventStatisticsController controller;
  late TabController tabController;

  @override
  void initState() {
    super.initState();
    controller = Get.put(EventStatisticsController());
    tabController = TabController(length: 4, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    await controller.fetchEventStatistics(widget.eventId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.eventTitle} - Statistics'),
        actions: [
          Obx(() => IconButton(
            onPressed: controller.isLoadingStatistics.value
                ? null
                : () => _loadStatistics(),
            icon: controller.isLoadingStatistics.value
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
          )),
        ],
        bottom: TabBar(
          controller: tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Sales', icon: Icon(Icons.trending_up)),
            Tab(text: 'Revenue', icon: Icon(Icons.attach_money)),
            Tab(text: 'Attendance', icon: Icon(Icons.people)),
          ],
        ),
      ),
      body: Obx(() {
        if (controller.isLoadingStatistics.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.hasError.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
                SizedBox(height: 16.h),
                Text(
                  'Failed to load statistics',
                  style: CustomTextStyles.titleMediumff545963,
                ),
                SizedBox(height: 8.h),
                Text(
                  controller.errorMessage.value,
                  style: CustomTextStyles.bodyMediumGray600,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24.h),
                ElevatedButton(
                  onPressed: _loadStatistics,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return TabBarView(
          controller: tabController,
          children: [
            _buildOverviewTab(),
            _buildSalesTab(),
            _buildRevenueTab(),
            _buildAttendanceTab(),
          ],
        );
      }),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key Metrics Cards
          _buildMetricsGrid(),
          SizedBox(height: 24.h),
          
          // Performance Summary
          _buildPerformanceSummary(),
          SizedBox(height: 24.h),
          
          // Top Performing Tickets
          _buildTopPerformingTickets(),
        ],
      ),
    );
  }

  Widget _buildSalesTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ticket Sales by Category',
            style: CustomTextStyles.titleMediumff545963,
          ),
          SizedBox(height: 16.h),
          _buildTicketSalesChart(),
          SizedBox(height: 24.h),
          _buildSalesTrendChart(),
        ],
      ),
    );
  }

  Widget _buildRevenueTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Revenue Analysis',
            style: CustomTextStyles.titleMediumff545963,
          ),
          SizedBox(height: 16.h),
          _buildRevenueBreakdown(),
          SizedBox(height: 24.h),
          _buildRevenueMetrics(),
        ],
      ),
    );
  }

  Widget _buildAttendanceTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Attendance Tracking',
            style: CustomTextStyles.titleMediumff545963,
          ),
          SizedBox(height: 16.h),
          _buildAttendanceBreakdown(),
          SizedBox(height: 24.h),
          _buildAttendanceMetrics(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return Obx(() => GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16.w,
      mainAxisSpacing: 16.h,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          'Total Revenue',
          FormattedCurrency.getFormattedCurrency(controller.totalRevenue.value),
          Icons.attach_money,
          Colors.green,
        ),
        _buildMetricCard(
          'Tickets Sold',
          controller.totalTicketsSold.value.toString(),
          Icons.confirmation_number,
          Colors.blue,
        ),
        _buildMetricCard(
          'Total Attendees',
          controller.totalAttendees.value.toString(),
          Icons.people,
          Colors.orange,
        ),
        _buildMetricCard(
          'Avg. Ticket Price',
          FormattedCurrency.getFormattedCurrency(controller.averageTicketPrice.value),
          Icons.price_change,
          Colors.purple,
        ),
      ],
    ));
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            SizedBox(height: 8.h),
            Text(
              value,
              style: CustomTextStyles.titleMediumff545963.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: CustomTextStyles.bodySmallGray600,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceSummary() {
    return Obx(() {
      final metrics = controller.getPerformanceMetrics();
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Performance Summary',
                style: CustomTextStyles.titleSmallGray900.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 16.h),
              _buildPerformanceRow(
                'Sell-through Rate',
                '${metrics['sell_through_rate'].toStringAsFixed(1)}%',
              ),
              _buildPerformanceRow(
                'Conversion Rate',
                '${metrics['conversion_rate'].toStringAsFixed(1)}%',
              ),
              _buildPerformanceRow(
                'Tickets Remaining',
                '${metrics['tickets_remaining']}',
              ),
              if (controller.topSellingTicketType.value.isNotEmpty)
                _buildPerformanceRow(
                  'Top Selling Type',
                  controller.topSellingTicketType.value,
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildPerformanceRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: CustomTextStyles.bodyMediumGray600),
          Text(
            value,
            style: CustomTextStyles.bodyMediumGray600.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPerformingTickets() {
    return Obx(() {
      final topTickets = controller.getTopPerformingTickets();
      if (topTickets.isEmpty) {
        return const SizedBox();
      }

      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Top Performing Tickets',
                style: CustomTextStyles.titleSmallGray900.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 16.h),
              ...topTickets.map((ticket) => _buildTicketPerformanceItem(ticket)),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTicketPerformanceItem(Map<String, dynamic> ticket) {
    final ticketType = ticket['ticket_type'] ?? 'Unknown';
    final sold = ticket['tickets_sold'] ?? 0;
    final total = ticket['total_tickets'] ?? 0;
    final percentage = ticket['percentage'] ?? 0.0;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ticketType,
                  style: CustomTextStyles.bodyMediumGray600.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$sold / $total sold',
                  style: CustomTextStyles.bodySmallGray600,
                ),
              ],
            ),
          ),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: CustomTextStyles.bodyMediumGray600.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Placeholder methods for charts (would need chart library implementation)
  Widget _buildTicketSalesChart() {
    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('Ticket Sales Chart\n(Chart library needed)'),
      ),
    );
  }

  Widget _buildSalesTrendChart() {
    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('Sales Trend Chart\n(Chart library needed)'),
      ),
    );
  }

  Widget _buildRevenueBreakdown() {
    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('Revenue Breakdown\n(Chart library needed)'),
      ),
    );
  }

  Widget _buildRevenueMetrics() {
    return const SizedBox(); // Placeholder
  }

  Widget _buildAttendanceBreakdown() {
    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('Attendance Breakdown\n(Chart library needed)'),
      ),
    );
  }

  Widget _buildAttendanceMetrics() {
    return const SizedBox(); // Placeholder
  }
}
