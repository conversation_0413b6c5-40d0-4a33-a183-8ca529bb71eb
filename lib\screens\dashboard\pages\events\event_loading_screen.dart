import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'package:onekitty/screens/dashboard/pages/events/view_single_event_deeplink.dart';

class EventLoadingScreen extends StatefulWidget {
  const EventLoadingScreen({
    super.key,
  });

  @override
  State<EventLoadingScreen> createState() => _EventLoadingScreenState();
}

class _EventLoadingScreenState extends State<EventLoadingScreen> {
  final controller = Get.put(EditEventController());

  @override
  void initState() {
    super.initState();
  }

  Future<void> _loadEventData() async {
    try {
      var parameters = Get.parameters;
      String username = parameters['username'] ?? '';
      print('Loading event data for username: $username');
      final res = await controller.fetchEventDetailbyUsername(username);

      if (res) {
        // Ensure event data is properly loaded before navigating
        if (controller.event.value.id != 0) {
          // Navigate to the event viewer with the loaded data
          Get.offAll(
            () => ViewSingleEventViewer(
                isFromDeepLink: true, event: controller.event.value),
            transition: Transition.fadeIn,
          );
        } else {
          print('Event ID is invalid: ${controller.event.value.id}');
          Snack.showInfo(message1: 'Invalid event data');
          Get.offAll(() => BottomNavSection());
        }
      } else {
        // Handle error case
        print('Event not found for username: $username');
        Snack.showInfo(message1: 'Event not found or unavailable');
        Get.offAll(() => BottomNavSection());
      }
    } catch (e) {
      print('Error loading event: $e');
      Snack.showInfo(message1: 'Error loading event');
      Get.offAll(() => BottomNavSection());
    } finally {
      EasyLoading.dismiss(); // Dismiss loading before returning
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      splitScreenMode: true,
      designSize: const Size(392.72727272727275, 850.9090909090909),
      builder: (context, child) {
        return FutureBuilder(
          future: _loadEventData(),
          builder: (context, snapshot) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: AppColors.primary,
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      'Loading Event...',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
