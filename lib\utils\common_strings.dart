class KtStrings {
  static String hideNames =
      "By checking this option,your full names\nwill not be displayed in the\nkitty notification message";
  static String hideNumber =
      "By checking this option,your Number\nwill not be displayed in the kitty\nnotification message";
  static String endDateInfo =
      "When this Date and Time has reached,\nthe kitty will be marked as CLOSED\nand the funds will be sent to the Beneficiary account";

  static String limitText = "This is the maximum amount this kitty should hold";
  static String benfChannel =
      "This is the channel where the beneficiary will receive the money.\nThe account should be registered on the selected Platform";
  static String benfAcc =
      "After all the collection has been done Or when the Kitty closes on the end date, all the funds will be sent to this Number";

  static String whatsAppLink =
      "One-kitty bot will join the Group inorder to notify the members timely on the contribution status of the kitty.\nRemember to provide a valid link.";
  static String whatsAppNameJoin =
      "You can also add this number to your Whatsapp Group.\nThen paste the group name below";

  static String whatsAppJoinMessage =
      "Send a message containing the link and kitty link to our online assistant. Oneki<PERSON> will automatically join the group.";

  static String receiveChannelPayReq =
      "This is the channel where you will receive the payments.\nThe account should be registered on the selected Platform";

  static String errorTry = "Errror, Please try again";
  static String often = "Number of times members contribute within a period";
  static String amount = "Amount each member should contribute";
  static String deadline = "End date of contribution";
  static String phoneNumberOnekitty = "+************";
  static String kittyDescription =
      "This is the general desription of your Kitty";
  static String phone = "This is the phone Number of the Kitty owner";
  static String kittyName = "This is the name of your kitty.";
  static String terms =
      "Privacy Disclosure\nThank you for choosing OneKitty. We are committed to protecting your privacy and ensuring the security of your personal information. In order to provide you with the best possible experience and to fulfill the functionalities of our app, we collect certain information from you. Below is a summary of the information we collect and how it is used:\nContact Information:\nWe may request access to your contacts in order to provide features such as inviting friends to use the app or facilitating communication between users. Your contacts will not be stored or used for any other purpose without your explicit consent.\nBiometric Data:\nOur app may utilize biometric authentication methods such as fingerprint or facial recognition for login purposes. This data is securely stored on your device and is not transmitted to our servers.\nEmail and Password:\nIf you choose to create an account with us, we will collect your email address and password for authentication and account management purposes. Your password is encrypted and stored securely.\nOTP (One-Time Password):\nIn order to verify your identity and enhance the security of your account, we may send OTPs to your messages (SMS). These OTPs are used for login or transaction verification purposes only and are not stored beyond their immediate use.\nPayment Information:\nWhen you use our payment gateway to send or receive money, we collect necessary payment information such as credit/debit card details or bank account information. This data is securely encrypted and processed by our trusted payment partners. We do not store your payment information on our servers.\nWe are committed to safeguarding your personal information and ensuring its confidentiality. We do not sell, trade, or otherwise transfer your information to third parties without your consent, except as required by law or as necessary to provide our services.\nBy using our app, you consent to the collection and use of your information as outlined in this disclosure. If you have any questions or concerns about our privacy practices, please contact us at Tel: +254 ********* Email: <EMAIL> www.onekitty.co.ke.\nThank you for your trust in OneKitty.";
  static String benefPerCycle =
      "These are the number of people receiving funds in a single merry go round";
  static String sigThreshold =
      "These are the number of signatories that are required to sign a transaction for it to be verified. This number can't be more than the number of set signatories.";
  static String benefPercentage =
      "This is the percentage of what the beneficiaries will receive after a contribution. In the sense 50% might go to the beneficiaries set and 50% will remain in the chama wallet";
  static String meetingFrequency =
      "This is how often the meeting shall be held.";
  static String locationTip =
      "Give a tip of a nearby location to help members navigate easily.";
  static String generalPenalty =
      "These are penalties pre-set by the system to make your work easier. You can edit their details to your liking. These are penalties that are commonly found in most chamas";
  static String payerPhone =
      "This is the number you can use to do transactions. Does not have to be part of the chama";
  static String chamaMemberPhNo =
      "This is the phone number associated with the chama member";
}
