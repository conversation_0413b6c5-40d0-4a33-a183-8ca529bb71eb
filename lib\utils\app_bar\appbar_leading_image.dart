import 'package:flutter/material.dart';

// ignore: must_be_immutable
class AppbarLeadingImage extends StatelessWidget {
  AppbarLeadingImage({
    super.key,
    this.imagePath,
    this.margin,
    this.onTap,
  });

  String? imagePath;

  EdgeInsetsGeometry? margin;

  Function? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onTap!.call();
      },
      child: Padding(
        padding: margin ?? EdgeInsets.zero,
        child: CircleAvatar(
          backgroundImage: imagePath != null && imagePath!.isNotEmpty
              ? NetworkImage(imagePath!)
              : null,
          // child: CustomImageView(
          //   imagePath: imagePath,
          //   height: 30.h,
          //   width: 30.w,
          //   fit: BoxFit.contain,
          // ),
        ),
      ),
    );
  }
}
