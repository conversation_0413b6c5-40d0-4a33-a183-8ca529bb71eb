import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:local_auth/local_auth.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/onboarding/blocked_page.dart';
import 'package:onekitty/screens/onboarding/pinput.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/lottie/my_lottie.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:encrypt/encrypt.dart' as crypt;
import '../../helpers/show_snack_bar.dart';
import 'sign_up.dart';
import 'updateKYC/views/kyc_home.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';

// ignore: must_be_immutable
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});
  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  TextEditingController passwordController = TextEditingController();
  bool hidePassword = true;
  bool hasBioCapability = false;
  String myPhone = "";
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  final LocalAuthentication auth = LocalAuthentication();
  GetStorage box = Get.find();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController controller = TextEditingController();
  String initialCountry = 'KE';
  final countryPicker = const FlCountryCodePicker();

  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  bool isValidated = false;

  Future<void> _checkBiometrics() async {
    late bool canCheckBiometrics;
    try {
      canCheckBiometrics = await auth.canCheckBiometrics;

      if (canCheckBiometrics) {
        setState(() {
          hasBioCapability = true;
        });
      }
    } on PlatformException catch (e) {
      canCheckBiometrics = false;

      printError(info: e.details + "===> + ${e.message}");
    }
  }

  enrollBiometrics(String pass) {
    if (hasBioCapability) {
      box.write(CacheKeys.allowsBiometric, true);
      final secureRandom = crypt.SecureRandom(16);

      final encryptKey = crypt.Key.fromUtf8(secureRandom.base64);
      final iv = crypt.IV.fromLength(16);

      box.write(CacheKeys.iv, iv.base64);
      final encrypter = crypt.Encrypter(crypt.AES(encryptKey));

      final encrypted = encrypter.encrypt(pass, iv: iv);

      box.write(CacheKeys.encryption, secureRandom.base64);
      box.write(CacheKeys.encrypted, encrypted.base64);
    }
  }

  @override
  void initState() {
    super.initState();
    _checkBiometrics();
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityCheck(
      child: SafeArea(
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              const MySnowFall(),
              Center(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom,
                  ),
                  child: Form(
                    key: _formKey,
                     
                    child: Container(
                      width: double.maxFinite,
                      padding: EdgeInsets.symmetric(horizontal: 28.h),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(height: 1.h),
                          CustomImageView(
                            imagePath: AssetUrl.logo3,
                            height: 78.h,
                            width: 150.w,
                          ),
                          SizedBox(height: 25.h),
                          Text(
                            "Karibu",
                            style: theme.textTheme.bodyLarge,
                          ),
                          SizedBox(height: 9.h),
                          Text(
                            "Sign In",
                            style: theme.textTheme.titleLarge,
                          ),
                          SizedBox(height: 32.h),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomInternationalPhoneInput(
                                onInputChanged: (num) {
                                  setState(() {
                                    myPhone = num.phoneNumber!;
                                  });
                                },
                                onInputValidated: (bool value) {},
                                initialValue: num,
                                controller: controller,
                                formatInput: true,
                                 
                              ),
                            ],
                          ),
                          // _buildFrame(context),
                          SizedBox(height: 15.h),
                          _buildInputText(context),
                          SizedBox(height: 25.h),
                          Obx(
                            () => CustomKtButton(
                              isLoading: authenticationController
                                  .isLoginloading.isTrue,
                              onPress: () async {
                                if (_formKey.currentState!.validate()) {
                                  final res =
                                      await authenticationController.login(
                                          myPhone,
                                          // controller.text,
                                          passwordController.text);

                                  if (res) {
                                    Snack.showInfo(
                                        message1: authenticationController
                                            .apiMessage.string);
                                    await enrollBiometrics(
                                        passwordController.text);
                                    final user = authenticationController
                                        .usermodel.value;
                                    switch (user.status) {
                                      case 0:
                                        Get.to(() => SignUpPage(
                                              isForgotPasswd: true,
                                              phoneNumber: user.phoneNumber,
                                            ));
                                        break;
                                      case 1:
                                        Get.to(() =>  PinPutPage(phoneNumber: user.phoneNumber));
                                        break;
                                      case 2:
                                        Get.to(() => const KYCHomePage());
                                        break;

                                      case 3:
                                      
                                        Get.offAllNamed(
                                            NavRoutes.bottomNavSection);
                                        break;
                                      case 4:
                                        Get.to(() => const BlockedPage());
                                        break;
                                      default:
                                        Get.to(() => const LoginScreen());
                                    }
                                  } else {
                                    Snack.showError(
                                        message1: authenticationController
                                            .apiMessage.string);
                                  }
                                }
                              },
                              btnText: "Log in",
                              margin: EdgeInsets.only(
                                left: 6.h,
                                right: 2.h,
                              ),
                            ),
                          ),

                          SizedBox(height: 10.h),
                          InkWell(
                            onTap: () {
                              Get.to(() => const SignUpPage(
                                    isForgotPasswd: true,
                                  ));
                            },
                            child: Text(
                              "Forgot your password",
                              style:
                                  CustomTextStyles.bodyLargeSecondaryContainer,
                            ),
                          ),
                          SizedBox(height: 20.h),

                          GestureDetector(
                            onTap: () {
                              Get.to(() => const SignUpPage(
                                    isForgotPasswd: false,
                                  ));
                            },
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "Don't have an account yet? ",
                                    style: CustomTextStyles.bodyLargeff6e6f79,
                                  ),
                                  TextSpan(
                                    text: "Sign up",
                                    style: TextStyle(
                                        color: appTheme.indigo500,
                                        fontSize: 15.sp),
                                  ),
                                ],
                              ),
                              //textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const Positioned(top: 0, right: 8, child: MyLottie()),
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildInputText(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 0.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "Password",
                style: theme.textTheme.titleSmall,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          CustomTextField(
            prefixIcon: Icons.lock,
            controller: passwordController,
            obscureText: hidePassword,
            suffixIcon: GestureDetector(
              onTap: () {
                setState(() {
                  hidePassword = !hidePassword;
                });
              },
              child: hidePassword
                  ? const Icon(
                      Icons.remove_red_eye,
                    )
                  : const Icon(
                      Icons.visibility_off,
                    ),
            ),
            hintText: "Password",
            labelText: "Password",
            isRequired: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "Password required";
              } else {
                return null;
              }
            },
          ),
        ],
      ),
    );
  }
}
