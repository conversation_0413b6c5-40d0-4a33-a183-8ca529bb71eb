import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show MethodChannel;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';

class DeepLinkHelper extends StatefulWidget {
  const DeepLinkHelper({super.key});

  @override
  State<DeepLinkHelper> createState() => _DeepLinkHelperState();
}

class _DeepLinkHelperState extends State<DeepLinkHelper> {
  // Deeplink Permission request
  final _storage = GetStorage();
  bool _neverShowAgain = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      persistentFooterButtons: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 50.h,
              width: 350.w,
              child: Expanded(
                child: FilledButton(
                    onPressed: () async {
                      _storage.write('showDeepLinkDialog', !_neverShowAgain);

                      await _openLinkSettings();

                      _storage.write('showDeepLinkDialog', true);

                      if (mounted) Navigator.of(context).pop();
                    },
                    child: Text(
                      'Continue',
                      style: TextStyle(
                          fontSize: 20.spMin, fontWeight: FontWeight.w600),
                    )),
              ),
            ),
          ],
        ),
      ],
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        actions: [
          TextButton(
              onPressed: () async {
                _storage.write('showDeepLinkDialog', !_neverShowAgain);

                await _openLinkSettings();

                _storage.write('showDeepLinkDialog', true);

                if (mounted) Navigator.of(context).pop();
              },
              child: Text(
                'Continue',
                style:
                    TextStyle(fontSize: 20.spMin, fontWeight: FontWeight.w600),
              ))
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'To enable deep linking, please follow these steps:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    Image.asset('assets/images/add_links_arrow.jpg'),
                    const SizedBox(height: 16),
                    const Text(
                      '1. Click on "Add links" button shown above',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                    Image.asset('assets/images/all_links_dialog.jpg'),
                    const SizedBox(height: 16),
                    const Text(
                      '2. Select all links associated with OneKitty from the list',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Checkbox(
                          value: _neverShowAgain,
                          onChanged: (value) {
                            setState(() {
                              _neverShowAgain = value ?? false;
                            });
                          },
                        ),
                        const Text('Don\'t show this again'),
                      ],
                    ),
                    const SizedBox(height: 100)
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _openLinkSettings() async {
    try {
      const platform = MethodChannel('ke.co.onekitty/deep_link_settings');
      final bool result = await platform.invokeMethod('openLinkSettings');
      if (!result) {
        debugPrint('Failed to open link settings');
      }
    } catch (e) {
      debugPrint('Error opening link settings: $e');
    }
  }
}
