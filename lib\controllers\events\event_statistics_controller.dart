import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

/// Controller for managing event statistics and dashboard data
class EventStatisticsController extends GetxController {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  // Loading states
  final RxBool isLoadingStatistics = false.obs;
  final RxBool isLoadingTicketSales = false.obs;
  final RxBool isLoadingRevenue = false.obs;
  final RxBool isLoadingAttendance = false.obs;

  // Statistics data
  final RxMap<String, dynamic> eventStatistics = <String, dynamic>{}.obs;
  final RxList<Map<String, dynamic>> ticketSalesByCategory = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> revenueStatistics = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> attendanceData = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> salesTrends = <Map<String, dynamic>>[].obs;

  // Summary metrics
  final RxInt totalTicketsSold = 0.obs;
  final RxDouble totalRevenue = 0.0.obs;
  final RxInt totalAttendees = 0.obs;
  final RxDouble averageTicketPrice = 0.0.obs;
  final RxString topSellingTicketType = ''.obs;
  final RxDouble conversionRate = 0.0.obs;

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  /// Fetch comprehensive event statistics
  Future<bool> fetchEventStatistics(int eventId) async {
    try {
      isLoadingStatistics(true);
      hasError(false);
      errorMessage('');

      final response = await apiProvider.request(
        url: "${ApiUrls.EVENT_STATISTICS}?event_id=$eventId",
        method: Method.GET,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'] as Map<String, dynamic>;
        
        // Store raw statistics data
        eventStatistics.value = data;
        
        // Parse and store specific metrics
        _parseStatisticsData(data);
        
        return true;
      } else {
        hasError(true);
        errorMessage(response.data['message'] ?? 'Failed to fetch statistics');
        return false;
      }
    } catch (e) {
      logger.e('Error fetching event statistics: $e');
      hasError(true);
      errorMessage('An error occurred while fetching statistics');
      return false;
    } finally {
      isLoadingStatistics(false);
    }
  }

  /// Parse statistics data and populate observables
  void _parseStatisticsData(Map<String, dynamic> data) {
    try {
      // Ticket sales by category
      if (data['ticket_sales_by_category'] != null) {
        ticketSalesByCategory.value = List<Map<String, dynamic>>.from(
          data['ticket_sales_by_category']
        );
      }

      // Revenue statistics
      if (data['revenue_statistics'] != null) {
        revenueStatistics.value = List<Map<String, dynamic>>.from(
          data['revenue_statistics']
        );
      }

      // Attendance data
      if (data['attendance_data'] != null) {
        attendanceData.value = List<Map<String, dynamic>>.from(
          data['attendance_data']
        );
      }

      // Sales trends
      if (data['sales_trends'] != null) {
        salesTrends.value = List<Map<String, dynamic>>.from(
          data['sales_trends']
        );
      }

      // Summary metrics
      final summary = data['summary'] as Map<String, dynamic>? ?? {};
      totalTicketsSold.value = summary['total_tickets_sold'] ?? 0;
      totalRevenue.value = double.tryParse(summary['total_revenue']?.toString() ?? '0') ?? 0.0;
      totalAttendees.value = summary['total_attendees'] ?? 0;
      averageTicketPrice.value = double.tryParse(summary['average_ticket_price']?.toString() ?? '0') ?? 0.0;
      topSellingTicketType.value = summary['top_selling_ticket_type'] ?? '';
      conversionRate.value = double.tryParse(summary['conversion_rate']?.toString() ?? '0') ?? 0.0;

    } catch (e) {
      logger.e('Error parsing statistics data: $e');
    }
  }

  /// Get ticket sales data for charts
  List<Map<String, dynamic>> getTicketSalesChartData() {
    return ticketSalesByCategory.map((item) => {
      'category': item['ticket_type'] ?? 'Unknown',
      'sold': item['tickets_sold'] ?? 0,
      'total': item['total_tickets'] ?? 0,
      'percentage': item['percentage'] ?? 0.0,
      'revenue': item['revenue'] ?? 0.0,
    }).toList();
  }

  /// Get revenue trend data for charts
  List<Map<String, dynamic>> getRevenueTrendData() {
    return salesTrends.map((item) => {
      'date': item['date'] ?? '',
      'revenue': item['revenue'] ?? 0.0,
      'tickets_sold': item['tickets_sold'] ?? 0,
    }).toList();
  }

  /// Get attendance breakdown data
  List<Map<String, dynamic>> getAttendanceBreakdown() {
    return attendanceData.map((item) => {
      'status': item['status'] ?? 'Unknown',
      'count': item['count'] ?? 0,
      'percentage': item['percentage'] ?? 0.0,
    }).toList();
  }

  /// Calculate performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    final totalTickets = ticketSalesByCategory.fold<int>(
      0, 
      (sum, item) => sum + (item['total_tickets'] as int? ?? 0)
    );
    
    final soldTickets = totalTicketsSold.value;
    final sellThroughRate = totalTickets > 0 ? (soldTickets / totalTickets) * 100 : 0.0;
    
    return {
      'sell_through_rate': sellThroughRate,
      'average_ticket_price': averageTicketPrice.value,
      'total_capacity': totalTickets,
      'tickets_remaining': totalTickets - soldTickets,
      'conversion_rate': conversionRate.value,
    };
  }

  /// Get top performing ticket types
  List<Map<String, dynamic>> getTopPerformingTickets({int limit = 5}) {
    final sortedTickets = List<Map<String, dynamic>>.from(ticketSalesByCategory);
    sortedTickets.sort((a, b) => 
      (b['tickets_sold'] as int? ?? 0).compareTo(a['tickets_sold'] as int? ?? 0)
    );
    
    return sortedTickets.take(limit).toList();
  }

  /// Get recent sales activity
  List<Map<String, dynamic>> getRecentSalesActivity({int days = 7}) {
    final now = DateTime.now();
    final cutoffDate = now.subtract(Duration(days: days));
    
    return salesTrends.where((item) {
      final dateStr = item['date'] as String?;
      if (dateStr == null) return false;
      
      try {
        final date = DateTime.parse(dateStr);
        return date.isAfter(cutoffDate);
      } catch (e) {
        return false;
      }
    }).toList();
  }

  /// Refresh all statistics data
  Future<void> refreshStatistics(int eventId) async {
    await fetchEventStatistics(eventId);
  }

  /// Clear all data
  void clearData() {
    eventStatistics.clear();
    ticketSalesByCategory.clear();
    revenueStatistics.clear();
    attendanceData.clear();
    salesTrends.clear();
    
    totalTicketsSold.value = 0;
    totalRevenue.value = 0.0;
    totalAttendees.value = 0;
    averageTicketPrice.value = 0.0;
    topSellingTicketType.value = '';
    conversionRate.value = 0.0;
    
    hasError.value = false;
    errorMessage.value = '';
  }
}
