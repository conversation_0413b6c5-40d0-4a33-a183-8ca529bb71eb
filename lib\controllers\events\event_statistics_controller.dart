import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/event_statistics_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

/// Controller for managing event statistics and dashboard data
class EventStatisticsController extends GetxController {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  // Loading states
  final RxBool isLoadingStatistics = false.obs;

  // Statistics data using new models
  final Rx<EventStatisticsResponse?> statisticsResponse = Rx<EventStatisticsResponse?>(null);
  final Rx<Overview?> overview = Rx<Overview?>(null);
  final Rx<RevenueBreakdown?> revenueBreakdown = Rx<RevenueBreakdown?>(null);
  final Rx<UserDemographics?> userDemographics = Rx<UserDemographics?>(null);
  final Rx<StatisticsFilters?> filters = Rx<StatisticsFilters?>(null);

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  /// Fetch comprehensive event statistics
  Future<bool> fetchEventStatistics({int? eventId}) async {
    try {
      isLoadingStatistics(true);
      hasError(false);
      errorMessage('');

      String url = ApiUrls.EVENT_STATISTICS;
      if (eventId != null) {
        url += "?event_id=$eventId";
      }

      final response = await apiProvider.request(
        url: url,
        method: Method.GET,
      );

      // Parse the response using the new model
      final statisticsResponseData = EventStatisticsResponse.fromJson(response.data);
      statisticsResponse.value = statisticsResponseData;

      if (statisticsResponseData.status && statisticsResponseData.data != null) {
        // Extract individual components
        final data = statisticsResponseData.data!;
        overview.value = data.statistics?.overview;
        revenueBreakdown.value = data.statistics?.revenueBreakdown;
        userDemographics.value = data.statistics?.userDemographics;
        filters.value = data.filters;

        return true;
      } else {
        hasError(true);
        errorMessage(statisticsResponseData.message.isNotEmpty
            ? statisticsResponseData.message
            : 'Failed to fetch statistics');
        return false;
      }
    } catch (e) {
      logger.e('Error fetching event statistics: $e');
      hasError(true);
      errorMessage('An error occurred while fetching statistics');
      return false;
    } finally {
      isLoadingStatistics(false);
    }
  }

  // Getter methods for accessing data from the new models
  int get totalTicketsSold => overview.value?.totalTicketsSold ?? 0;
  double get totalRevenue => overview.value?.totalRevenue ?? 0.0;
  int get totalAttendees => overview.value?.totalAttendees ?? 0;
  double get averageTicketPrice => overview.value?.averageTicketPrice ?? 0.0;
  int get totalEvents => overview.value?.totalEvents ?? 0;

  double get averageDailyRevenue => revenueBreakdown.value?.averageDailyRevenue ?? 0.0;
  int get totalTransactions => revenueBreakdown.value?.totalTransactions ?? 0;

  int get uniqueCustomers => userDemographics.value?.uniqueCustomers ?? 0;
  int get repeatCustomers => userDemographics.value?.repeatCustomers ?? 0;
  double get repeatCustomerRate => userDemographics.value?.repeatCustomerRate ?? 0.0;
  double get averageTicketsPerUser => userDemographics.value?.averageTicketsPerUser ?? 0.0;

  bool get hasOverviewData => overview.value?.hasData ?? false;
  bool get hasRevenueData => revenueBreakdown.value?.hasData ?? false;
  bool get hasUserDemographicsData => userDemographics.value?.hasData ?? false;

  /// Get basic performance metrics from available data
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'total_events': totalEvents,
      'total_tickets_sold': totalTicketsSold,
      'total_revenue': totalRevenue,
      'total_attendees': totalAttendees,
      'average_ticket_price': averageTicketPrice,
      'average_daily_revenue': averageDailyRevenue,
      'total_transactions': totalTransactions,
      'unique_customers': uniqueCustomers,
      'repeat_customers': repeatCustomers,
      'repeat_customer_rate': repeatCustomerRate,
      'average_tickets_per_user': averageTicketsPerUser,
    };
  }

  /// Get summary data for dashboard cards
  Map<String, dynamic> getSummaryData() {
    return {
      'overview': {
        'total_events': totalEvents,
        'total_tickets_sold': totalTicketsSold,
        'total_revenue': totalRevenue,
        'total_attendees': totalAttendees,
        'average_ticket_price': averageTicketPrice,
      },
      'revenue': {
        'total_revenue': totalRevenue,
        'average_daily_revenue': averageDailyRevenue,
        'total_transactions': totalTransactions,
      },
      'demographics': {
        'unique_customers': uniqueCustomers,
        'repeat_customers': repeatCustomers,
        'repeat_customer_rate': repeatCustomerRate,
        'average_tickets_per_user': averageTicketsPerUser,
      }
    };
  }

  /// Refresh all statistics data
  Future<void> refreshStatistics({int? eventId}) async {
    await fetchEventStatistics(eventId: eventId);
  }

  /// Clear all data
  void clearData() {
    statisticsResponse.value = null;
    overview.value = null;
    revenueBreakdown.value = null;
    userDemographics.value = null;
    filters.value = null;

    hasError.value = false;
    errorMessage.value = '';
  }
}
