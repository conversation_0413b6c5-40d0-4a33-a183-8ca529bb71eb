import 'dart:convert';

SignatoryRequest signatoryRequestFromJson(String str) => SignatoryRequest.fromJson(json.decode(str));

String signatoryRequestToJson(SignatoryRequest data) => json.encode(data.toJson());

class SignatoryRequest {
    int? chamaId;
    int? memberId;
    String? phoneNumber;
    String? notificationType;
    String? email;
    String? whatsAppNumber;

    SignatoryRequest({
        this.chamaId,
        this.memberId,
        this.phoneNumber,
        this.notificationType,
        this.email,
        this.whatsAppNumber,
    });

    factory SignatoryRequest.fromJson(Map<String, dynamic> json) => SignatoryRequest(
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        phoneNumber: json["phone_number"] ?? "",
        notificationType: json["notification_type"],
        email: json["email"],
        whatsAppNumber: json["whats_app_number"],
    );

    Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "member_id": memberId,
        "phone_number": phoneNumber,
        "notification_type": notificationType,
        "email": email,
        "whats_app_number": whatsAppNumber,
    };
}