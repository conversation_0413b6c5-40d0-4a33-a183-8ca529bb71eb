import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart'; 
import '../widgets/clay_progress_bar.dart';
import '../controllers/kyc_controller.dart'; 
import '../widgets/clay_button.dart';

class ReviewSubmitPage extends StatelessWidget {
  final KYCController controller = Get.find<KYCController>();

    ReviewSubmitPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
persistentFooterButtons: [
  _buildSubmitButton(theme),
],
      persistentFooterAlignment:AlignmentDirectional.center,
      appBar: AppBar(
        title: const Text('Review & Submit'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
           const  ClayProgress(
              currentStep: 5,
              totalSteps: 5,
            ),
            const SizedBox(height: 30),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDocumentPreview(
                      'Front ID',
                      controller.frontID.value,
                      controller.isFrontValid.value,
                    ),
                    _buildDocumentPreview(
                      'Back ID',
                      controller.backID.value,
                      controller.isBackValid.value,
                    ),
                    _buildDocumentPreview(
                      'Selfie',
                      controller.selfie.value,
                      controller.isSelfieValid.value,
                    ),
                    const SizedBox(height: 25),
                    _buildIdNumberSection(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            
          ],
        ),
      )),
    );
  }

  Widget _buildDocumentPreview(String title, File? file, bool isValid) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              isValid ? Icons.check_circle : Icons.error,
              color: isValid ? Colors.green : Colors.red,
              size: 20,
            ),
          ],
        ),
        const SizedBox(height: 10),
        Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(Get.context!).colorScheme.surface,
            border: Border.all(
              color: isValid ? Colors.green : Colors.red.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: file != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: Image.file(file, fit: BoxFit.cover),
                )
              : Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.image_not_supported,
                        color: Theme.of(Get.context!)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.4),
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No $title captured',
                        style: TextStyle(
                          color: Theme.of(Get.context!)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.4),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildIdNumberSection() {
    return Obx(() => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'ID Number',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              controller.isIdNumberValid.value ? Icons.check_circle : Icons.error,
              color: controller.isIdNumberValid.value ? Colors.green : Colors.red,
              size: 20,
            ),
          ],
        ),
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(Get.context!).colorScheme.surface,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: controller.isIdNumberValid.value
                  ? Colors.green
                  : Colors.red.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  controller.idNumber.text.isEmpty
                      ? 'No ID number entered'
                      : controller.idNumber.text,
                  style: TextStyle(
                    fontSize: 16,
                    color: controller.idNumber.text.isEmpty
                        ? Theme.of(Get.context!).colorScheme.onSurface.withOpacity(0.4)
                        : null,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (!controller.isIdNumberValid.value && controller.idNumber.text.isNotEmpty)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: Text(
              'ID number must be 7-8 digits',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    ));
  }

  Widget _buildSubmitButton(ThemeData theme) {
    return Obx(() {
      if (controller.isUploading.value) {
        return Column(
          children: [
            CircularProgressIndicator(
              value: controller.uploadProgress.value,
              backgroundColor: theme.colorScheme.surface,
            ),
            const SizedBox(height: 10),
            Text(
              'Uploading ${controller.currentUpload.value}...',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        );
      }

      return ClayButton(
        text: 'Submit KYC',
        icon: Icons.cloud_upload,
        onPressed: () {
          // Check if all documents are ready (includes validation)
          if (!controller.allDocumentsReady) {
            String message = 'Please complete all requirements:\n';
            if (!controller.isFrontValid.value) message += '• Front ID\n';
            if (!controller.isBackValid.value) message += '• Back ID\n';
            if (!controller.isSelfieValid.value) message += '• Selfie\n';
            if (!controller.isIdNumberValid.value) message += '• Valid ID Number\n';

            Get.snackbar(
              'Incomplete Submission',
              message.trim(),
              snackPosition: SnackPosition.bottom,
              backgroundColor: Colors.orange[200],
              duration: const Duration(seconds: 4),
            );
            return;
          }

          showDialog(
            context: Get.context!,
            builder: (context) => AlertDialog(
              title: const Text('Confirm Submission'),
              content: const Text(
                  'Are you sure you want to submit your KYC information?'),
              actions: [
                TextButton(
                  onPressed: Get.back,
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Get.back();
                    controller.uploadImages();
                  },
                  child: const Text('Submit'),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}