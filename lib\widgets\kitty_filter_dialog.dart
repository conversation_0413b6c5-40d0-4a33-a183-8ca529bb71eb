import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:intl/intl.dart';

class KittyFilterDialog extends StatefulWidget {
  final Function(Map<String, String>) onApplyFilter;
  final Map<String, String> currentFilters;

  const KittyFilterDialog({
    super.key,
    required this.onApplyFilter,
    required this.currentFilters,
  });

  @override
  State<KittyFilterDialog> createState() => _KittyFilterDialogState();
}

class _KittyFilterDialogState extends State<KittyFilterDialog> {
  late TextEditingController startDateController;
  late TextEditingController endDateController;
  String selectedStatus = 'all';
  String selectedSort = 'name';

  final List<Map<String, String>> statusOptions = [
    {'value': 'all', 'label': 'All'},
    {'value': 'active', 'label': 'Active'},
    {'value': 'completed', 'label': 'Completed'},
    {'value': 'draft', 'label': 'Draft (Canceled)'},
  ];

  final List<Map<String, String>> sortOptions = [
    {'value': 'name', 'label': 'Name'},
    {'value': 'created_at', 'label': 'Date Created'},
    {'value': 'end_date', 'label': 'End Date'},
    {'value': 'balance', 'label': 'Balance'},
  ];

  @override
  void initState() {
    super.initState();
    startDateController = TextEditingController(text: widget.currentFilters['start-date'] ?? '');
    endDateController = TextEditingController(text: widget.currentFilters['end-date'] ?? '');
    selectedStatus = widget.currentFilters['status'] ?? 'all';
    selectedSort = widget.currentFilters['sort'] ?? 'name';
  }

  @override
  void dispose() {
    startDateController.dispose();
    endDateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      controller.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  void _clearFilters() {
    setState(() {
      startDateController.clear();
      endDateController.clear();
      selectedStatus = 'all';
      selectedSort = 'name';
    });
  }

  void _applyFilters() {
    final filters = <String, String>{};
    
    if (startDateController.text.isNotEmpty) {
      filters['start-date'] = startDateController.text;
    }
    if (endDateController.text.isNotEmpty) {
      filters['end-date'] = endDateController.text;
    }
    if (selectedStatus != 'all') {
      filters['status'] = selectedStatus;
    }
    if (selectedSort != 'name') {
      filters['sort'] = selectedSort;
    }

    widget.onApplyFilter(filters);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Kitties',
                  style: CustomTextStyles.titleMediumBlack900,
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            
            // Status Filter
            Text('Status', style: CustomTextStyles.titleSmallGray900),
            SizedBox(height: 8.h),
            DropdownButtonFormField<String>(
              value: selectedStatus,
              decoration: InputDecoration(
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              ),
              items: statusOptions.map((option) {
                return DropdownMenuItem<String>(
                  value: option['value'],
                  child: Text(option['label']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedStatus = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Sort Filter
            Text('Sort By', style: CustomTextStyles.titleSmallGray900),
            SizedBox(height: 8.h),
            DropdownButtonFormField<String>(
              value: selectedSort,
              decoration: InputDecoration(
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              ),
              items: sortOptions.map((option) {
                return DropdownMenuItem<String>(
                  value: option['value'],
                  child: Text(option['label']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedSort = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Date Range Filters
            Text('Date Range', style: CustomTextStyles.titleSmallGray900),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: MyTextFieldwValidator(
                    controller: startDateController,
                    hint: 'Start Date',
                    readOnly: true,
                    onTap: () => _selectDate(startDateController),
                    iconSuffix: const Icon(Icons.calendar_today),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: MyTextFieldwValidator(
                    controller: endDateController,
                    hint: 'End Date',
                    readOnly: true,
                    onTap: () => _selectDate(endDateController),
                    iconSuffix: const Icon(Icons.calendar_today),
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearFilters,
                    child: const Text('Clear'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: MyButton(
                    onClick: _applyFilters,
                    label: 'Apply',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
