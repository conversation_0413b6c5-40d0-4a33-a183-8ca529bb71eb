// To parse this JSON data, do
//
//     final meetingRequest = meetingRequestFromJson(jsonString);

import 'dart:convert';

MeetingRequest meetingRequestFromJson(String str) => MeetingRequest.fromJson(json.decode(str));

String meetingRequestToJson(MeetingRequest data) => json.encode(data.toJson());

class MeetingRequest {
    String? title;
    String? description;
    String? email;
    String? locationTip;
    String? venue;
    int? memberId;
    int? chamaId;
    double? latitude;
    double? longitude;
    String? frequency;
    String? eventType;
    DateTime? startDate;
    DateTime? endDate;

    MeetingRequest({
        this.title,
        this.description,
        this.email,
        this.locationTip,
        this.venue,
        this.memberId,
        this.chamaId,
        this.latitude,
        this.longitude,
        this.frequency,
        this.eventType,
        this.startDate,
        this.endDate,
    });

    factory MeetingRequest.fromJson(Map<String, dynamic> json) => MeetingRequest(
        title: json["title"],
        description: json["description"],
        email: json["email"],
        locationTip: json["location_tip"],
        venue: json["venue"],
        memberId: json["member_id"],
        chamaId: json["chama_id"],
        latitude: json["latitude"].toDouble(),
        longitude: json["longitude"].toDouble(),
        frequency: json["frequency"],
        eventType: json["event_type"],
        startDate: DateTime.parse(json["start_date"]),
        endDate: DateTime.parse(json["end_date"]),
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "email": email,
        "location_tip": locationTip,
        "venue": venue,
        "member_id": memberId,
        "chama_id": chamaId,
        "latitude": latitude,
        "longitude": longitude,
        "frequency": frequency,
        "event_type": eventType,
        "start_date": startDate!.toIso8601String(),
        "end_date": endDate!.toIso8601String(),
    };
}



UpdateMeeting updateMeetingFromJson(String str) => UpdateMeeting.fromJson(json.decode(str));

String updateMeetingToJson(UpdateMeeting data) => json.encode(data.toJson());

class UpdateMeeting {
    int? id;
    String? title;
    String? description;
    String? email;
    String? locationTip;
    String? venue;
    int? memberId;
    int? chamaId;
    double? latitude;
    double? longitude;
    String? frequency;
    String? eventType;
    DateTime? startDate;
    DateTime? endDate;

    UpdateMeeting({
        this.id,
        this.title,
        this.description,
        this.email,
        this.locationTip,
        this.venue,
        this.memberId,
        this.chamaId,
        this.latitude,
        this.longitude,
        this.frequency,
        this.eventType,
        this.startDate,
        this.endDate,
    });

    factory UpdateMeeting.fromJson(Map<String, dynamic> json) => UpdateMeeting(
        id: json["ID"],
        title: json["title"],
        description: json["description"],
        email: json["email"],
        locationTip: json["location_tip"],
        venue: json["venue"],
        memberId: json["member_id"],
        chamaId: json["chama_id"],
        latitude: json["latitude"].toDouble(),
        longitude: json["longitude"].toDouble(),
        frequency: json["frequency"],
        eventType: json["event_type"],
        startDate: DateTime.parse(json["start_date"]),
        endDate: DateTime.parse(json["end_date"]),
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "title": title,
        "description": description,
        "email": email,
        "location_tip": locationTip,
        "venue": venue,
        "member_id": memberId,
        "chama_id": chamaId,
        "latitude": latitude,
        "longitude": longitude,
        "frequency": frequency,
        "event_type": eventType,
        "start_date": startDate!.toIso8601String(),
        "end_date": endDate!.toIso8601String(),
    };
}
