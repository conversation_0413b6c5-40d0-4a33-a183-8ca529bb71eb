import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/chama/all_chama_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/contr_kitties_screen.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>';
import 'package:onekitty/screens/dashboard/pages/events/events_page.dart';

import '../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class BottomNavSection extends StatelessWidget {
  BottomNavSection({super.key});

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Obx(() {
          final currentIndex =
              Get.find<BottomNavController>().currentIndex.value;
          return IndexedStack(
            index: currentIndex,
            children: const [
              HomeScreen(),
              MyKittiesScreen(), 
              AllChamaScreen(),
              EventsPage(),
            ],
          );
        }),
        bottomNavigationBar: _buildBottomBar(context),
      ),
    );
  }

  /// Section Widget
  Widget _buildBottomBar(BuildContext context) {
    return CustomBottomBar(
      onChanged: (type) {
        int index = _getIndexFromEnum(type);
        Get.find<BottomNavController>().changeIndex(index);
      },
    );
  }

  int _getIndexFromEnum(BottomBarEnum type) {
    switch (type) {
      case BottomBarEnum.Home:
        return 0;
      case BottomBarEnum.Contribution:
        return 1;
      case BottomBarEnum.Chama:
        return 2;
      case BottomBarEnum.Events:
        return 3;
      // ignore: unreachable_switch_default
      default:
        return 0; // Default to Home if not recognized
    }
  }
}

class BottomNavController extends GetxController {
  var currentIndex = 0.obs;

  void changeIndex(int index) {
    currentIndex.value = index;
  }
}
