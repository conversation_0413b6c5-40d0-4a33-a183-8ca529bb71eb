// notifications_view.dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/notification_card.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/notifications_skeleton.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';

import 'controller/notification_controller.dart';
import 'model/notification_model.dart';

class NotificationsView extends StatefulWidget {
  const NotificationsView({super.key});

  @override
  State<NotificationsView> createState() => _NotificationsViewState();
}

class _NotificationsViewState extends State<NotificationsView> {
  final NotificationController controller = Get.put(NotificationController());
  final uid = ''.obs;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    token();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        controller.loadStoredNotifications(refresh: true);
        controller.getNotificationTypes();
      }
    });

    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        controller.loadStoredNotifications();
      }
    });

  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  token() async {
    uid.value = await FirebaseAuth.instance.currentUser?.getIdToken() ?? '';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return 'Today';
    } else if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day - 1) {
      return 'Yesterday';
    }
    return '${date.day}/${date.month}/${date.year}';
  }

  Map<String, List<AppNotification>> _groupNotificationsByDate(
      List<AppNotification> notifications) {
    final groupedNotifications = <String, List<AppNotification>>{};
    for (var notification in notifications) {
      final date = _formatDate(notification.created);
      if (!groupedNotifications.containsKey(date)) {
        groupedNotifications[date] = [];
      }
      groupedNotifications[date]!.add(notification);
    }
    return groupedNotifications;
  }

  void _showDateRangePicker() {
    DatePicker.showDatePicker(
      context,
      onChanged: (date) {},
      onConfirm: (date) {
        if (controller.startDate.value == null) {
          controller.setDateRange(date, null);
        } else {
          if (date.isBefore(controller.startDate.value!)) {
            controller.setDateRange(date, controller.startDate.value);
          } else {
            controller.setDateRange(controller.startDate.value, date);
          }
          controller.applyFilters();
        }
      },
      minTime: DateTime(2020),
      maxTime: DateTime.now(),
      currentTime: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 90.w,
        leading: TextButton.icon(          
          onPressed: ()=>Navigator.pop(context), label: const Text('Back', style: TextStyle(color: Colors.white,),), icon: const Icon(Icons.navigate_before, color: Colors.white,)),
        title: const Text('Notifications',
            style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          IconButton(
            icon: Obx(() => Icon(
              controller.isFilterVisible.value 
                  ? Icons.filter_list_off 
                  : Icons.filter_list
            )),
            onPressed: controller.toggleFilterVisibility,
            tooltip: 'Toggle filters',
          ),
          IconButton(
            icon: const Icon(Icons.checklist),
            onPressed: controller.markAllAsRead,
            tooltip: 'Mark all as read',
          ),
        ],
      ),
      body: Column(
        children: [
          // Add loading progress indicator
          Obx(() {
            if (controller.isApplyingFilters.value) {
              return LinearProgressIndicator(
                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              );
            }
            return const SizedBox.shrink();
          }),
          // Filter section
        Obx(() {
  if (controller.isFilterVisible.value) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Type filters with improved spacing
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  label: 'All',
                  isSelected: controller.selectedFilter.value == 'all',
                  onTap: () => controller.setSelectedFilter('all'),
                ),
                SizedBox(width: 12.w),
                ...controller.notificationTypes.map((type) => 
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: _buildFilterChip(
                      label: type.replaceAll('_', ' ').toUpperCase(),
                      isSelected: controller.selectedFilter.value == type,
                      onTap: () => controller.setSelectedFilter(type),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Date and status filters with better visual grouping
          Row(
            children: [
              Expanded(
                child: _buildDateRangeChip(
                  label: controller.startDate.value == null
                      ? 'Select Date Range'
                      : controller.endDate.value == null
                          ? 'Select End Date'
                          : '${DateFormat('MMM d').format(controller.startDate.value!)} - ${DateFormat('MMM d').format(controller.endDate.value!)}',
                  onTap: _showDateRangePicker,
                ),
              ),
              SizedBox(width: 12.w),
              _buildStatusChip(
                label: 'Read',
                isSelected: controller.readFilter.value == 'read',
                onTap: () => controller.setReadFilter('read'),
              ),
              SizedBox(width: 8.w),
              _buildStatusChip(
                label: 'Unread',
                isSelected: controller.readFilter.value == 'unread',
                onTap: () => controller.setReadFilter('unread'),
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // Clear filters button with subtle animation
          AnimatedOpacity(
            opacity: controller.hasActiveFilters ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Center(
              child: TextButton.icon(
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear All Filters'),
              ),
            ),
          ),
        ],
      ),
    );
  }
  return const SizedBox.shrink();
}),

// Active filters display with improved chip styling
Obx(() {
  if (controller.hasActiveFilters) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: [
          if (controller.selectedFilter.value != 'all')
            _buildActiveFilterChip(
              label: controller.selectedFilter.value.replaceAll('_', ' ').toUpperCase(),
              onRemoved: () => controller.setSelectedFilter('all'),
            ),
          if (controller.startDate.value != null)
            _buildActiveFilterChip(
              label: 'From: ${DateFormat('MMM d').format(controller.startDate.value!)}',
              onRemoved: () => controller.setDateRange(null, controller.endDate.value),
            ),
          if (controller.endDate.value != null)
            _buildActiveFilterChip(
              label: 'To: ${DateFormat('MMM d').format(controller.endDate.value!)}',
              onRemoved: () => controller.setDateRange(controller.startDate.value, null),
            ),
          if (controller.readFilter.value != null)
            _buildActiveFilterChip(
              label: controller.readFilter.value!.toUpperCase(),
              onRemoved: () => controller.setReadFilter(null),
            ),
          ActionChip(
            elevation: 2,
            label: const Text('Clear All'),
            onPressed: controller.clearFilters,
            backgroundColor: Theme.of(context).colorScheme.error.withOpacity(0.1),
          ),
        ],
      ),
    );
  }
  return const SizedBox.shrink();
}),

  Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const NotificationSkeletonList();
              }

              if (controller.notifications.isEmpty) {
                return const EmptyNotificationsView();
              }

              final groupedNotifications =
                  _groupNotificationsByDate(controller.notifications);

              return RefreshIndicator(
                onRefresh: () => controller.loadStoredNotifications(refresh: true),
                child: ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  itemCount: groupedNotifications.length + (controller.isLoadingMore.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index >= groupedNotifications.length) {
                      return Padding(
                        padding: EdgeInsets.all(16.w),
                        child: Center(
                          child: SizedBox(
                            width: 32.w,
                            height: 32.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      );
                    }

                    final date = groupedNotifications.keys.elementAt(index);
                    final notifications = groupedNotifications[date]!;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          child: Text(
                            date,
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: notifications.length,
                          separatorBuilder: (context, index) => SizedBox(height: 8.h),
                          itemBuilder: (context, index) {
                            final notification = notifications[index];
                            return NotificationCard(
                              notification: notification,
                            );
                          },
                        ),
                        if (index < groupedNotifications.length - 1)
                          Divider(height: 24.h),
                      ],
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
// Helper methods for consistent styling
Widget _buildFilterChip({
  required String label,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return FilterChip(
    label: Text(label),
    selected: isSelected,
    onSelected: (_) {
      onTap();
      controller.applyFilters(); // Add this line to fetch notifications
    },
    labelStyle: TextStyle(
      fontWeight: FontWeight.w500,
      color: isSelected 
          ? Theme.of(context).colorScheme.onPrimary
          : Theme.of(context).colorScheme.onSurface,
    ),
    backgroundColor: isSelected 
        ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
        : Theme.of(context).colorScheme.surfaceVariant,
    selectedColor: Theme.of(context).colorScheme.primary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  );
}

Widget _buildDateRangeChip({
  required String label,
  required VoidCallback onTap,
}) {
  return InputChip(
    label: Text(label),
    onPressed: () {
      onTap();
      // Date range picker already calls applyFilters() after selection
    },
    avatar: Icon(Icons.date_range,
        color: Theme.of(context).colorScheme.primary),
    labelStyle: TextStyle(
      color: Theme.of(context).colorScheme.onSurface,
    ),
    backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  );
}

Widget _buildStatusChip({
  required String label,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return ChoiceChip(
    label: Text(label),
    selected: isSelected,
    onSelected: (_) {
      onTap();
      controller.applyFilters(); // Add this line to fetch notifications
    },
    labelStyle: TextStyle(
      color: isSelected 
          ? Theme.of(context).colorScheme.onSecondary
          : Theme.of(context).colorScheme.onSurface,
    ),
    backgroundColor: isSelected 
        ? Theme.of(context).colorScheme.secondary
        : Theme.of(context).colorScheme.surfaceVariant,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  );
}

Widget _buildActiveFilterChip({
  required String label,
  required VoidCallback onRemoved,
}) {
  return InputChip(
    label: Text(label),
    onDeleted: () {
      onRemoved();
      controller.applyFilters(); // Add this line to fetch notifications
    },
    deleteIcon: Icon(Icons.close,
        color: Theme.of(context).colorScheme.onSurfaceVariant),
    backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
    labelStyle: TextStyle(
      color: Theme.of(context).colorScheme.onSurface,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  );
}
}
