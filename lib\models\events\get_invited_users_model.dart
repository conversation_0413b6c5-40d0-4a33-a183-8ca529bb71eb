// To parse this JSON data, do
//
//     final getInvitedUsersModel = getInvitedUsersModelFromJson(jsonString);

import 'dart:convert';

import 'tickets_model.dart';

List<GetInvitedUsersModel> getInvitedUsersModelFromJson(String str) =>
    List<GetInvitedUsersModel>.from(
        json.decode(str).map((x) => GetInvitedUsersModel.fromJson(x)));

String getInvitedUsersModelToJson(List<GetInvitedUsersModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class GetInvitedUsersModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int eventId;
  final int ticketId;
  final Ticket? ticket;
  final String inviteCode;
  final String reservationStatus;
  final String phoneNumber;
  final String email;
  final String firstName;
  final String secondName;

  GetInvitedUsersModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.eventId = 0,
    this.ticketId = 0,
    this.ticket,
    this.inviteCode = '',
    this.reservationStatus = '',
    this.phoneNumber = '',
    this.email = '',
    this.firstName = '',
    this.secondName = '',
  });

  factory GetInvitedUsersModel.fromJson(Map<String, dynamic> json) =>
      GetInvitedUsersModel(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        eventId: json["event_id"] ?? 0,
        ticketId: json["ticket_id"] ?? 0,
        ticket: json["ticket"] != null ? Ticket.fromJson(json["ticket"]) : null,
        inviteCode: json["invite_code"] ?? '',
        reservationStatus: json["reservation_status"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        email: json["email"] ?? '',
        firstName: json["first_name"] ?? "" ?? '',
        secondName: json["second_name"] ?? "" ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "event_id": eventId,
        "ticket_id": ticketId,
        "ticket": ticket?.toJson(),
        "invite_code": inviteCode,
        "reservation_status": reservationStatus,
        "phone_number": phoneNumber,
        "email": email,
        "first_name": firstName,
        "second_name": secondName,
      };
}
