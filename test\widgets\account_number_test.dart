import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';

void main() {
  group('Account Number Input Tests', () {
    testWidgets('CustomTextField allows alphanumeric input when allowAlphanumeric is true', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomTextField(
              controller: controller,
              labelText: 'Account Number',
              allowAlphanumeric: true,
              showNoKeyboard: true,
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Test alphanumeric input
      await tester.enterText(textField, 'ABC123XYZ');
      expect(controller.text, 'ABC123XYZ');

      // Test numeric input still works
      await tester.enterText(textField, '*********');
      expect(controller.text, '*********');

      // Test mixed alphanumeric
      await tester.enterText(textField, 'PAY123BILL');
      expect(controller.text, 'PAY123BILL');
    });

    testWidgets('CustomTextField restricts to numeric when allowAlphanumeric is false', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomTextField(
              controller: controller,
              labelText: 'Numeric Field',
              allowAlphanumeric: false,
              showNoKeyboard: true,
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Test that alphabetic characters are filtered out
      await tester.enterText(textField, 'ABC123XYZ');
      expect(controller.text, '123');

      // Test numeric input works
      await tester.enterText(textField, '*********');
      expect(controller.text, '*********');
    });

    testWidgets('Account number validation accepts alphanumeric values', (WidgetTester tester) async {
      final controller = TextEditingController();
      final formKey = GlobalKey<FormState>();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: CustomTextField(
                controller: controller,
                labelText: 'Account Number',
                allowAlphanumeric: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Account Number is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        ),
      );

      // Test with alphanumeric account number
      await tester.enterText(find.byType(TextFormField), 'PAY123BILL');
      
      // Validate the form
      expect(formKey.currentState!.validate(), true);
      expect(controller.text, 'PAY123BILL');
    });
  });
}
