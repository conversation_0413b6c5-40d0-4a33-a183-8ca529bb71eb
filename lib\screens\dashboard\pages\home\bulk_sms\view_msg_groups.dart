import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/models/bulk_sms/sms_groups.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contr_kitty_url_screen.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/create_msg_group.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/view_single_group.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
// import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ViewMsgGroups extends StatefulWidget {
  const ViewMsgGroups({super.key});

  @override
  State<ViewMsgGroups> createState() => _ViewMsgGroupsState();
}

class _ViewMsgGroupsState extends State<ViewMsgGroups> {  
  String greeting = getGreeting();
  final dateformat = DateFormat('EE, dd MMMM');

  final controller = Get.put(CreateMsgController());
  @override
  void initState() {
    
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    controller.fetchMessageGroups();
    return RefreshIndicator(
      onRefresh: () async => await controller.fetchMessageGroups(),
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              // expandedHeight: 300.h,
              title: const Text('SMS Groups'),
              pinned: true,
              bottom: PreferredSize(
                  preferredSize: Size.fromHeight(60.h),
                  child: ColoredBox(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    child: Row(
                      children: [
                        const Spacer(),
                        Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: MyButton(
                              onClick: () => {
                                controller.groupMembers.clear(),
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const CreateMessageGroup(view: false,isEditing: true,)))
                              },
                              width: 200.w,
                              label: 'Create Group',
                            )),
                      ],
                    ),
                  )),
            ),
            SliverToBoxAdapter(
                child: Obx(
              () => controller.isloading.value
                  ? _buildLoadingIndicator()
                  : controller.groups.isEmpty ? 
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(height: 120.h),
                    CustomImageView(
                  imagePath: AssetUrl.imgGroup7,
                  height: 174.h,
                  width: 325.w,
                ),
                SizedBox(height: 5.h),
                TextButton(
                  onPressed: ()=>{
                                controller.groupMembers.clear(),
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const CreateMessageGroup(view: false,isEditing: true,)))
                              },
                  child: Text(
                    "Create Group Now",
                    style: theme.textTheme.titleLarge,
                  ),
                ),
                
                  ],)
                   : Obx(
                      () => ListView.builder(
                          itemCount: controller.groups.length,
                          shrinkWrap: true,
                          primary: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            final group = controller.groups[index];
                            return OpenContainer(
                                closedColor:
                                    Theme.of(context).scaffoldBackgroundColor,
                                openBuilder: (context, call) =>
                                    CreateMessageGroup(smsGroup: group, view: true),
                                closedBuilder: (context, call) {
                                  return InkWell(
                                      onTap: () {
                                        controller.groupMembers.value =
                                            group.smsGroupMembers ?? [];
                                        call;
                                      },
                                      child: MsgGroupsItem(
                                          group: group, call: call));
                                });
                          }),
                    ),
            ))
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Obx(() {
      if (controller.isloading.value) {
        return Skeletonizer(
            enabled: true,
            child: ListView(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                children: List.generate(
                    5,
                    (index) => MsgGroupsItem(
                          group: SmsGroups(),
                          call: () {},
                        ))));
      }
      return const SizedBox.shrink();
    });
  }
}

class MsgGroupsItem extends StatelessWidget {
  final SmsGroups group;
  final Function() call;
  const MsgGroupsItem({super.key, required this.group, required this.call});

  @override
  Widget build(BuildContext context) {
    final CreateMsgController controller = Get.find();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        onTap: () {
          controller.groupMembers.value = group.smsGroupMembers ?? [];
          call();
        },
        onLongPress: () {
          showMenu(
            context: context,
            position: const RelativeRect.fromLTRB(100, 100, 0, 0),
            items: [
              PopupMenuItem(
                child: const Text('Edit Group'),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateMessageGroup(smsGroup: group),
                  ),
                ),
              ),
              PopupMenuItem(
                child: const Text('Edit Members'),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SelectContacts(smsGroups: group),
                  ),
                ),
              ),
              PopupMenuItem(
                child: const Text('Delete Group'),
                onTap: () => showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Delete Group'),
                    content: const Text(
                        'Are you sure you want to delete this group?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () {
                          controller.deleteGroup(group.id ?? 0, group.toJson());
                          Navigator.pop(context);
                        },
                        child: const Text('Delete',
                            style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          group.name ?? '',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'created at: ${DateFormat('dd-MM-yyyy HH:mm').format((group.createdAt ?? DateTime(2000)))}',
            style: const TextStyle(fontSize: 12),
          ),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.group_outlined, color: Colors.blue[700]),
            const SizedBox(height: 4),
            Text(
              '${group.smsGroupMembers?.length ?? 0} members',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }
}
