import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/bulkSms/msg_model.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/view_msg_groups.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/sms_card.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/main.dart' show isLight;
import '../../../../../utils/utils_exports.dart';

class AllSmsScreen extends StatefulWidget {
  const AllSmsScreen({
    super.key,
  });

  @override
  State<AllSmsScreen> createState() => _AllSmsScreenState();
}

@override
class _AllSmsScreenState extends State<AllSmsScreen> {
  TextEditingController searchController = TextEditingController();

  TextEditingController messageController = TextEditingController();

  final UserKittyController _userController = Get.put(UserKittyController());
  final BulkSMSController _bulkSmsController = Get.put(BulkSMSController());

  String greeting = getGreeting();
  final dateformat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    _loadMessages();

    super.initState();
  }

  Future<void> _loadMessages() async {
    try {
      if (!mounted) return;
      _bulkSmsController.isgetloading(true);
      await _bulkSmsController.getMessages(
        phoneNumber: _userController.getLocalUser()?.phoneNumber,
      );
    } catch (e) {
      print("Error loading messages: $e");
      // Handle error appropriately
    } finally {
      _bulkSmsController.isgetloading(false);
    }
  }

  @override
  void dispose() {
    _bulkSmsController.reset();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        resizeToAvoidBottomInset: false,
        body: GetBuilder(
          builder: (BulkSMSController smsController) {
            return Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const RowAppBar(),
                    Container(
                      margin: EdgeInsets.only(top: 20.h, left: 8, right: 8),
                      padding: EdgeInsets.symmetric(vertical: 15.h),
                      decoration: BoxDecoration(
                        color: isLight.value
                            ? Colors.white
                            : const Color(0xFF1F1F1F),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: isLight.value
                                ? Colors.grey.withOpacity(0.1)
                                : Colors.black.withOpacity(0.2),
                            spreadRadius: 2,
                            blurRadius: 5,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 24.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  children: [
                                    Text(
                                      "Total SMS Sent",
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    SizedBox(height: 1.h),
                                    Obx(() {
                                        if (_bulkSmsController
                                            .isgetloading.isTrue) {
                                          return const Text(
                                            "Loading...",
                                            style: TextStyle(
                                                fontSize: 15,
                                                fontStyle: FontStyle.italic),
                                          );
                                        }
                                        return Text(
                                          "${_bulkSmsController.results.value!.total}",
                                          style: const TextStyle(
                                              color: Color.fromARGB(
                                                  255, 5, 78, 138),
                                              fontSize: 25),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                Column(
                                  children: [
                                    Text(
                                      "My Wallet",
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    Text(
                                      FormattedCurrency.getFormattedCurrency(
                                        _userController
                                                .getLocalUser()
                                                ?.balance
                                                ?.toStringAsFixed(2) ??
                                            "",
                                      ),
                                      style: const TextStyle(
                                          color:
                                              Color.fromARGB(255, 5, 78, 138),
                                          fontSize: 25),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 15.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              CustomElevatedButton(
                                buttonStyle: CustomButtonStyles.fillPrimary,
                                width: 140.w,
                                height: 35.h,
                                text: "TopUp",
                                buttonTextStyle: TextStyle(
                                    fontSize: 12.h,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white),
                                onPressed: () {
                                  Get.toNamed(NavRoutes.topup);
                                },
                                alignment: Alignment.center,
                              ),
                              CustomElevatedButton(
                                  buttonStyle: CustomButtonStyles.fillPrimary,
                                  width: 140.w,
                                  height: 35.h,
                                  buttonTextStyle: TextStyle(
                                      fontSize: 12.h,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white),
                                  alignment: Alignment.center,
                                  onPressed: () =>
                                      Get.to(() => const ViewMsgGroups()),
                                  text: 'SMS Group'),
                            ],
                          ),
                          // Align(
                          //   alignment: Alignment.centerRight,
                          //   child:
                          // ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Send Bulk SMS",
                      style: theme.textTheme.titleLarge,
                    ),
                    SizedBox(height: 13.h),
                    Text(
                      "Conveniently send messages to people at once",
                      style: CustomTextStyles.bodyMediumBluegray700,
                    ),
                    SizedBox(height: 22.h),
                    Obx(
                      () => Align(
                        alignment: Alignment.centerLeft,
                        child: _bulkSmsController.messages.isEmpty
                            ? const SizedBox()
                            : Text(
                                "Messages sent",
                                style: CustomTextStyles.titleMediumBlack900,
                              ),
                      ),
                    ),
                    SizedBox(height: 10.h),
                    _buildMessages(context),
                    
                   
                    SizedBox(height: 10.h),
                    
                    const SizedBox(height: 100)
                  ],
                ),
              ),
            );
          },
        ),
        floatingActionButton: CustomElevatedButton(
          buttonStyle: CustomButtonStyles.fillPrimary,
          width: 140.w,
          height: 45.h,
          text: "Send Message",
          buttonTextStyle:
              TextStyle(fontSize: 12.h, fontWeight: FontWeight.bold),
          leftIcon: Container(
            margin: EdgeInsets.only(right: 5.w),
            color: Colors.white,
            child: CustomImageView(
                imagePath: AssetUrl.message, height: 12.h, width: 12.w),
          ),
          onPressed: () {
            Get.toNamed(NavRoutes.crtsmsScreen);
          },
          alignment: Alignment.bottomRight,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  /// Section Widget
  Widget _buildMessages(BuildContext context) {
    return GetX<BulkSMSController>(
      builder: (smsController) {
        if (smsController.isgetloading.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .33,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        }
        if (smsController.messages.isEmpty) {
          return Container(
            height: 300.h,
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 32.w,
              vertical: 5.h,
            ),
            child: Column(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgGroup7,
                  height: 174.h,
                  width: 325.w,
                ),
                SizedBox(height: 10.h),
                 Center(
                                    child: GestureDetector(
                                      onTap: () {
                                      Get.toNamed(NavRoutes.crtsmsScreen);
                                      },
                                      child: Text.rich(
                                      TextSpan(
                                        children: [
                                        TextSpan(
                                          text: 'You Do not Have any Messages Yet, ',
                                          style: CustomTextStyles.titleMediumBlack900,
                                        ),
                                        TextSpan(
                                          text: 'Try it Now',
                                          style: CustomTextStyles.titleMediumBlack900.copyWith(
                                          color: theme.primaryColor,
                                          ),
                                        ),
                                        ],
                                      ),
                                      ),
                                    ),
                                    ),
                                  
              ],
            ),
          );
        }

        return Column(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 18.h,
                ),
                decoration: AppDecoration.outlineGray.copyWith(
                  borderRadius: BorderRadiusStyle.circleBorder22,
                ),
                child: GroupedListView<msgItem, DateTime>(
                  controller: smsController.chcontroller,
                  sort: false,
                  shrinkWrap: true,
                  elements: smsController.messages,
                  groupBy: (msgItem element) {
                    DateTime date = element.createdAt!;
                    return DateTime(date.year, date.month, date.day);
                  },
                  groupHeaderBuilder: (msgItem value) {
                    final date = dateformat.format(value.createdAt!);
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        date,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18.0,
                        ),
                      ),
                    );
                  },
                  itemBuilder: (_, msgItem item) {
                    return SmsCardWidget(item: item);
                  },
                  separator: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.0),
                  ),
                ),
              ),
            ),
            if (smsController.loadingMore.isTrue)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Column(
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    const Text(
                      "loading..",
                      style: TextStyle(
                        color: Colors.white,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}
