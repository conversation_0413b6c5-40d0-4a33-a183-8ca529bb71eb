import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart'; 
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/datetime/combined_datetime.dart';
import 'package:onekitty/utils/size_config.dart';

class EndDate extends StatefulWidget {
  const EndDate({super.key});

  @override
  State<EndDate> createState() => _EndDateState();
}

class _EndDateState extends State<EndDate> {
  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  KittyController kittyController = Get.put(KittyController());
  DataController datacontroller = Get.put(DataController());
  ContributeController contributeController = Get.put(ContributeController());
  final _formKey = GlobalKey<FormState>();
  DateTime? endDate;
  int? kittyId;
  bool isLoading = false;

  setValues() async {
    DateTime endDateGot =
        datacontroller.kitty.value.kitty?.endDate ?? DateTime.now();
    endDate = endDateGot;
    kittyId = datacontroller.kitty.value.kitty?.iD ?? 0;
  }

  @override
  void initState() {
    setValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Text(
                "Edit Kitty End Date",
                style: context.headlineMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(
                height: 30,
              ),
              SingleLineRow(
                text: "Expected contribution end date",
                popup: KtStrings.endDateInfo,
              ),
              DatePicker(
                date: dateController,
                time: timeController,
                isAllow: true,
              ),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: SizeConfig.screenWidth,
                height: 50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Text(
                          "Back",
                          style: context.dividerTextLarge
                              ?.copyWith(color: AppColors.primary),
                        ),
                      ),
                    ),
                    Obx(() => CustomKtButton(
                          width: 100.w,
                          btnText: "Update",
                          isLoading:
                              kittyController.isEditEndDateloading.isTrue,
                          onPress: () async {
                            if (_formKey.currentState!.validate()) {
                              final date = dateController.text;
                              final time = timeController.text;
                              if (dateController.text.trim().isEmpty ||
                                  timeController.text.trim().isEmpty) {
                                ToastUtils.showErrorToast(context,
                                    "Kindly pick an end date", "Error");
                              }

                              // Combine the date and time into a single DateTime object
                              final combinedDateTime =
                                  combineDateTime(date, time);

                              // Format the combined DateTime object according to the backend's expected format
                              final formattedDateTime =
                                  formatDateTime(combinedDateTime);
                              final dateTime =
                                  DateTime.tryParse(formattedDateTime);

                              // int newDateTime = dateTime!
                              //     .difference(DateTime.now())
                              //     .inMinutes;
                              // if (newDateTime < 15) {
                              //   endDate = DateTime.now()
                              //       .add(const Duration(minutes: 15));
                              // } else {
                              //   endDate = dateTime;
                              // }
                              if (dateTime == null) {
                                ToastUtils.showErrorToast(context, "Error",
                                    "Please select an end date at least 15 minutes away",
                                    autoDismiss: false);
                                return;
                              } else {
                                var res = await kittyController.updateEndDate(
                                    newDate: dateTime.toUtc(),
                                    KittyId: kittyId!);
                                if (res) {
                                  var resp = await contributeController
                                      .getKitty(id: kittyId);
                                  if (resp) {
                                    var dataController =
                                        Get.put(DataController());
                                    dataController.kitty.value =
                                        contributeController.singleKitty.value;
                                  }
                                  ToastUtils.showSuccessToast(
                                      context,
                                      kittyController.apiMessage.string,
                                      "Success");
                                      Navigator.pop(context);
                                } else {
                                  ToastUtils.showErrorToast(
                                      context,
                                      kittyController.apiMessage.string,
                                      "Error");
                                }
                              }
                            } else {
                              ToastUtils.showErrorToast(
                                  context, "Fill in all values", "Error");
                            }
                          },
                        ))
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
