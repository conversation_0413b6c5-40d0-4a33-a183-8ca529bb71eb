// To parse this JSON data, do
//
//     final signatoryApprovalModel = signatoryApprovalModelFromJson(jsonString);

import 'dart:convert';

SignatoryApprovalModel signatoryApprovalModelFromJson(String str) => SignatoryApprovalModel.fromJson(json.decode(str));

String signatoryApprovalModelToJson(SignatoryApprovalModel data) => json.encode(data.toJson());

class SignatoryApprovalModel {
    int? chamaId;
    int? memberId;
    bool? isApproved;
    String? comment;
    int? transactionId;
    String? latitude;
    String? longitude;
    String? deviceId;
    String? deviceModel;

    SignatoryApprovalModel({
        this.chamaId,
        this.memberId,
        this.isApproved,
        this.comment,
        this.transactionId,
        this.latitude,
        this.longitude,
        this.deviceId,
        this.deviceModel,
    });

    factory SignatoryApprovalModel.from<PERSON>son(Map<String, dynamic> json) => SignatoryApprovalModel(
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        isApproved: json["is_approved"],
        comment: json["comment"],
        transactionId: json["transaction_id"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        deviceId: json["device_id"],
        deviceModel: json["device_model"],
    );

    Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "member_id": memberId,
        "is_approved": isApproved,
        "comment": comment,
        "transaction_id": transactionId,
        "latitude": latitude,
        "longitude": longitude,
        "device_id": deviceId,
        "device_model": deviceModel,
    };
}
