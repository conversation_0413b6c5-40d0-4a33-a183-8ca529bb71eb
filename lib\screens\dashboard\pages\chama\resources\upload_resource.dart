import 'dart:io';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../../helpers/colors.dart';
import '../../../../../models/chama/chamaDto.dart';
import '../../../../../utils/utils_exports.dart';

class UploadResource extends StatefulWidget {
  const UploadResource({super.key});

  @override
  State<UploadResource> createState() => _UploadResourceState();
}

class _UploadResourceState extends State<UploadResource> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController titleController = TextEditingController();
  TextEditingController descrController = TextEditingController();
  String filepath = "";
  String fileName = "No file chosen";
  String filetype = "PDF";
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: Padding(
          padding: EdgeInsets.only(left: 15.0.w, right: 15.w),
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 12.h,
                ),
                const RowAppBar(),
                Text(
                  "Upload Documents",
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
                ),
                SizedBox(
                  height: 5.h,
                ),
                Container(
                  width: 350.w,
                  margin: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Text(
                    "Share any documents with your chama members",
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                _buildTextField(context),
                SizedBox(
                  height: 20.h,
                ),
                _buildButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Obx(
        () => CustomKtButton(
          isLoading: chamaController.isUploading.isTrue,
          onPress: () async {
            await onSubmit(context, filepath);
          },
          width: SizeConfig.screenWidth,
          height: 60,
          btnText: "Save",
        ),
      ),
    );
  }

  Widget _buildTextField(BuildContext context) {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Document title",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: titleController,
            hintText: "e.g Chama Guidelines",
            labelText: "Enter document title",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Filed cannot be empty";
              } else if (p0.length < 5) {
                return "Document title must be between 5 and 300";
              }
              return p0;
            },
          ),
          Text("Enter Document description",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: descrController,
            hintText: "e.g Our rules and Guidelines",
            labelText: "Description",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Filed cannot be empty";
              }
              return null;
            },
          ),
          Text(
            "Document",
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          Container(
            width: SizeConfig.screenWidth - 45,
            height: 40.h,
            decoration: BoxDecoration(
              color: Colors.blueAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color: AppColors.midBlue,
                width: 1.0,
              ),
            ),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child: TextButton(
                    onPressed: () async {
                      _pickFile(context);
                    },
                    child: Text(
                      "Choose File",
                      style: TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 10.h),
                    ),
                  ),
                ),
                SizedBox(
                  width: 5.w,
                ),
                Expanded(
                  child: Text(
                    fileName,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const Text(
            "File size limit is 20MB",
            style: TextStyle(
              fontStyle: FontStyle.italic,
              color: Color.fromARGB(255, 135, 131, 131),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickFile(BuildContext context) async {
    const int fileSizeLimit = 20 * 1024 * 1024;
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        int fileSize = await file.length();

        if (fileSize <= fileSizeLimit) {
          setState(() {
            filepath = result.files.single.path ?? "";
            fileName = result.files.single.name;
            filetype = path.extension(fileName).substring(1);
          });
        } else {
          // Handle file size exceeding the limit
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('File size exceeds the limit of 20 MB.'),
            ),
          );
        }
      } else {
        // User canceled the picker
      }
    } catch (e) {}
  }

  Future<void> onSubmit(BuildContext context, String filepath) async {
    if (filepath.isNotEmpty) {
      File file = File(filepath);

      String? mimeType = lookupMimeType(file.path);
      if (mimeType == null) {
        ToastUtils.showErrorToast(
            context, "Could not determine the file type", "Error");
        return;
      }

      String fileType = mimeType.split('/')[1].toUpperCase();
      String type;

      switch (fileType) {
        case 'PNG':
        case 'JPG':
        case 'JPEG':
        case 'GIF':
        case 'BMP':
        case 'WEBP':
          type = "IMAGE";
          break;
        case 'MP4':
        case 'AVI':
        case 'MOV':
        case 'WMV':
        case 'FLV':
          type = "VIDEO";
          break;
        case 'MP3':
        case 'WAV':
        case 'AAC':
        case 'FLAC':
        case 'OGG':
          type = "AUDIO";
          break;
        case 'PDF':
        case 'DOC':
        case 'DOCX':
        case 'XLS':
        case 'XLSX':
        case 'PPT':
        case 'PPTX':
          type = "PDF";
          break;
        default:
          ToastUtils.showErrorToast(context, "Unsupported file type", "Error");
          return;
      }

      try {
        String mediaURL = await chamaController.uploadMedia(file);

        final upload = UploadRDto(
            title: titleController.text,
            description: descrController.text,
            mediaUrl: mediaURL,
            chamaId: _dataController.singleChamaDts.value.id ?? 0,
            type: type);

        final res = await chamaController.uploadResource(uploadDto: upload);
        if (res) {
          ToastUtils.showSuccessToast(
              context, chamaController.apiMessage.string, "Success");
          chamaController.getResources(
            chamaId: _dataController.singleChamaDts.value.id ?? 0,
          );
          Get.back();
        } else {
          ToastUtils.showErrorToast(
              context, chamaController.apiMessage.string, "Error");
        }
      } catch (e) {
        ToastUtils.showErrorToast(context, "File upload failed: $e", "Error");
      }
    }
  }
}
