analyzer:
  errors:
    use_build_context_synchronously: ignore
    avoid_print: ignore
    camel_case_types: ignore
    unused_local_variable: ignore
    constant_identifier_names: ignore 
    file_names: ignore
    no_leading_underscores_for_local_identifiers: ignore
    non_constant_identifier_names: ignore
    deprecated_member_use: ignore
    avoid_types_as_parameter_names: ignore
    library_private_types_in_public_api: ignore
    use_rethrow_when_possible: ignore
    empty_catches: ignore
    no_wildcard_variable_uses: ignore
    unused_field: ignore
include: package:flutter_lints/flutter.yaml

linter: 
  rules:
    prefer_const_constructors: true
    # always_speficy_types: true
    # avoid_print: true  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule