// import 'dart:io';

// import 'package:device_info_plus/device_info_plus.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:onekitty/controllers/chama/chama_controller.dart';
// import 'package:onekitty/helpers/show_snack_bar.dart';
// import 'package:onekitty/models/chama/signatory_approval.dart';
// import 'package:onekitty/models/chama/signatory_transactions_model.dart';
// import 'package:onekitty/screens/widgets/text_form_field.dart';
// import 'package:onekitty/utils/utils_exports.dart';

// class SignatoryApproval extends StatefulWidget {
//   final Transacton transacton;
//   const SignatoryApproval({super.key, required this.transacton});

//   @override
//   State<SignatoryApproval> createState() => _SignatoryApprovalState();
// }

// class _SignatoryApprovalState extends State<SignatoryApproval> {
//   TextEditingController commentController = TextEditingController();
//   String deviceId = "";
//   String deviceModel = "";
//   final ChamaDataController chamaDataController =
//       Get.put(ChamaDataController());
//   final ChamaController chamaController = Get.put(ChamaController());
//   @override
//   void dispose() {
//     super.dispose();
//     commentController.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//     getDeviceInfo();
//   }

//   void getDeviceInfo() async {
//     DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
//     if (Platform.isAndroid) {
//       AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
//       setState(() {
//         deviceId = androidInfo.id;
//         deviceModel = androidInfo.model;
//       });
//       print('Running on ${androidInfo.id} ${androidInfo.model}');
//     } else if (Platform.isIOS) {
//       IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
//       print(
//           'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');
//       setState(() {
//         deviceId = iosInfo.identifierForVendor!;
//         deviceModel = iosInfo.model;
//       });
//     } else {
//       WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
//       print('Running on ${webBrowserInfo.userAgent}');
//       setState(() {
//         deviceId = webBrowserInfo.userAgent!;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: buildAppBar(context),
//       body: SafeArea(
//         child: Container(
//           margin: EdgeInsets.symmetric(horizontal: 12, vertical: 40),
//           child: Column(
//             children: [
//               CustomTextField(
//                 controller: commentController,
//                 labelText: "Comment",
//                 isRequired: true,
//               ),
//               Obx(() => CustomKtButton(
//                   isLoading: chamaController.isSignatoryApproveLoading.isTrue,
//                   onPress: () {
//                     approveTransaction(true);
//                   },
//                   btnText: "Approve"))
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   approveTransaction(bool isApprove) async {
//     SignatoryApprovalModel request = SignatoryApprovalModel(
//         chamaId: chamaDataController.chama.value.chama?.id,
//         memberId: 71,
//         isApproved: isApprove ? true : false,
//         comment: commentController.text.trim(),
//         transactionId: widget.transacton.id,
//         latitude: 12.3456.toString(),
//         longitude: 12.3456.toString(),
//         deviceId: deviceId,
//         deviceModel: deviceModel);
//     bool res = await chamaController.signatoryApproval(request: request);
//     if (res) {
//       if (!mounted) return;
//       Snack.show(res, chamaController.apiMessage.string);
//       Get.back();
//     } else {
//       if (!mounted) return;
//       Snack.show(res, chamaController.apiMessage.string);
//     }
//   }
// }
