import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../controllers/events/verify_ticket_controller.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class QrVerifyTicket extends StatefulWidget {
  final int eventId;
  const QrVerifyTicket({super.key, required this.eventId});

  @override
  State<QrVerifyTicket> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QrVerifyTicket> {
  final controller = Get.put(VerifyTicketController());
  final ticketCodeController = TextEditingController();
  MobileScannerController? cameraController;
  bool isScanning = true;
  bool hasCameraPermission = false;

  @override
  void initState() {
    super.initState();
    checkCameraPermission();
  }

  Future<void> checkCameraPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      hasCameraPermission = status.isGranted;
    });
    if (hasCameraPermission) {
      initializeCamera();
    }
  }

  void initializeCamera() {
    try {
      cameraController = MobileScannerController();
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  void onDetect(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    for (final barcode in barcodes) {
      controller.verifyTicket(widget.eventId, barcode.rawValue!);
      print('Barcode found! ${barcode.rawValue}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body:
          !hasCameraPermission ? _buildPermissionRequest() : _buildScannerUI(),
    );
  }

  Widget _buildPermissionRequest() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.camera_alt, size: 100, color: Colors.white),
          const SizedBox(height: 20),
          const Text(
            'Camera Permission Required',
            style: TextStyle(color: Colors.white, fontSize: 20),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () async {
              final status = await Permission.camera.request();
              setState(() {
                hasCameraPermission = status.isGranted;
              });
              if (hasCameraPermission) {
                initializeCamera();
              }
            },
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerUI() {
    return Stack(
      children: [
        if (cameraController != null)
          MobileScanner(
            controller: cameraController!,
            onDetect: onDetect,
          ),
        ColorFiltered(
          colorFilter: ColorFilter.mode(
            Colors.black.withOpacity(0.5),
            BlendMode.srcOut,
          ),
          child: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  backgroundBlendMode: BlendMode.dstOut,
                ),
              ),
              Center(
                child: Container(
                  height: 250,
                  width: 250,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
        SafeArea(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.flash_off, color: Colors.white),
                      onPressed: () => cameraController?.toggleTorch(),
                    ),
                    IconButton(
                      icon: const Icon(Icons.flip_camera_ios,
                          color: Colors.white),
                      onPressed: () => cameraController?.switchCamera(),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Container(
                width: 250,
                height: 250,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 20),
              TextButton(
                onPressed: () {
                  setState(() => isScanning = false);
                  Navigator.pop(context);
                },
                child: const Text(
                  'Cancel Scanning',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: const BoxDecoration(
                        color: Colors.amber,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.center_focus_strong),
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    cameraController?.dispose();
    super.dispose();
  }
}
