import 'dart:isolate';
import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

/// A utility class for detecting potential memory issues through both
/// UI responsiveness and direct memory monitoring
class MemoryChecker {
  static final MemoryChecker _instance = MemoryChecker._internal();
  factory MemoryChecker() => _instance;
  
  MemoryChecker._internal() {
    // Default values
    _warningThresholdMB = 100;
    _checkInterval = const Duration(seconds: 30);
  }

  // UI responsiveness monitoring
  Timer? _uiCheckTimer;
  final Queue<int> _responseTimeHistory = Queue<int>();
  int _consecutiveSlowResponses = 0;
  final int _historyLimit = 10;
  bool _hasShownWarning = false;
  int _testCount = 0;
  
  // Memory monitoring with isolates
  Isolate? _isolate;
  ReceivePort? _receivePort;
  Function(String message, int usageMB)? _onMemoryWarning;
  late int _warningThresholdMB;
  late Duration _checkInterval;
  
  /// Set up memory monitoring parameters
  void configure({
    Function(String message, int usageMB)? onMemoryWarning,
    int? warningThresholdMB,
    Duration? checkInterval,
  }) {
    _onMemoryWarning = onMemoryWarning;
    if (warningThresholdMB != null) _warningThresholdMB = warningThresholdMB;
    if (checkInterval != null) _checkInterval = checkInterval;
  }
  
  /// Start monitoring for potential memory issues through both UI responsiveness
  /// and direct memory measurement in an isolate
  void startMonitoring({Duration? uiCheckInterval}) {
    stopMonitoring(); // Ensure any existing monitoring is stopped
    
    // Start UI responsiveness monitoring
    _uiCheckTimer = Timer.periodic(
      uiCheckInterval ?? const Duration(seconds: 10), 
      (timer) => _checkUIResponsiveness()
    );
    
    // Start isolate-based memory monitoring
    _receivePort = ReceivePort();
    Isolate.spawn(
      _memoryMonitorIsolate, 
      _MemoryMonitorParams(
        _receivePort!.sendPort, 
        _warningThresholdMB,
        _checkInterval,
      )
    ).then((isolate) {
      _isolate = isolate;
      _receivePort!.listen((message) {
        if (message is Map<String, dynamic> && message.containsKey('message')) {
          final warningMessage = message['message'] as String;
          final usageMB = message['usageMB'] as int;
          
          // Always log the warning
          debugPrint(warningMessage);
          
          // Notify listener if provided
          _onMemoryWarning?.call(warningMessage, usageMB);
          
          // Log to crashlytics in non-debug mode
          if (!kDebugMode) {
            FirebaseCrashlytics.instance.recordError(
              Exception('Memory usage warning'),
              StackTrace.current,
              reason: warningMessage,
              fatal: false,
            );
          }
        }
      });
    }).catchError((error) {
      debugPrint('Failed to start memory monitoring isolate: $error');
    });
  }
  
  /// Stop all monitoring
  void stopMonitoring() {
    // Stop UI monitoring
    _uiCheckTimer?.cancel();
    _uiCheckTimer = null;
    
    // Stop isolate-based monitoring
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;
    _receivePort?.close();
    _receivePort = null;
    
    // Reset state
    _responseTimeHistory.clear();
    _hasShownWarning = false;
    _consecutiveSlowResponses = 0;
    _testCount = 0;
  }
  
  /// Check UI responsiveness as an indirect way to detect memory issues
  void _checkUIResponsiveness() {
    try {
      _testCount++;
      
      // Measure how long it takes to schedule and run a microtask
      final stopwatch = Stopwatch()..start();
      
      // This future will be scheduled in the next event loop
      Future.microtask(() {
        final responseTime = stopwatch.elapsedMilliseconds;
        stopwatch.stop();
        
        // Store in history queue
        _responseTimeHistory.add(responseTime);
        if (_responseTimeHistory.length > _historyLimit) {
          _responseTimeHistory.removeFirst();
        }
        
        // Log performance in debug mode
        if (kDebugMode) {
          debugPrint('UI response time: ${responseTime}ms (test #$_testCount)');
        }
        
        // Check for slow UI response which could indicate memory issues
        _detectPotentialIssue(responseTime);
      });
        
    } catch (e, stack) {
      if (!kDebugMode) {
        FirebaseCrashlytics.instance.recordError(
          e, 
          stack,
          reason: 'UI responsiveness check failed',
          fatal: false,
        );
      }
    }
  }
  
  /// Detect potential memory issues through UI responsiveness degradation
  void _detectPotentialIssue(int responseTimeMs) {
    // Response time thresholds
    const int slowResponseThreshold = 16;  // Over 16ms (below 60fps) is considered slow
    const int criticalResponseThreshold = 100;  // Over 100ms is critical
    
    // Check for critical single response time
    if (responseTimeMs > criticalResponseThreshold) {
      String message = 'Critical UI lag detected: ${responseTimeMs}ms response time';
      
      if (kDebugMode) {
        debugPrint('🚨 $message');
      } else {
        FirebaseCrashlytics.instance.recordError(
          Exception('Critical UI lag'),
          StackTrace.current,
          reason: message,
          fatal: false,
        );
      }
      return;
    }
    
    // If response time is slow, increment counter
    if (responseTimeMs > slowResponseThreshold) {
      _consecutiveSlowResponses++;
      
      // If we have multiple consecutive slow responses, it might indicate a memory issue
      if (_consecutiveSlowResponses >= 3 && !_hasShownWarning) {
        _hasShownWarning = true;
        
        // Log warning
        String message = 'Potential performance issue detected: '
            'Slow UI response (${responseTimeMs}ms) for $_consecutiveSlowResponses consecutive checks';
        
        if (kDebugMode) {
          debugPrint('⚠️ $message');
          debugPrint('This may indicate memory pressure or other resource issues.');
        } else {
          FirebaseCrashlytics.instance.recordError(
            Exception('Performance degradation detected'),
            StackTrace.current,
            reason: message,
            fatal: false,
          );
        }
      }
    } else {
      // Reset counter if response time recovers
      _consecutiveSlowResponses = 0;
    }
  }
  
  /// Manually trigger a check and show app status summary
  String checkAppStatus() {
    _hasShownWarning = false;
    
    // Calculate average response time from history
    if (_responseTimeHistory.isEmpty) {
      return 'No UI response data collected yet';
    }
    
    final avgResponse = _responseTimeHistory.reduce((a, b) => a + b) / _responseTimeHistory.length;
    
    final summary = StringBuffer();
    summary.writeln('📊 App Status Summary:');
    summary.writeln('- Average UI response time: ${avgResponse.toStringAsFixed(1)}ms');
    summary.writeln('- Tracking for: ${_responseTimeHistory.length} intervals');
    
    if (avgResponse > 16) {
      summary.writeln('⚠️ App is responding slowly, which may indicate memory pressure.');
      summary.writeln('Consider checking for memory leaks or resource-intensive operations.');
    } else {
      summary.writeln('✅ App performance looks good!');
    }
    
    final statusText = summary.toString();
    if (kDebugMode) {
      debugPrint(statusText);
    }
    
    return statusText;
  }

  /// Isolate function to monitor memory usage
  static void _memoryMonitorIsolate(_MemoryMonitorParams params) {
    final SendPort sendPort = params.sendPort;
    final int thresholdMB = params.thresholdMB;
    final Duration interval = params.interval;
    
    // Create a periodic timer to check memory usage
    Timer? timer;
    timer = Timer.periodic(interval, (_) {
      try {
        // This is a simplified approach - in a real app, you should
        // use platform-specific methods to get actual memory usage.
        // For Android: Use MethodChannel to call ActivityManager.getMemoryInfo
        // For iOS: Use MethodChannel to access mach_task_basic_info
        
        // Simulated memory check for example purposes:
        // In a real implementation, replace this with actual platform-specific code
        int usageMB = _simulateMemoryUsage();
        
        // Check if memory usage exceeds threshold
        if (usageMB > thresholdMB) {
          sendPort.send({
            'message': 'MEMORY_WARNING: Usage at $usageMB MB exceeds threshold of $thresholdMB MB',
            'usageMB': usageMB,
          });
        }
      } catch (e) {
        sendPort.send({
          'message': 'Error monitoring memory: $e',
          'usageMB': 0,
        });
      }
    });
    
    // Set up a receive port to handle cleanup
    final receivePort = ReceivePort();
    sendPort.send({'port': receivePort.sendPort});
    
    receivePort.listen((message) {
      if (message == 'STOP') {
        timer?.cancel();
        receivePort.close();
      }
    });
  }
  
  // A simplified simulation for example purposes
  // In a real app, implement platform-specific memory checks instead
  static int _simulateMemoryUsage() {
    // This is just a simulation for the example
    // In a real app, you would get the actual memory usage from the platform
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    return 50 + random; // Simulates memory usage between 50-150MB
  }
}

/// Private class to pass parameters to the isolate
class _MemoryMonitorParams {
  final SendPort sendPort;
  final int thresholdMB;
  final Duration interval;
  
  _MemoryMonitorParams(this.sendPort, this.thresholdMB, this.interval);
} 