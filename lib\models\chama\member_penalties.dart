// To parse this JSON data, do
//
//     final memberPenalty = memberPenaltyFromJson(jsonString);

import 'dart:convert';

MemberPenalty memberPenaltyFromJson(String str) => MemberPenalty.fromJson(json.decode(str));

String memberPenaltyToJson(MemberPenalty data) => json.encode(data.toJson());

class MemberPenalty {
    bool? status;
    String? message;
    Data? data;

    MemberPenalty({
        this.status,
        this.message,
        this.data,
    });

    factory MemberPenalty.fromJson(Map<String, dynamic> json) => MemberPenalty(
        status: json["status"],
        message: json["message"],
        data: Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
    };
}

class Data {
    List<Item>? items;
    int? page;
    int? size;
    int? maxPage;
    int? totalPages;
    int? total;
    bool? last;
    bool? first;
    int? visible;

    Data({
        this.items,
        this.page,
        this.size,
        this.maxPage,
        this.totalPages,
        this.total,
        this.last,
        this.first,
        this.visible,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        items: List<Item>.from(json["items"].map((x) => Item.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
    );

    Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
    };
}

class Item {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    int? chamaId;
    int? memberId;
    int? penaltyId;
    Penalty? penalty;
    int? amount;
    dynamic paidAmount;
    String? reason;
    String? status;

    Item({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.chamaId,
        this.memberId,
        this.penaltyId,
        this.penalty,
        this.amount,
        this.paidAmount,
        this.reason,
        this.status,
    });

    factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        penaltyId: json["penalty_id"],
        penalty: Penalty.fromJson(json["penalty"]),
        amount: json["amount"],
        paidAmount: json["paid_amount"],
        reason: json["reason"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "member_id": memberId,
        "penalty_id": penaltyId,
        "penalty": penalty?.toJson(),
        "amount": amount,
        "paid_amount": paidAmount,
        "reason": reason,
        "status": status,
    };
}

class Penalty {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    dynamic chamaId;

    Penalty({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.chamaId,
    });

    factory Penalty.fromJson(Map<String, dynamic> json) => Penalty(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
    };
}


// To parse this JSON data, do
//
//     final memberPenaltiesUsingAccNumber = memberPenaltiesUsingAccNumberFromJson(jsonString);

MemberPenaltiesUsingAccNumber memberPenaltiesUsingAccNumberFromJson(String str) => MemberPenaltiesUsingAccNumber.fromJson(json.decode(str));

String memberPenaltiesUsingAccNumberToJson(MemberPenaltiesUsingAccNumber data) => json.encode(data.toJson());

class MemberPenaltiesUsingAccNumber {
    dynamic status;
    String? message;
    List<MemPenalty>? data;

    MemberPenaltiesUsingAccNumber({
        this.status,
        this.message,
        this.data,
    });

    factory MemberPenaltiesUsingAccNumber.fromJson(Map<String, dynamic> json) => MemberPenaltiesUsingAccNumber(
        status: json["status"],
        message: json["message"],
        data: List<MemPenalty>.from(json["data"].map((x) => MemPenalty.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
    };
}

class MemPenalty {
    int? id;
    int? amount;
    int? paidAmount;
    String? reason;
    Status? status;
    String? title;
    String? description;

    MemPenalty({
        this.id,
        this.amount,
        this.paidAmount,
        this.reason,
        this.status,
        this.title,
        this.description,
    });

    factory MemPenalty.fromJson(Map<String, dynamic> json) => MemPenalty(
        id: json["Id"],
        amount: json["amount"],
        paidAmount: json["paid_amount"],
        reason: json["reason"],
        status: statusValues.map[json["status"]],
        title: json["title"],
        description: json["description"],
    );

    Map<String, dynamic> toJson() => {
        "Id": id,
        "amount": amount,
        "paid_amount": paidAmount,
        "reason": reason,
        "status": statusValues.reverse[status],
        "title": title,
        "description": description,
    };
}

enum Status {
    PENDING_PAYMENT
}

final statusValues = EnumValues({
    "PENDING-PAYMENT": Status.PENDING_PAYMENT
});

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
            reverseMap = map.map((k, v) => MapEntry(v, k));
            return reverseMap;
    }
}
