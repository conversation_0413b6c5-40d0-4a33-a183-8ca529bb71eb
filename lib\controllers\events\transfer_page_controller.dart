import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/screens/dashboard/pages/events/transfers_page.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

import '../../helpers/show_toast.dart';
import 'events_controller.dart';

class TransferPageController extends GetxController implements GetxService {
  RxInt page = 0.obs;
  RxString selectedProvider = 'M-PESA'.obs;
  RxBool isTransfering = false.obs;
  final logger = Get.find<Logger>();
  RxBool isConfirmingTransfer = false.obs;
  final HttpService apiProvider = Get.find();
  final Rx<PaymentChannels?> selectedBank = Rx<PaymentChannels?>(null);

  Future transferRequestConfirm(
      BuildContext context, Map<String, dynamic> data) async {
    isConfirmingTransfer(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.TRANSFERCONFIRM, method: Method.POST, params: data);
      isConfirmingTransfer(false);

      if (res.data["status"] ?? false) {
        Get.snackbar('Success', res.data['message'],
            backgroundColor: Colors.green);
        ToastUtils.showToast(
          res.data['message'],
          toastType: ToastType.success,
        );
      } else {
        Get.snackbar(
            'Error', res.data['message'] ?? 'couldnt complete transaction',
            backgroundColor: Colors.red);
        ToastUtils.showErrorToast(
          context,
          'Error',
          res.data['message'] ?? 'couldnt complete transaction',
        );
      }
    } catch (e) {
      ToastUtils.showErrorToast(context, 'Error', 'couldnt complete transfer');
      // Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      isConfirmingTransfer(false);
      logger.e(e);
    } finally {
      isConfirmingTransfer(false);
      Get.back();
    }
  }

  Future transferRequest(BuildContext context,
      {
      required int kittyId,
      required int amount,
      String? paybill,
      String? accNo,
      String? phoneNumber,
      String? till,
      String? bankAccount,
      required String transferMode,
      required String reason}) async {
    isTransfering(true);
    try {
      final Eventcontroller eventController = Get.find();
      final GetStorage box = Get.find();
      var res = await apiProvider
          .request(url: ApiUrls.TRANSFERREQUEST, method: Method.POST, params: {
        "amount": amount,
        //todo
        "user_id": eventController.getLocalUser()?.id,
        "kitty_id": kittyId,

        "channel_code": selectedProvider.value == "M-PESA"
            ? 63902
            : selectedProvider.value == "SasaPay"
                ? 0
                : selectedProvider.value == "AirtelMoney"
                    ? 63903
                    : selectedProvider.value == "Card"
                        ? 55
                        : selectedProvider.value == "Tkash"
                            ? 63907
                            : selectedProvider.value == "BANK"
                                ? selectedBank.value!.channelCode
                                : null,
        "recipient_account_number": transferMode == "WALLET"
            ? phoneNumber
            : transferMode == "TILL"
                ? till
                : transferMode == "PAYBILL"
                    ? paybill
                    : transferMode == "BANK"
                        ? bankAccount
                        : "",
        "recipient_account_ref": transferMode == "PAYBILL" ? accNo : null,
        "reason": reason,
        "transfer_mode": transferMode,
        "latitude": box.read(CacheKeys.lat),
        "longitude": box.read(CacheKeys.long),
        "device_id": box.read(CacheKeys.deviceId),
        "device_model": box.read(CacheKeys.deviceModel),
      });
      isTransfering(false);
      if (res.data["status"] ?? false) {
        Get.to(() => TransferConfirmPage(
            res: res,
            selectedProvider: selectedProvider,
            kittyId: kittyId,
            amount: amount,
            transferMode: transferMode,
            receiverName: res.data['beneficiary_account'] ?? '',
            receiverAccRef: res.data['beneficiary_account_ref'] ?? '',
            reason: reason));
      } else {
        print(res.data);
        Get.snackbar(
            'Error', res.data['message'] ?? 'couldnt complete transaction',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      // Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      isTransfering(false);
      logger.e(e);
      return false;
    }
  }
}
