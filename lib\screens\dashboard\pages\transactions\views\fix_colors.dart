// Color mapping for transaction system
// Maps problematic color references to existing AppColors

import 'package:flutter/material.dart';
import 'package:onekitty/helpers/colors.dart';

class TransactionColors {
  // Map problematic colors to existing ones
  static Color get whiteA700 => Colors.white;
  static Color get gray900 => AppColors.dark;
  static Color get gray600 => AppColors.neutralGrey;
  static Color get gray500 => AppColors.neutralGrey;
  static Color get gray400 => AppColors.neutral;
  static Color get gray300 => AppColors.neutral;
  static Color get gray200 => AppColors.slate;
  static Color get gray100 => AppColors.background;
  static Color get gray50 => AppColors.background;
  static Color get gray700 => AppColors.neutralDark;
  static Color get indigo500 => AppColors.primary;
  static Color get indigo200 => AppColors.midBlue;
  static Color get indigo50 => AppColors.background;
}

class TransactionTextStyles {
  // Map problematic text styles to simple ones
  static TextStyle get titleMediumGray900 => const TextStyle(
    color: Colors.black87,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle get titleMediumGray600 => const TextStyle(
    color: Colors.black54,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get bodyMediumGray900 => const TextStyle(
    color: Colors.black87,
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  
  static TextStyle get bodyMediumGray500 => const TextStyle(
    color: Colors.black54,
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  
  static TextStyle get bodySmallGray500 => const TextStyle(
    color: Colors.black54,
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );
  
  static TextStyle get labelMediumGray900 => const TextStyle(
    color: Colors.black87,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get labelMediumGray600 => const TextStyle(
    color: Colors.black54,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );
}
