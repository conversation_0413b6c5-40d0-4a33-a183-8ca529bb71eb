import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/utils/cache_keys.dart';

class LocationService extends GetxController {
  final GetStorage _box = GetStorage();

  /// Request location permission and get current position if granted
  Future<bool> requestLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled, we can't get location
      return false;
    }

    // Check location permission status
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      // Check if we've already requested permission
      bool hasRequested = _box.read<bool>(CacheKeys.locationPermissionRequested) ?? false;
      if (hasRequested) {
        return false;
      }

      // Request permission for the first time
      permission = await Geolocator.requestPermission();
      // Store that we've requested permission
      _box.write(CacheKeys.locationPermissionRequested, true);
      
      if (permission == LocationPermission.denied) {
        // Permission denied
        return false;
      }
    }

    // Permission denied forever
    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    // Permission granted, get position
    if (permission == LocationPermission.always || 
        permission == LocationPermission.whileInUse) {
      try {
        Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        
        // Save location to storage
        saveLocation(position.latitude.toString(), position.longitude.toString());
        return true;
      } catch (e) {
        print('Error getting location: $e');
        return false;
      }
    }

    return false;
  }

  /// Save location to GetStorage
  void saveLocation(String lat, String long) {
    _box.write(CacheKeys.lat, lat);
    _box.write(CacheKeys.long, long);
  }

  /// Get saved latitude
  String? getLatitude() {
    return _box.read<String>(CacheKeys.lat);
  }

  /// Get saved longitude
  String? getLongitude() {
    return _box.read<String>(CacheKeys.long);
  }

  /// Check if location is saved
  bool hasLocation() {
    return _box.hasData(CacheKeys.lat) && _box.hasData(CacheKeys.long);
  }
}