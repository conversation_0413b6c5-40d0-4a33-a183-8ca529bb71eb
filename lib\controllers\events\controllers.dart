import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/properties/email.dart';
import 'package:flutter_contacts/properties/name.dart';
import 'package:flutter_contacts/properties/phone.dart';

import 'package:get/get.dart';

import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/models/events/enums.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'dart:async';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_contacts/contact.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_contacts_service/flutter_contacts_service.dart';
const primaryColor = Color(0xff4355b6);
Color scaffoldBackgroundColor = const Color(0xfff8fafc);

class GlobalControllers extends GetxController implements GetxService {
  final contacts = <Contact>[].obs;
  final paymentChannels = <PaymentChannels>[].obs;
  final isLight = ValueNotifier(false);
  Rx<Enums> enums = Enums(
      eventPaymentModes: [],
      eventStatus: [],
      eventTypes: [],
      ticketStatus: [],
      ticketType: []).obs;
  var logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
final box = GetStorage();
final imageSize = 14.0;
  @override
  void onInit() {
    super.onInit();
    getEnums();
    getPaymentChannels();
    // Remove automatic contact fetching on app start
    // Contacts will be fetched only when user needs them
  }
String getCode () {
  
  final code = box.read('code') ?? '';
  box.remove('code');
  return code;
}
void clearCode(){
  box.remove('code');
}
  Future<void> getEnums() async {
    try {
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETENUMS);

      if (response.data != null) {
        final _returneddata = response.data['data'] as Map<String, dynamic>;
        enums = Enums.fromJson(_returneddata).obs;
      } else {
        logger.v('No data or unexpected response structure,');
        // Retry after error
        await Future.delayed(const Duration(seconds: 1));
        await getEnums();
      }
    } catch (e) {
      logger.e('Error fetching enums: $e, retrying...');
      // Retry after error
      await Future.delayed(const Duration(seconds: 2));
      await getEnums();
    }
  }

  Future<void> getPaymentChannels() async {
    try {
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.getPaymentChannels);

      if (response.data['status'] ?? false) {
        final _returneddata = response.data['data'] as List;
        paymentChannels(
            _returneddata.map((e) => PaymentChannels.fromJson(e)).toList());
      } else {
        logger.v('No data or unexpected response structure');
        await Future.delayed(const Duration(seconds: 2));
        await getPaymentChannels();
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 2));
        await getPaymentChannels();
      logger.e('Error fetching paymentChannels: $e');
    }
  }

  // Isolate worker function for processing contacts
  static List<Contact> _processContacts(List<ContactInfo> contactInfos) {
    return contactInfos.map((info) {
      return Contact(
        id: info.identifier ?? '',
        photo: info.avatar,
        name: info.displayName != null ? Name(
          first: info.givenName ?? '',
          last: info.familyName ?? '',
          middle: info.middleName ?? '',
          prefix: info.prefix ?? '',
          suffix: info.suffix ?? '',
        ) : null,
        displayName: info.displayName ?? '',
        emails: info.emails?.map((e) => Email(e.value ?? '')).toList(),
        phones: info.phones?.map((p) => Phone(p.value ?? '', normalizedNumber: p.value ?? '')).toList(),
      );
    }).toList();
  }

  Future<void> fetchContacts() async {
    if (await Permission.contacts.request().isGranted) {
      try {
        final contactInfos = await FlutterContactsService.getContacts();
        // Use compute to run contact processing on a separate isolate
        final processedContacts = await compute<List<ContactInfo>, List<Contact>>(_processContacts, contactInfos.toList());
        contacts.value = processedContacts;
      } catch (e) {
        logger.e('Error processing contacts: $e');
        contacts.value = [];
      }
    } else {
      contacts.value = [];
    }
  }
}
