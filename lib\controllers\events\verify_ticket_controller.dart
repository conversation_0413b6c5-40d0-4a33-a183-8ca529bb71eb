import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/verify_ticket.dart';
import 'package:onekitty/screens/dashboard/pages/events/verify_ticket.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class VerifyTicketController extends GetxController implements GetxService {
  final pageController = PageController().obs;
  RxBool isVerifying = false.obs;
  final HttpService apiProvider = Get.find();
  RxString prefix = "".obs;
  final logger = Get.find<Logger>();
  RxBool isConfirming = false.obs;
  // final selectedTickets = <Map<String, dynamic>>[].obs;
  Future verifyTicket(int eventId, String code) async {
    isVerifying(true);
    try {
      var response = await apiProvider.request(
        url: ApiUrls.VERIFYTICKET,
        method: Method.POST,
        params: {"code": code, "event_id": "$eventId"},
      );

      if (response.data['status'] ?? false) {
        Get.to(() => VerifyConfirm(
              verify: VerifyTicketConfirm.fromJson(
                  response.data['data'] as Map<String, dynamic>),
              eventId: eventId,
              code: code,
            ));
      } else {
        Get.snackbar(
            'Error', '${response.data['message'] ?? "Failed to verify ticket"}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('$e');
      // Get.snackbar('Error', 'An error occurred: ',
          // backgroundColor: Colors.amber);
    } finally {
      isVerifying(false);
    }
  }

  Future verifyTicketConfirm(int eventId, String code, List<int> ids) async {
    isConfirming(true);
    try {
      var response = await apiProvider.request(
        url: ApiUrls.VERIFYTICKETCONFIRM,
        method: Method.POST,
        params: {
          "code": code,
          "event_id": "$eventId",
          "transaction_ticket_ids": ids
        },
      );

      if (response.data['status'] ?? false) {
        Logger().d("prefix: ${prefix.value}");
        // clear the prefix text
        if (prefix.value.isNotEmpty) {
          if (prefix.value.contains('-')) {
            var split = prefix.value.split("-");
            prefix("${split[0]}-");
          }
        }
        Get.back();
        Get.snackbar('Success', '${response.data['message'] ?? "Success"}',
            backgroundColor: Colors.green);
      } else {
        Get.snackbar(
            'Error', '${response.data['message'] ?? "Failed to verify ticket"}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e(e);
    } finally {
      isConfirming(false);
    }
  }
}
