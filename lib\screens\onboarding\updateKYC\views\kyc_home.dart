import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/kyc_controller.dart';
import 'capture_front.dart';
import '../widgets/clay_button.dart';
import '../widgets/clay_progress_bar.dart';

class KYCHomePage extends StatelessWidget {
  

const    KYCHomePage({super.key});

  @override
  Widget build(BuildContext context) {
  final KYCController controller = Get.find<KYCController>();
    return Scaffold(
      appBar: AppBar(title: const Text('KYC Verification')),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const ClayProgress(currentStep: 1, totalSteps: 5),
            const SizedBox(height: 30),
            TextField(
              controller: controller.idNumber,
              decoration: InputDecoration(
                labelText: 'ID Number',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
              ),
            ),),
              const Sized<PERSON><PERSON>(height: 40),
            <PERSON><PERSON><PERSON><PERSON>(
              text: 'Start Verification',
              onPressed: () => Get.to(() => const CaptureFrontIDPage()
              ),
            )
          ],
        ),
      ),
    );
  }
}