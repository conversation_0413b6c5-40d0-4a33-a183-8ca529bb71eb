// To parse this JSON data, do
//
//     final resources = resourcesFromJson(jsonString);

import 'dart:convert';

Resources resourcesFromJson(String str) => Resources.fromJson(json.decode(str));

String resourcesToJson(Resources data) => json.encode(data.toJson());

class Resources {
    bool status;
    String message;
    List<ResourceDts>? data;

    Resources({
        required this.status,
        required this.message,
         this.data,
    });

    factory Resources.fromJson(Map<String, dynamic> json) => Resources(
        status: json["status"],
        message: json["message"],
        data: List<ResourceDts>.from(json["data"].map((x) => ResourceDts.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
    };
}

class ResourceDts {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    String? title;
    String? description;
    String? mediaUrl;
    int? chamaId;
    String? type;
    String? status;

    ResourceDts({
         this.id,
         this.createdAt,
         this.updatedAt,
         this.deletedAt,
         this.title,
         this.description,
         this.mediaUrl,
         this.chamaId,
         this.type,
         this.status,
    });

    factory ResourceDts.fromJson(Map<String, dynamic> json) => ResourceDts(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        description: json["description"],
        mediaUrl: json["media_url"],
        chamaId: json["chama_id"],
        type: json["type"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "media_url": mediaUrl,
        "chama_id": chamaId,
        "type": type,
        "status": status,
    };
}

