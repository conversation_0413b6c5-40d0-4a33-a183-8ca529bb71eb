import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/meeting_request.dart';
import 'package:onekitty/models/chama/meetings.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/datetime/combined_datetime.dart';

import '../../../../../utils/utils_exports.dart';

class AddMeetingPage extends StatefulWidget {
  final bool isUpdating;
  final Meeting? meeting;
  const AddMeetingPage({super.key, required this.isUpdating, this.meeting});

  @override
  State<AddMeetingPage> createState() => _AddMeetingPageState();
}

class _AddMeetingPageState extends State<AddMeetingPage> {
  final formKey = GlobalKey<FormState>();
  TextEditingController startDateController = TextEditingController();
  TextEditingController startTimeController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController endTimeController = TextEditingController();
  TextEditingController venueController = TextEditingController();
  TextEditingController locationTipController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  TextEditingController desController = TextEditingController();
  TextEditingController freqcyController = TextEditingController();
  TextEditingController meetingLinkController = TextEditingController();
  String? _dropdownValue;
  bool _showDropdown = false;

  final box = GetStorage();
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  List<String> dropdownItems = [];
  String? selectedvalue;

  void toggleDropdownVisibility() {
    setState(() {
      _showDropdown = !_showDropdown;
    });
  }

  void dropdownCallback(String? selectedValue) {
    if (selectedValue is String) {
      setState(() {
        _dropdownValue = selectedValue;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    getfrequency();
    if (widget.isUpdating && widget.meeting != null) {
      venueController.text = widget.meeting?.venue ?? "";
      titleController.text = widget.meeting?.title ?? "";
      desController.text = widget.meeting?.description ?? "";
      startDateController.text = DateFormat("yyyy-MM-dd")
          .format(widget.meeting?.startDate ?? DateTime.now());
      startTimeController.text = DateFormat("HH:ss")
          .format(widget.meeting?.startDate ?? DateTime.now());
      endDateController.text = DateFormat("yyyy-MM-dd")
          .format(widget.meeting?.endDate ?? DateTime.now());
      endTimeController.text = DateFormat("HH:ss")
          .format(widget.meeting?.startDate ?? DateTime.now());
      _dropdownValue = widget.meeting?.eventType;
      locationTipController.text = widget.meeting?.locationTip ?? "";
      if (_dropdownValue == "VIRTUAL") {
        meetingLinkController.text = widget.meeting?.venue ?? "";
      }
      selectedvalue = widget.meeting?.frequency;
    }
  }

  void getfrequency() {
    // _chamaController.getfrequency();
    setState(() {
      dropdownItems = chamaController.frequencies
          .map((frequency) => frequency.frequency)
          .toList();
    });
  }

  @override
  void dispose() {
    startDateController.dispose();
    startTimeController.dispose();
    endDateController.dispose();
    endTimeController.dispose();
    venueController.dispose();
    titleController.dispose();
    desController.dispose();
    locationTipController.dispose();
    meetingLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:  Text(  widget.isUpdating ? "Update Meeting" : "Add Meeting",),
      ),
      persistentFooterButtons: [
        Obx(
          () => FilledButton( 
                      onPressed: () async {
                        await addMeeting();
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children:  [widget.isUpdating ? const Text("Update") : const Text("Add Meeting"), const SizedBox(width: 10,),
                      if (chamaController.isAddMeetingLoading.value) const CircularProgressIndicator(strokeWidth: 2, color: Colors.white,),],))
                ),],
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [ 
                Text(
                  widget.isUpdating
                      ? ""
                      : "Add a meeting which can be seen by all members",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: appTheme.gray600, fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  height: 8,
                ),
                const SingleLineRow(text: "Meeting Information"),
                CustomTextField(
                  controller: titleController,
                  labelText: "Meeting Title",
                  hintText: "e.g. Monthly Budget Review",
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "This field cannot be empty";
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 15,
                ),
                CustomTextField(
                  controller: desController,
                  labelText: "Meeting Description",
                  hintText: "Describe the purpose of this meeting",
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "This field cannot be empty";
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5.0),
                  child: SingleLineRow(
                    text: "Start Date",
                    widget: Text(
                      "Start Time",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
                    ),
                  ),
                ),
                DatePicker(
                  date: startDateController,
                  time: startTimeController,
                  isAllow: true,
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5.0),
                  child: SingleLineRow(
                    text: "End Date",
                    widget: Text(
                      "End Time",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
                    ),
                  ),
                ),
                DatePicker(
                  date: endDateController,
                  time: endTimeController,
                  isAllow: true,
                ),
                const SizedBox(
                  height: 25,
                ),
                const SingleLineRow(text: "Event Type"),
                Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: _dropdownValue == null ? Colors.red : AppColors.blueButtonColor),
                      borderRadius: BorderRadius.circular(12)),
                  child: ListTile(
                    onTap: toggleDropdownVisibility,
                    title: Text(_dropdownValue == null ? "Select event type (required)" : "Event type: $_dropdownValue"),
                    trailing: DropdownButton(
                        underline: const SizedBox(),
                        items: const [
                          DropdownMenuItem(
                            value: "PHYSICAL",
                            child: Text("PHYSICAL"),
                          ),
                          DropdownMenuItem(
                            value: "VIRTUAL",
                            child: Text("VIRTUAL"),
                          ),
                        ],
                        value: _dropdownValue,
                        hint: const Text("Select event type"),
                        onChanged: dropdownCallback),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                if (_dropdownValue == "VIRTUAL") ...[
                  const SingleLineRow(text: "Meeting Link"),
                  CustomTextField(
                    controller: meetingLinkController,
                    labelText: "Enter meeting link",
                    hintText: "https://meet.zoom.us/...",
                    validator: (p0) {
                      if (p0!.isEmpty) {
                        return "This field cannot be empty";
                      }
                      return null;
                    },
                  ),
                ] else ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            const SingleLineRow(text: "Meeting Venue"),
                            CustomTextField(
                              controller: venueController,
                              labelText: "Venue",
                              hintText: "e.g. Conference Room A",
                              validator: (p0) {
                                if (p0!.isEmpty) {
                                  return "This field cannot be empty";
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          children: [
                            SingleLineRow(
                              text: "Location Tip",
                              popup: KtStrings.locationTip,
                            ),
                            CustomTextField(
                              controller: locationTipController,
                              labelText: "Location Tip",
                              hintText: "e.g. Near the main entrance",
                              validator: (p0) {
                                if (p0!.isEmpty) {
                                  return "This field cannot be empty";
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
                SingleLineRow(
                  text: "How often will the meeting be held?",
                  popup: KtStrings.meetingFrequency,
                ),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: "Select Frequency",
                    hintText: "How often will meetings occur",
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: selectedvalue == null ? Colors.red : AppColors.blueButtonColor,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: AppColors.blueButtonColor,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  isExpanded: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please select a frequency";
                    }
                    return null;
                  },
                  items: dropdownItems
                      .map(
                        (String item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(
                            item,
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                  value: selectedvalue,
                  onChanged: (String? value) {
                    setState(() {
                      selectedvalue = value;
                      freqcyController.text = value!;
                    });
                  },
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  addMeeting() async {
    // Check if dropdown values are selected
    if (_dropdownValue == null) {
      Snack.showInfo(message1: "Please select an event type");
      return;
    }
    
    if (selectedvalue == null) {
      Snack.showInfo(message1: "Please select a meeting frequency");
      return;
    }

    if (formKey.currentState!.validate()) {
      dynamic request;
      final startDate = startDateController.text;
      final startTime = startTimeController.text;
      final endDate = endDateController.text;
      final endTime = endTimeController.text;

      final combinedStartTime = combineDateTime(startDate, startTime);
      final combinedEndTime = combineDateTime(endDate, endTime);
      final formattedStartTime = formatDateTime(combinedStartTime);
      final formattedEndTime = formatDateTime(combinedEndTime);

      final startDateTime = DateTime.tryParse(formattedStartTime);
      final endDateTime = DateTime.tryParse(formattedEndTime);

      // Set venue and location tip to meeting link if virtual event
      if (_dropdownValue == "VIRTUAL") {
        venueController.text = meetingLinkController.text.trim();
        locationTipController.text = meetingLinkController.text.trim();
      }

      // Also validate start and end dates
      if (startDateTime == null || endDateTime == null) {
        Snack.showInfo(message1: "Please enter valid start and end dates/times");
        return;
      }

      if (startDateTime.isAfter(endDateTime)) {
        Snack.showInfo(message1: "Start date/time must be before end date/time");
        return;
      }

      if (widget.isUpdating) {
        request = UpdateMeeting(
          id: widget.meeting?.id,
          title: titleController.text.trim(),
          description: desController.text.trim(),
          venue: venueController.text.trim(),
          eventType: _dropdownValue,
          startDate: startDateTime,
          endDate: endDateTime,
          frequency: selectedvalue,
          chamaId: chamaDataController.chama.value.chama?.id,
          memberId: chamaDataController.chama.value.member?.id,
          email: chamaDataController.chama.value.chama?.email,
          locationTip: locationTipController.text.trim(),
          latitude: box.read(CacheKeys.lat),
          longitude: box.read(CacheKeys.long),
        );
      } else {
        request = MeetingRequest(
          title: titleController.text.trim(),
          description: desController.text.trim(),
          venue: venueController.text.trim(),
          eventType: _dropdownValue,
          startDate: startDateTime,
          endDate: endDateTime,
          frequency: selectedvalue,
          chamaId: chamaDataController.chama.value.chama?.id,
          memberId: chamaDataController.chama.value.member?.id,
          email: chamaDataController.chama.value.chama?.email,
          locationTip: locationTipController.text.trim(),
          latitude: box.read(CacheKeys.lat),
          longitude: box.read(CacheKeys.long),
        );
      }
      bool res;
      if (widget.isUpdating) {
        res =
            await chamaController.addMeeting(request: request, isUpdate: true);
      } else {
        res = await chamaController.addMeeting(request: request);
      }
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        Navigator.pop(context);
        Get.offAllNamed(NavRoutes.meetings);
      } else {
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }
}
