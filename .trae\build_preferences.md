# Build Preferences

## User Preference: NO WINDOWS BUILDS
**IMPORTANT**: User explicitly requested to never build to Windows platform.

## Preferred Build Targets

### Web Builds (Recommended)
- `flutter run -d chrome --debug` - Quick testing with Chrome
- `flutter run -d edge --debug` - Alternative web testing with Edge
- `flutter build web --release` - Production web build

### APK Builds
- `flutter build apk --debug` - Debug APK for testing
- `flutter build apk --release` - Production APK

### STRICTLY AVOID
- **Windows builds** - User preference: NEVER use Windows builds
- `flutter run -d windows` - FORBIDDEN by user request
- Any Windows-specific build commands

## Testing Strategy
- Use web builds for quick UI testing and debugging
- Use APK builds for mobile-specific functionality testing
- Prioritize Chrome for web development due to better DevTools integration
- Always respect user's no-Windows-build preference