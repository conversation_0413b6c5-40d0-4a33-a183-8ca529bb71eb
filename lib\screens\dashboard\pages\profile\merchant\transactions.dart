import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/utils/utils_exports.dart';

class Transactions extends StatefulWidget {
  final Kitty? kitty;
  const Transactions({super.key, this.kitty});

  @override
  State<Transactions> createState() => _TransactionsState();
}

class _TransactionsState extends State<Transactions> {
  final dateformat = DateFormat('EE, dd MMMM');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Column(
          children: [
            const RowAppBar(),
            Expanded(
              child: Container(
                height: 869.h,
                margin: EdgeInsets.symmetric(horizontal: 32.w),
                child: Align(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [_buildTransactionsList(context)],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context) {
    return GetX(
      init: UserKittyController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            await state.controller?.getMerchantTransactions(
                code: widget.kitty?.refererMerchantCode ?? 0,
                kittId: widget.kitty?.iD ?? 0);
          } catch (e) {
            print("Error loading kitties: $e");
            throw e;
          }
        });
      },
      builder: (UserKittyController controller) {
        if (controller.loadingTransactions.isTrue) {
          return SizedBox(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (controller.merchtransactions.isEmpty) {
          return const Center(
            child: Text("You have no earnings yet here"),
          );
        } else if (controller.merchtransactions.isNotEmpty) {
          return Expanded(
            child: GroupedListView<TransactionModel, DateTime>(
              elements: controller.merchtransactions,
              sort: false,
              useStickyGroupSeparators: true,
              groupBy: (TransactionModel element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 15.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, TransactionModel item) {
                return TransactionItem(item: item);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }
}

class TransactionItem extends StatelessWidget {
  final TransactionModel item;

  const TransactionItem({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 16.h),
      decoration: AppDecoration.outlineGray
          .copyWith(borderRadius: BorderRadiusStyle.circleBorder22),
      child: SizedBox(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration:
                  BoxDecoration(borderRadius: BorderRadiusStyle.roundedBorder6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 8.h),
                    child: Container(
                      margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                      padding: EdgeInsets.all(7.h),
                      decoration: AppDecoration.fillAGray
                          .copyWith(shape: BoxShape.circle),
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Text(
                          '${item.merchant?.merchantName?.isNotEmpty ?? false ? item.merchant?.merchantName![0] : ' '}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 14.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(item.transactionRef ?? "",
                            style: CustomTextStyles.titleSmallIndigo500),
                        SizedBox(height: 7.h),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            ' ${item.amount?.toString()}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          )

                          // style: TextStyle(
                          //     color: item.transactionType == "Contribution"
                          //         ? Colors.green
                          //         : Colors.red,
                          //     fontWeight: FontWeight.bold,
                          //     fontSize: 12),
                          ),
                      SizedBox(height: 2.h),
                      Opacity(
                        opacity: 0.4,
                        child: Text(
                            DateFormat.jm().format(item.createdAt!.toLocal()),
                            style: theme.textTheme.bodySmall),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
