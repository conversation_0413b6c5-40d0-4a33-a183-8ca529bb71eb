import 'package:flutter/material.dart';

class FullWidthOutlinedButton extends StatelessWidget {
  const FullWidthOutlinedButton(
      {super.key,
      this.color,
      this.fontSize,
      this.fontWeight,
      this.textColor,
      this.buttonHeight,
      this.width,
      this.borderRadius,
      required this.buttonText,
      required this.press});
  final double? buttonHeight;
  final double? fontSize;
  final FontWeight? fontWeight;
  final String buttonText;
  final Color? color, textColor;
  final VoidCallback press;
  final double? width;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: buttonHeight ?? 50,
      //width: width ?? context.width,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: color ?? Theme.of(context).primaryColor),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius ?? 12)),
          padding: const EdgeInsets.all(12),
        ),
        onPressed: press,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: Text(
            buttonText,
            style: TextStyle(
                fontSize: fontSize ?? 16,
                color: textColor ?? Theme.of(context).primaryColor,
                fontWeight: fontWeight ?? FontWeight.bold),
          ),
        ),
      ),
    );
  }
}
