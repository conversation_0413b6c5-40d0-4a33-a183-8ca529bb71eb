import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/events/verify_ticket.dart';
import 'package:onekitty/screens/dashboard/pages/events/qrcode_verifu.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/utils_exports.dart';
import '../../../../controllers/events/verify_ticket_controller.dart';

class VerifyTicket extends StatelessWidget {
  final int eventId;
  const VerifyTicket({super.key, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyTicketController());
    final ticketCodeController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Ticket'),
      ),
      body: Obx(
        () => PageView(
          controller: controller.pageController.value,
          children: [
            Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),
                    const Text(
                      'Enter Ticket Code',
                      style:
                          TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 20),
                    Obx(
                      () {
                        ticketCodeController.text = controller.prefix.value;
                        return TextField(
                          controller: ticketCodeController,
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              controller.prefix.value =
                                  ticketCodeController.text;
                            }
                          },
                          decoration: InputDecoration(
                            suffixIcon: IconButton(
                                onPressed: () {
                                  // clear input from where we have -
                                  if (ticketCodeController.text.isNotEmpty) {
                                    var input = controller.prefix;
                                    if (input.contains('-')) {
                                      var twoInputs = input.split("-");
                                      ticketCodeController.text =
                                          "${twoInputs[0]}-";
                                    }
                                  }
                                },
                                icon: const Icon(Icons.close_outlined)),
                            hintText: 'Enter code here',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 20),
                    Obx(() => MyButton(
                          showLoading: controller.isVerifying.value,
                          onClick: () {
                            controller.verifyTicket(
                              eventId,
                              ticketCodeController.text,
                            );
                          },
                          label: 'Verify',
                        )),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                            onTap: () {
                              if (controller.pageController.value.page == 0) {
                                controller.pageController.value.jumpToPage(1);
                              } else {
                                controller.pageController.value.jumpToPage(0);
                              }
                            },
                            child: Container(
                                margin: EdgeInsets.only(bottom: 10.h),
                                width: 150.w,
                                height: 60.h,
                                alignment: Alignment.centerRight,
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.navigate_before),
                                    Text('Swipe to scan ')
                                  ],
                                ))),
                      ],
                    )
                  ],
                ),
              ),
            ),
            QrVerifyTicket(eventId: eventId),
            // // Page 2: QR Code Scanner
            // Builder(builder: (context) {
            //   Future<bool> requestCameraPermission() async {
            //     PermissionStatus status = await Permission.camera.request();
            //     return status.isGranted;
            //   }

            //   return FutureBuilder<bool>(
            //     future: requestCameraPermission(),
            //     builder: (context, snapshot) {
            //       if (snapshot.data == true) {
            //         return BarcodeScanner(
            //           lineColor: "#ff6666",
            //           cancelButtonText: "Cancel",
            //           isShowFlashIcon: true,
            //           scanType: ScanType.barcode,
            //           // appBarTitle: appBarTitle,
            //           // centerTitle: centerTitle,
            //           // child: child,
            //           onScanned: (res) {},
            //         );
            //       } else {
            //         return const Center(
            //           child: Text(
            //               'Camera permission is required to scan QR codes.'),
            //         );
            //       }
            //     },
            //   );
            // }),
          ],
        ),
      ),
    );
  }
}

class VerifyConfirm extends StatelessWidget {
  final int eventId;
  final VerifyTicketConfirm verify;
  final String code;
  const VerifyConfirm(
      {super.key,
      required this.verify,
      required this.eventId,
      required this.code});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyTicketController());
    final tickets = verify.transaction?.transactionTicket ?? [];
    final selectedTickets = tickets.map((e) => e.id).toList().obs;
    return Scaffold(
      appBar: AppBar(title: const Text('Verify Ticket Confirm')),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${verify.transaction?.firstName} ${verify.transaction?.secondName}",
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              verify.transaction?.phoneNumber ?? '',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              verify.transaction?.transactionCode ?? '',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              ' ${formatDate(verify.transaction?.createdAt?.toLocal().toString() ?? '')}, ${DateFormat('hh:mm a').format(verify.transaction?.createdAt?.toLocal() ?? DateTime.now())}',
              style: const TextStyle(fontSize: 16),
            ),
            const Divider(),
            ListView.builder(
              shrinkWrap: true,
              itemCount: tickets.length,
              itemBuilder: (context, index) {
                final ticket = verify.transaction?.transactionTicket?[index];
                final RxBool selected = true.obs;
                return GestureDetector(
                  onTap: () {
                    selected.value = !selected.value;
                    if (selected.value) {
                      selectedTickets.add(ticket?.id ?? 0);
                    } else {
                      selectedTickets.remove(ticket?.id ?? 0);
                    }
                    print(selectedTickets);
                  },
                  child: Obx(
                    () => Container(
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                          color: selected.value
                              ? AppColors.primary.withOpacity(0.12)
                              : Colors.white,
                          border: Border.all(
                              color: selected.value
                                  ? AppColors.primary
                                  : Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(15)),
                      child: ListTile(
                        leading: Text(
                          "Qty: ${ticket?.quantity}",
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        trailing: Text(
                          FormattedCurrency
                              .getFormattedCurrency(ticket?.amount),
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        title: Text(
                          ticket?.ticket?.title ?? "",
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        subtitle: Text(ticket?.ticket?.description ?? ""),
                      ),
                    ),
                  ),
                );
              },
            ),
            SizedBox(
              height: 8.h,
            ),
            Align(
              alignment: Alignment.center,
              child: Obx(
                () => MyButton(
                  showLoading: controller.isConfirming.value,
                  onClick: () {
                    controller.verifyTicketConfirm(
                        eventId, code, selectedTickets);
                  },
                  label: 'Verify Ticket',
                  width: 210.w,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
