import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:onekitty/services/permission_service.dart';
import 'package:onekitty/services/media_upload_service.dart';
import 'package:onekitty/services/whatsapp_statement_service.dart';
import 'package:onekitty/services/kyc_service.dart';
import 'package:onekitty/controllers/events/event_statistics_controller.dart';
import 'package:onekitty/utils/crud_navigation_helper.dart';
import 'package:onekitty/widgets/transfer_charges_dialog.dart';

void main() {
  group('Comprehensive Feature Tests', () {
    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
    });

    tearDown(() {
      // Clean up after each test
      Get.reset();
    });

    group('Permission Service Tests', () {
      late PermissionService permissionService;

      setUp(() {
        permissionService = PermissionService();
        Get.put(permissionService);
      });

      testWidgets('should request contacts permission with explanation dialog', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) => Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    await permissionService.requestContactsPermission(
                      context: context,
                      customMessage: 'Test message for contacts access',
                    );
                  },
                  child: const Text('Request Permission'),
                ),
              ),
            ),
          ),
        );

        // Tap the button to trigger permission request
        await tester.tap(find.text('Request Permission'));
        await tester.pumpAndSettle();

        // Verify explanation dialog appears
        expect(find.text('Contacts Access'), findsOneWidget);
        expect(find.text('Test message for contacts access'), findsOneWidget);
        expect(find.text('Allow'), findsOneWidget);
        expect(find.text('Not Now'), findsOneWidget);
      });

      test('should track permission states correctly', () {
        expect(permissionService.contactsPermissionGranted, isFalse);
        expect(permissionService.cameraPermissionGranted, isFalse);
        expect(permissionService.storagePermissionGranted, isFalse);
      });
    });

    group('Media Upload Service Tests', () {
      late MediaUploadService uploadService;

      setUp(() {
        uploadService = MediaUploadService();
        Get.put(uploadService);
      });

      test('should add items to upload queue', () {
        final item = MediaUploadItem.fromUrl('https://example.com/image.jpg', MediaType.image);
        uploadService.addToUploadQueue(item);

        expect(uploadService.uploadQueue.length, equals(1));
        expect(uploadService.uploadQueue.first.mediaType, equals(MediaType.image));
      });

      test('should remove items from upload queue', () {
        final item = MediaUploadItem.fromUrl('https://example.com/image.jpg', MediaType.image);
        uploadService.addToUploadQueue(item);
        
        expect(uploadService.uploadQueue.length, equals(1));
        
        uploadService.removeFromUploadQueue(item.id);
        expect(uploadService.uploadQueue.length, equals(0));
      });

      test('should track upload progress correctly', () {
        expect(uploadService.isUploading.value, isFalse);
        expect(uploadService.overallProgress.value, equals(0.0));
        expect(uploadService.completedUploads.value, equals(0));
        expect(uploadService.failedUploads.value, equals(0));
      });

      test('should validate upload summary', () {
        final item1 = MediaUploadItem.fromUrl('https://example.com/image1.jpg', MediaType.image);
        final item2 = MediaUploadItem.fromUrl('https://example.com/image2.jpg', MediaType.video);
        
        uploadService.addToUploadQueue(item1);
        uploadService.addToUploadQueue(item2);

        final summary = uploadService.uploadSummary;
        expect(summary['total'], equals(2));
        expect(summary['pending'], equals(2));
        expect(summary['completed'], equals(0));
        expect(summary['failed'], equals(0));
      });
    });

    group('WhatsApp Statement Service Tests', () {
      late WhatsAppStatementService statementService;

      setUp(() {
        statementService = WhatsAppStatementService();
        Get.put(statementService);
      });

      test('should track request status', () {
        expect(statementService.isRequestingStatement.value, isFalse);
        expect(statementService.lastRequestStatus.value, isEmpty);
      });

      testWidgets('should show statement request dialog', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) => Scaffold(
                body: ElevatedButton(
                  onPressed: () {
                    statementService.showStatementRequestDialog(
                      context: context,
                      entityType: 'event',
                      entityId: 123,
                      defaultPhoneNumber: '+254712345678',
                    );
                  },
                  child: const Text('Show Dialog'),
                ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        expect(find.text('Request event Statement'), findsOneWidget);
        expect(find.text('WhatsApp Number'), findsOneWidget);
        expect(find.text('Statement Type'), findsOneWidget);
      });
    });

    group('KYC Debug Service Tests', () {
      late KYCDebugService debugService;

      setUp(() {
        debugService = KYCDebugService();
        Get.put(debugService);
      });

      test('should initialize with empty debug logs', () {
        expect(debugService.debugLogs.isEmpty, isTrue);
        expect(debugService.isDebugging.value, isFalse);
        expect(debugService.lastError.value, isEmpty);
      });

      test('should generate debug report', () {
        debugService.debugLogs.add('[2024-01-01T00:00:00] Test log entry');
        debugService.lastError.value = 'Test error';

        final report = debugService.getDebugReport();
        expect(report.contains('KYC Debug Report'), isTrue);
        expect(report.contains('Test log entry'), isTrue);
        expect(report.contains('Test error'), isTrue);
      });

      test('should clear debug data', () {
        debugService.debugLogs.add('Test log');
        debugService.lastError.value = 'Test error';
        debugService.isDebugging.value = true;

        debugService.clearDebugData();

        expect(debugService.debugLogs.isEmpty, isTrue);
        expect(debugService.lastError.value, isEmpty);
        expect(debugService.isDebugging.value, isFalse);
      });
    });

    group('Event Statistics Controller Tests', () {
      late EventStatisticsController statsController;

      setUp(() {
        statsController = EventStatisticsController();
        Get.put(statsController);
      });

      test('should initialize with default values', () {
        expect(statsController.isLoadingStatistics.value, isFalse);
        expect(statsController.totalTicketsSold.value, equals(0));
        expect(statsController.totalRevenue.value, equals(0.0));
        expect(statsController.hasError.value, isFalse);
      });

      test('should calculate performance metrics correctly', () {
        // Mock some data
        statsController.ticketSalesByCategory.value = [
          {'ticket_type': 'VIP', 'tickets_sold': 50, 'total_tickets': 100},
          {'ticket_type': 'Regular', 'tickets_sold': 80, 'total_tickets': 200},
        ];
        statsController.totalTicketsSold.value = 130;
        statsController.averageTicketPrice.value = 25.0;

        final metrics = statsController.getPerformanceMetrics();
        expect(metrics['total_capacity'], equals(300));
        expect(metrics['tickets_remaining'], equals(170));
        expect(metrics['average_ticket_price'], equals(25.0));
      });

      test('should get top performing tickets', () {
        statsController.ticketSalesByCategory.value = [
          {'ticket_type': 'VIP', 'tickets_sold': 50},
          {'ticket_type': 'Regular', 'tickets_sold': 80},
          {'ticket_type': 'Premium', 'tickets_sold': 30},
        ];

        final topTickets = statsController.getTopPerformingTickets(limit: 2);
        expect(topTickets.length, equals(2));
        expect(topTickets.first['ticket_type'], equals('Regular'));
        expect(topTickets.last['ticket_type'], equals('VIP'));
      });
    });

    group('CRUD Navigation Helper Tests', () {
      testWidgets('should show success message and navigate', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) => Scaffold(
                body: ElevatedButton(
                  onPressed: () {
                    CrudNavigationHelper.handleSuccessfulCrudOperation(
                      context: context,
                      operation: 'create',
                      entityType: 'event',
                      successMessage: 'Event created successfully!',
                    );
                  },
                  child: const Text('Test Success'),
                ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Test Success'));
        await tester.pumpAndSettle();

        // Note: In a real test, you'd mock the navigation and toast services
        // This is a basic structure test
      });

      testWidgets('should show discard changes dialog', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) => Scaffold(
                body: ElevatedButton(
                  onPressed: () {
                    CrudNavigationHelper.showDiscardChangesDialog(
                      context: context,
                    );
                  },
                  child: const Text('Show Discard Dialog'),
                ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Discard Dialog'));
        await tester.pumpAndSettle();

        expect(find.text('Discard Changes?'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Discard'), findsOneWidget);
      });
    });

    group('Transfer Charges Dialog Tests', () {
      testWidgets('should display transfer charges correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: TransferChargesDialog(
              amount: 1000.0,
              platformFee: 30.0,
              transactionCharge: 50.0,
              thirdPartyFee: 20.0,
              totalCharges: 100.0,
              finalAmount: 900.0,
              remainingBalance: 5000.0,
              recipientName: 'John Doe',
              recipientAccount: '**********',
              transferMode: 'M-PESA',
              reason: 'Payment',
              onConfirm: () {},
              onCancel: () {},
            ),
          ),
        );

        expect(find.text('Transfer Confirmation'), findsOneWidget);
        expect(find.text('John Doe'), findsOneWidget);
        expect(find.text('**********'), findsOneWidget);
        expect(find.text('M-PESA'), findsOneWidget);
        expect(find.text('Payment'), findsOneWidget);
        expect(find.text('Confirm Transfer'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });
    });

    group('Integration Tests', () {
      testWidgets('should handle complete workflow', (tester) async {
        // Initialize all services
        final permissionService = PermissionService();
        final uploadService = MediaUploadService();
        final statementService = WhatsAppStatementService();
        final debugService = KYCDebugService();

        Get.put(permissionService);
        Get.put(uploadService);
        Get.put(statementService);
        Get.put(debugService);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  ElevatedButton(
                    onPressed: () {
                      // Simulate a complete workflow
                      final item = MediaUploadItem.fromUrl(
                        'https://example.com/test.jpg',
                        MediaType.image,
                      );
                      uploadService.addToUploadQueue(item);
                    },
                    child: const Text('Add Upload'),
                  ),
                  Obx(() => Text('Queue: ${uploadService.uploadQueue.length}')),
                ],
              ),
            ),
          ),
        );

        await tester.tap(find.text('Add Upload'));
        await tester.pumpAndSettle();

        expect(find.text('Queue: 1'), findsOneWidget);
      });
    });
  });
}

/// Note: Mock classes would require mockito package
/// For now, tests use real service instances in test mode
