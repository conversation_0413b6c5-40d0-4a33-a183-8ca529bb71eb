import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/main.dart';
import 'package:onekitty/utils/my_button.dart';

import '../controllers/events/controllers.dart';
import '../controllers/events/edit_event_controller.dart';
import '../models/events/categories_model.dart';

class SearchBarWidget extends StatelessWidget {
  final int page;
  const SearchBarWidget({
    super.key,
    required this.page,
  });

  @override
  Widget build(BuildContext context) {
    RxBool isSearching = false.obs;

    final _controller = Get.put(Eventcontroller());
    Timer? _debounce;
    return Builder(builder: (context) {
      final TextEditingController textController =
          TextEditingController(text: _controller.search.value);
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Obx(() => Container(
                  height: 45.h,
                  margin: EdgeInsets.all(4.spMin),
                  alignment: Alignment.bottomCenter,
                  // constraints: BoxConstraints(
                  //   maxWidth: 245.w,
                  // ),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(15)),
                  child: TextField(
                    controller: textController,
                    style: TextStyle(fontSize: 12.spMin),
                    onChanged: (val) {
                      // Cancel the previous timer if it's still active
                      if (_debounce?.isActive ?? false) {
                        _debounce!.cancel();
                      }

                      // Set a new timer to execute the function after 1 second of inactivity
                      _debounce = Timer(const Duration(seconds: 1), () async {
                        // Trigger the searching state
                        isSearching(true);

                        // Perform the search operation
                        _controller.search(textController.text);

                        // Fetch events or user events based on the page value
                        if (page == 0) {
                          _controller.resetEvents();
                          await _controller.fetchEvents().whenComplete(() {
                            isSearching(false);
                          }).onError((e, s) => isSearching(false));
                        } else {
                          _controller.resetUserEvents();
                          await _controller.fetchUserEvents().whenComplete(() {
                            isSearching(false);
                          }).onError((e, s) => isSearching(false));
                        }
                      });
                    },
                    decoration: InputDecoration(
                        hintText: ' Search',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide:
                              const BorderSide(color: primaryColor, width: 0.4),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide:
                              const BorderSide(color: primaryColor, width: 0.4),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide:
                              const BorderSide(color: primaryColor, width: 0.4),
                        ),
                        hintStyle: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 12.spMin,
                        ),
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: isSearching.value
                            ? SizedBox(
                                height: 12.spMin,
                                width: 12.spMin,
                                child: const CupertinoActivityIndicator(),
                              )
                            : null),
                  ),
                )),
          ),
          SizedBox(width: 12.w),
          OutlinedButton.icon(
            onPressed: () async {
              showDialog<Map<String, dynamic>>(
                  context: context,
                  builder: (BuildContext context) => FilterDialog(page: page));
            },
            label: Text(
              'Filter',
              style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16.spMin),
            ),
            icon: const Icon(Icons.filter_alt_outlined),
          )
        ],
      );
    });
  }
}

class AttendeesWidget extends StatelessWidget {
  final double? padding, size, textSize;
  final int count;
  const AttendeesWidget(
      {super.key, this.padding, this.size, this.textSize, required this.count});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(padding ?? 8.0),
      child: Row(
        children: [
          SizedBox(
            width: size != null ? size! * 4 + 8 : 105,
            child: Stack(
              children: [
                Positioned(
                  child: CircleAvatar(
                    radius: size ?? 25,
                    backgroundColor: Colors.purple[300],
                    child: const Icon(Icons.person),
                  ),
                ),
                Positioned(
                  left: size ?? 25,
                  child: CircleAvatar(
                    radius: size ?? 25,
                    child: const Icon(Icons.person),
                  ),
                ),
                Positioned(
                  left: size != null ? size! * 2 : 50,
                  child: CircleAvatar(
                    radius: size ?? 25,
                    backgroundColor: const Color(0xff4355b6),
                    child: Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: FittedBox(
                        child: Text(
                          '+$count',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Text(
            'attendees',
            style: TextStyle(
                fontSize: textSize ?? 18,
                fontWeight: FontWeight.w500,
                color: const Color(0xff4355b6)),
          ),
        ],
      ),
    );
  }
}

class TabButtonWidget extends StatelessWidget {
  final bool? active;
  final String label;
  final Function()? onTap;
  const TabButtonWidget(
      {super.key, this.active, required this.label, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: active ?? false
              ? primaryColor.withOpacity(0.15)
              : isLight.value
                  ? const Color(0xfff9f9f9)
                  : null,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: active ?? false ? primaryColor : Colors.grey,
            width: 1.0,
          ),
        ),
        child: active ?? false
            ? Row(
                children: [
                  const Icon(Icons.circle, size: 12, color: Color(0xff4355b6)),
                  const SizedBox(width: 6),
                  Text(label,
                      style: TextStyle(
                        color: const Color(0xff4355b6),
                        fontSize: 16.spMin,
                        fontWeight: FontWeight.w600,
                      )),
                ],
              )
            : Text(label,
                style: TextStyle(
                  fontSize: 16.spMin,
                  fontWeight: FontWeight.w600,
                )),
      ),
    );
  }
}

class FilterDialog extends StatelessWidget {
  final int page;
  const FilterDialog({super.key, required this.page});

  @override
  Widget build(BuildContext context) {
    final Eventcontroller _controller = Get.put(Eventcontroller());
    final startDate = ''.obs;
    final endDate = ''.obs;
    RxString selectedStatus = "ACTIVE".obs;
    final selectedCategory = Rxn<CategoriesModel>();
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 700,
        ),
        width: 350.w,
        // height: 350.h,
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text('Category:',
                  style: TextStyle(
                      fontSize: 16.spMin, fontWeight: FontWeight.w600)),
            ),
            SizedBox(height: 8.h),
            GetX<EditEventController>(
              initState: (state) async {
                await state.controller?.getCategories();
              },
              builder: (controller) {
                if (controller.isLoadingCategories.isTrue) {
                  return Container(
                    height: 55.h,
                    padding: const EdgeInsets.all(8),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: isLight.value ? Colors.white70 : null,
                      border: Border.all(color: Colors.grey, width: 0.5),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Select Category'),
                        CupertinoActivityIndicator()
                      ],
                    ),
                  );
                }

                return Obx(() => DropdownButtonFormField<CategoriesModel>(
                      decoration: InputDecoration(
                        hintText: "Select Category",
                        //filled: true,
                        // fillColor: Colors.white70,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: const BorderSide(
                            width: 0.5,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      value: selectedCategory.value,
                      items: controller.categories.map((category) {
                        return DropdownMenuItem<CategoriesModel>(
                          value: category,
                          child: Text(category.title ?? ''),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          selectedCategory.value = value;
                          controller.category.value = value.id!;
                        }
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Category is required';
                        }
                        return null;
                      },
                    ));
              },
            ),
            SizedBox(height: 16.h),
            Align(
              alignment: Alignment.centerLeft,
              child: Text('Date Range:',
                  style: TextStyle(
                      fontSize: 16.spMin, fontWeight: FontWeight.w600)),
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey)),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () async {
                      DateTimeRange? picked = await showDateRangePicker(
                        context: context,
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2101),
                      );
                      if (picked != null) {
                        startDate.value = picked.start.toString().split(' ')[0];
                        endDate.value = picked.end.toString().split(' ')[0];
                      }
                    },
                    child: ColoredBox(
                        color: primaryColor.withOpacity(0.4),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            children: [
                              const Icon(Icons.date_range),
                              SizedBox(width: 8.h),
                              Text('Pick',
                                  style: TextStyle(
                                      fontSize: 16.spMin,
                                      fontWeight: FontWeight.w600))
                            ],
                          ),
                        )),
                  ),
                  SizedBox(width: 8.h),
                  Obx(() => Text(
                      startDate.value.isNotEmpty && endDate.value.isNotEmpty
                          ? '${startDate.value} - ${endDate.value}'
                          : "pick a date range",
                      style: TextStyle(
                          fontSize: 16.spMin, fontWeight: FontWeight.w600))),
                ],
              ),
            ),
            SizedBox(height: 30.h),
            if (page == 1)
              Align(
                alignment: Alignment.centerLeft,
                child: Text('Event Status:',
                    style: TextStyle(
                        fontSize: 16.spMin, fontWeight: FontWeight.w600)),
              ),
            if (page == 1)
              Obx(() {
                return Wrap(
                  children: [
                    for (String status in Get.find<GlobalControllers>()
                        .enums
                        .value
                        .eventStatus)
                      Padding(
                        padding: const EdgeInsets.all(4),
                        child: ChoiceChip(
                          label: Text(status),
                          selected: selectedStatus.value == status,
                          onSelected: (bool selected) {
                            if (selected) {
                              selectedStatus.value = status;
                            }
                          },
                          backgroundColor: primaryColor.withOpacity(0.2),
                          selectedColor: primaryColor,
                          labelStyle: TextStyle(
                            color: selectedStatus.value == status
                                ? Colors.white
                                : primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(color: primaryColor),
                            borderRadius: BorderRadius.circular(25.r),
                          ),
                        ),
                      ),
                  ],
                );
              }),
            SizedBox(height: 16.h),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel',
                        style: TextStyle(color: Colors.grey)),
                  ),
                  Obx(
                    () => MyButton(
                      showLoading: _controller.isApplyingFilters.value,
                      onClick: () async {
                        _controller.isApplyingFilters(true);
                        _controller.resetEvents();
                        _controller.filterCategory?.value =
                            (selectedCategory.value?.id);
                        _controller.startDate(startDate.value.isNotEmpty
                            ? DateTime.parse(startDate.value)
                                .toUtc()
                                .toIso8601String()
                            : "");
                        _controller.endDate(endDate.value.isNotEmpty
                            ? DateTime.parse(endDate.value)
                                .toUtc()
                                .toIso8601String()
                            : "");
                        if (page == 0) {
                          _controller.status(selectedStatus.value);
                          await _controller.fetchUserEvents().whenComplete(() {
                            _controller.isApplyingFilters(false);
                            Navigator.pop(context);
                          });
                        } else {
                          _controller.status("ACTIVE");
                          await _controller.fetchEvents().whenComplete(() {
                            _controller.isApplyingFilters(false);
                            Navigator.pop(context);
                          });
                        }
                      },
                      label: 'Apply',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
