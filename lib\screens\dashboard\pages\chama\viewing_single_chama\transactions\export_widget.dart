import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/statement.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/excel/excel_func.dart';
import 'package:onekitty/services/share_whatsapp_service.dart';
import 'package:onekitty/services/whatsapp_statement_export_service.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../../models/chama/get_text_transaction_request.dart'; 

// ignore: must_be_immutable
class ExportChamaContentWidget extends StatefulWidget {
  final TransactionModel? details;
  final UserChama? chama;
  bool singleTrans;

  ExportChamaContentWidget({
    super.key,
    this.details,
    this.chama,
    required this.singleTrans,
  });

  @override
  State<ExportChamaContentWidget> createState() =>
      _ExportChamaContentWidgetState();
}

class _ExportChamaContentWidgetState extends State<ExportChamaContentWidget> {
  ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  String shareMsg = "";

  Future<void> fetchMessage() async {
    ChamaTextTransactions request = ChamaTextTransactions(
        action: "TEXT", chamaId: chamaDataController.chama.value.chama?.id);
    final res = await chamaController.postTransactionText(requst: request);
    if (res) {
      if (mounted) {
        setState(() {
          shareMsg = chamaController.transMessage.toString();
        });
      }
    }
  }


  @override
  void initState() {
    super.initState();
    fetchMessage();
  }

@override
Widget build(BuildContext context) {
  final DateFormat format = DateFormat.MMMEd().add_jms();
  DateTime createdAt = widget.details?.createdAt ?? DateTime.now();
  
  return Container(
    width: double.maxFinite,
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
    ),
    child: SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.symmetric(vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          
          // Header with close button
          Padding(
            padding: EdgeInsets.fromLTRB(24.w, 8.h, 16.w, 24.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Export Options",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  borderRadius: BorderRadius.circular(20.r),
                  child: Container(
                    padding: EdgeInsets.all(8.w),
                    child: Icon(
                      Icons.close,
                      size: 20.w,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ),
              ],
            ),
          ),
      
          // Export options
          Padding(
            padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 32.h),
            child: Column(
              children: [
                // Generate Statement
                _buildExportOption(
                  context: context,
                  icon: Icons.receipt_long_outlined,
                  title: "Generate Statement",
                  subtitle: "Create detailed financial statement",
                  onTap: () async {
                    final whatsappService = Get.put(WhatsAppStatementExportService());
                    final chamaId = chamaDataController.chama.value.chama?.id;
                    final chamaTitle = chamaDataController.chama.value.chama?.title;
                    final kittyId = chamaDataController.chama.value.chama?.kittyId;
                    if (chamaId != null) {
                      await whatsappService.showWhatsAppChamaStatementDialog(
                        context: context,
                        kittyId: kittyId,
                        chamaTitle: chamaTitle,
                      );
                    }
                  },
                ),
                
                SizedBox(height: 16.h),
      
                // Export to PDF
                _buildExportOption(
                  context: context,
                  icon: Icons.picture_as_pdf_outlined,
                  title: "Export to PDF",
                  subtitle: "Download as PDF document",
                  onTap: () {
                    if (widget.singleTrans) {
                      Get.to(() => SingleChamaStatementPage(
                            transaction: widget.details,
                            chama: widget.chama,
                          ));
                    } else {
                      Get.to(() => ChamaStatementPage(
                            chama: widget.chama,
                            transactions: chamaController.chamaTransactions,
                          ));
                    }
                  },
                ),
      
                // Export to Excel (only for multiple transactions)
                if (!widget.singleTrans) ...[
                  SizedBox(height: 16.h),
                  _buildExportOption(
                    context: context,
                    icon: Icons.table_chart_outlined,
                    title: "Export to Excel",
                    subtitle: "Download as spreadsheet file",
                    onTap: () async {
                      String filePath = await createExcel(isKitty: false);
                      _showFileOptionsBottomSheet(context, filePath);
                    },
                  ),
                ],
      
                SizedBox(height: 16.h),
      
                // Export to Text
                _buildExportOption(
                  context: context,
                  icon: Icons.text_snippet_outlined,
                  title: "Export to Text",
                  subtitle: "Share as plain text message",
                  onTap: () async {
                    if (widget.singleTrans) {
                      String shareMsg = _buildSingleTransactionMessage(format, createdAt);
                      await Share.share(shareMsg, subject: 'Transaction details');
                    } else {
                      try {
                        await Share.share("${chamaController.transMessage}");
                      } catch (e) {}
                    }
                  },
                ),
      
                SizedBox(height: 16.h),
      
                // WhatsApp message
                _buildExportOption(
                  context: context,
                  icon: Icons.chat_outlined,
                  title: "WhatsApp Message",
                  subtitle: "Share directly via WhatsApp",
                  onTap: () async {
                    Get.back();
                    if (widget.singleTrans) {
                      String shareMsg = _buildSingleTransactionMessage(format, createdAt);
                      await ShareWhatsapp.share(shareMsg);
                    } else {
                      try {
                        await ShareWhatsapp.share("${chamaController.transMessage}");
                      } catch (e) {}
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildExportOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}

String _buildSingleTransactionMessage(DateFormat format, DateTime createdAt) {
  return "Chama Title: ${chamaDataController.chama.value.chama?.title}\n"
         "Phone Number: ${widget.details?.phoneNumber}\n"
         "Amount: KSH ${widget.details?.amount}\n"
         "Name: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\n"
         "Transaction Code: ${widget.details?.transactionCode}\n"
         "Date: ${format.format(createdAt.toLocal())}\n"
         "Chama: https://onekitty.co.ke/chama/${chamaDataController.chama.value.chama?.id}";
}

void _showFileOptionsBottomSheet(BuildContext context, String filePath) {
  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Text(
              "File Options",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 24.h),
            _buildFileOption(
              context: context,
              icon: Icons.file_open,
              title: "Open File",
              onTap: () async {
                try {
                  final result = await OpenFile.open(filePath);
                  if (result.type != ResultType.done) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error opening file: ${result.message}'),
                        backgroundColor: Theme.of(context).colorScheme.error,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error opening file: $e'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
                Navigator.pop(context);
              },
            ),
            SizedBox(height: 12.h),
            _buildFileOption(
              context: context,
              icon: Icons.share,
              title: "Share File",
              onTap: () {
                Navigator.pop(context);
                Share.shareXFiles([XFile(filePath)]);
              },
            ),
            SizedBox(height: 16.h),
          ],
        ),
      );
    },
  );
}

Widget _buildFileOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}
}
