import 'dart:async';
import 'dart:io';
import 'package:app_links/app_links.dart';
import 'package:connectivity_checker/connectivity_checker.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/onboarding/login_screen.dart';
import 'package:onekitty/screens/onboarding/splash_Screens/splash_screen.dart';
import 'package:onekitty/services/auth_manager.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/services/firebase_services.dart';
import 'package:onekitty/services/init_service.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/utils/image_cache_optimizer.dart';
import 'package:onekitty/utils/request_cache.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:onekitty/services/error_logging_service.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:url_strategy/url_strategy.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'firebase_options.dart'; 
import 'package:firebase_auth/firebase_auth.dart'; 
import 'utils/cache_keys.dart';

// Firebase Background Message Handler
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  Snack.showInfo(
    message1: message.notification?.title ?? 'Success',
    message2: message.notification?.body ?? "",
  );
}

// Light/Dark Mode Notifier
final isLight = ValueNotifier(true);

void main() async {

  // Ensure Flutter binding is initialized first
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  // FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Initialize Firebase options with timeout to prevent indefinite waiting
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  ).timeout(const Duration(seconds: 10), onTimeout: () {
    throw TimeoutException('Firebase initialization timed out');
  });

  // Use a safer approach for auth state with timeout
  await FirebaseAuth.instance.authStateChanges().first.timeout(
      const Duration(seconds: 5),
      onTimeout: () => null); // Return null instead of throwing

  await ImageCacheOptimizer.optimizeImageCache();
  await RequestCache.init();

  setPathUrlStrategy();
  HttpOverrides.global = MyHttpOverrides();

  await servicesInitialize();
  await GetStorage.init();
  
  // Initialize error logging
  await ErrorLoggingService.initialize();

  // Crashlytics Error Handling
  final logger = Logger(
    printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true),
  );

  FlutterError.onError = (details) {
    if (kDebugMode) {
      // In debug mode, use logger
      logger.e('FlutterError',
          error: details.exception, stackTrace: details.stack);
    } else {
      // In release mode, send to Crashlytics
      try {
        FirebaseCrashlytics.instance.recordFlutterFatalError(details);
      } catch (e) {
        // Fallback if Crashlytics fails
        debugPrint('Failed to report to Crashlytics: $e');
      }
    }
  };

  PlatformDispatcher.instance.onError = (error, stack) {
    if (kDebugMode) {
      // In debug mode, use logger
      logger.e('PlatformDispatcher error', error: error, stackTrace: stack);
    } else {
      // In release mode, send to Crashlytics using an isolate with fallback
      try {
        reportErrorToCrashlytics(
            {'error': error, 'stack': stack, 'fatal': true});
      } catch (e) {
        // Fallback if reporting fails
        debugPrint('Failed to report error: $e');
      }
    }
    return true; // Return true to prevent the error from propagating
  };

  // Firebase Messaging Setup
  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  final pushNotificationService = PushNotificationService(firebaseMessaging);
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  FirebaseMessaging.onMessageOpenedApp
      .listen(_firebaseMessagingBackgroundHandler);
  await pushNotificationService.setupInteractedMessage();

  // App Links Initialization
  final appLinks = AppLinks();
  final initialUri = await appLinks.getInitialLink();

  runApp(
    MyMaterialAppSuper(
      appLinks: appLinks,
      initialUri: initialUri,
    ),
  );
}

// HTTP Overrides for Certificate Handling
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyMaterialAppSuper extends StatefulWidget {
  final Uri? initialUri;
  final AppLinks appLinks;

  const MyMaterialAppSuper({
    super.key,
    required this.initialUri,
    required this.appLinks,
  });

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_MyMaterialAppSuperState>()?.restartApp();
  }

  @override
  State<MyMaterialAppSuper> createState() => _MyMaterialAppSuperState();
}

class _MyMaterialAppSuperState extends State<MyMaterialAppSuper> {
  final _navigatorKey = GlobalKey<NavigatorState>();
  final _authenticationManager = Get.put(AuthenticationManager());

  StreamSubscription<Uri?>? _deepLinkSubscription;

  Key appKey = UniqueKey();
  Timer? _timer;

  void restartApp() {
    setState(() {
      appKey = UniqueKey();
    });
  }

  @override
  void initState() {
    super.initState();
 
    _authenticationManager.checkLoginStatus();
    if (_authenticationManager.isLogged.isTrue) {
      _initializeTimer();
    }
     // Status Bar and Navigation Bar Styling
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: AppColors.primary,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.primary,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    // Cancel timer to prevent memory leaks
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  void _handleUserInteraction([_]) {
    // Skip timer reset if deep link is in progress
    // if (!_deepLinkManager.isDeepLinkInProgress &&
    if (_authenticationManager.isLogged.isTrue) {
      _initializeTimer();
    }
  }

  void _initializeTimer() {
    // Only create new timer if the widget is still mounted
    if (!mounted) return;

    _timer?.cancel();
    _timer = Timer(const Duration(minutes: 5), _logOutUser);
  }

  void _logOutUser() async {
    _timer?.cancel();
    _timer = null;

    if (kDebugMode) return;

    // Check if widget is still mounted before proceeding
    if (!mounted) return;

    // Skip authentication if deep link is in progress
    // if (!_deepLinkManager.isDeepLinkInProgress &&
    if (_authenticationManager.isLogged.isTrue) {
      var success = await Get.to(() => AuthPasswdScreen(), arguments: [false]);

      // Check again if still mounted after async operation
      if (mounted && success == true) {
        _initializeTimer();
      }
    }
  }

  final box = GetStorage();

  @override
  Widget build(BuildContext context) {
    isLight.value = box.read(CacheKeys.isLight) ?? true;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _handleUserInteraction,
      onPanDown: _handleUserInteraction,
      onPanUpdate: _handleUserInteraction,
      child: ConnectivityAppWrapper(
        app: ValueListenableBuilder<bool>(
          valueListenable: isLight,
          builder: (context, isLightMode, _) {
            return GetMaterialApp(
              key: appKey,
              
              navigatorKey: _navigatorKey,
              debugShowCheckedModeBanner: false,
              title: 'onekitty',
              navigatorObservers: <NavigatorObserver>[AnalyticsEngine.observer],
              themeMode: isLightMode ? ThemeMode.light : ThemeMode.dark,
              darkTheme: EbonyClayTheme().darkTheme,
              theme: EbonyClayTheme().lightTheme,
              localizationsDelegates: const [
                FlutterQuillLocalizations.delegate,
                // GlobalMaterialLocalizations.delegate,
                // GlobalWidgetsLocalizations.delegate,
                // GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [Locale('en')],
              home: const SplashScreen(),
              builder: EasyLoading.init(
                builder: (context, child) {
                  return ResponsiveBreakpoints.builder(
                    child: child!,
                    breakpoints: const [
                      Breakpoint(start: 0, end: 450, name: MOBILE),
                      Breakpoint(start: 451, end: 800, name: TABLET),
                      Breakpoint(start: 801, end: 1920, name: DESKTOP),
                      Breakpoint(start: 1921, end: double.infinity, name: '4K'),
                    ],
                  );
                },
              ),
              initialRoute: NavRoutes.splashScreen,
              getPages: [
                GetPage(
                  name: '/',
                  page: () => const SplashScreen(),
                ),
                GetPage(
                  name: '/loginPage',
                  page: () => const LoginScreen(),
                ),
                GetPage(
                  name: '/passreqPage',
                  page: () => AuthPasswdScreen(),
                ),
                GetPage(
                  name: '/bottom_navigation',
                  page: () => BottomNavSection(),
                ),
                ...NavRoutes.routes.entries.map(
                  (entry) => GetPage(
                    name: entry.key,
                    page: () => entry.value(context),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

// Functions for isolate-based error reporting
Future<void> reportErrorToCrashlytics(Map<String, dynamic> errorData) async {
  await compute(_reportErrorInIsolate, errorData);
}

void _reportErrorInIsolate(Map<String, dynamic> errorData) {
  final error = errorData['error'];
  final StackTrace stack = errorData['stack'];
  final bool fatal = errorData['fatal'] ?? false;

  FirebaseCrashlytics.instance.recordError(error, stack, fatal: fatal);
}

 