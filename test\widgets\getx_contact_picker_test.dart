import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:onekitty/widgets/getx_contact_picker.dart';
import 'package:onekitty/controllers/contact_picker_controller.dart';

void main() {
  group('GetXContactPicker Tests', () {
    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('should display contact picker in dialog mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: GetXContactPicker(
              mode: ContactPickerMode.single,
              display: ContactPickerDisplay.dialog,
              title: 'Test Contact Picker',
            ),
          ),
        ),
      );

      expect(find.text('Test Contact Picker'), findsOneWidget);
      expect(find.byType(GetXContactPicker), findsOneWidget);
    });

    testWidgets('should display contact picker in full screen mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: GetXContactPicker(
            mode: ContactPickerMode.multiple,
            display: ContactPickerDisplay.fullScreen,
            title: 'Full Screen Picker',
          ),
        ),
      );

      expect(find.text('Full Screen Picker'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should show selected contacts header in multiple mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: GetXContactPicker(
              mode: ContactPickerMode.multiple,
              display: ContactPickerDisplay.dialog,
              showSelectedContactsHeader: true,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // The selected contacts header should be present but empty initially
      expect(find.byType(GetXContactPicker), findsOneWidget);
    });

    test('ContactPickerController should manage selected contacts', () {
      final controller = ContactPickerController();
      
      final contact = Contact(
        id: '1',
        displayName: 'Test User',
      );

      // Test selecting a contact
      controller.selectContact(contact);
      expect(controller.selectedContacts.length, 1);
      expect(controller.isSelected(contact), true);

      // Test removing a contact
      controller.removeContact(contact);
      expect(controller.selectedContacts.length, 0);
      expect(controller.isSelected(contact), false);

      // Test clearing selection
      controller.selectContact(contact);
      controller.clearSelection();
      expect(controller.selectedContacts.length, 0);
    });
  });
}
