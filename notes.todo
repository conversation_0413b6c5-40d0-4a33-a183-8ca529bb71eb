onekitty:
    ☐ on withdraw  - before confirm, add a field  to show the kitty balance
    ☐ update social media pages links , instagram, threads
    ☐ Have a notification page - the notifications come from backend
    ☐ notification navigation
    ☐ Clearer show the pdf ,whatsapp,buttons

Kitty Page:
    ☐ Share the kitty,
    ✔ Add configuration to add the settings in which the contribution list will appear, @done(24-07-07 13:10)
                                   ✔  I can choose to only send transactions with a start and end date @done(24-07-07 13:10)
                                   ✔  Sort with the highest/lowest amount @done(24-07-07 13:10)
    ☐ Have a charity Kitty where users can tip the organisation which can be used for humanitarian activities
   

Profile Page:
    ☐ Savings statistics
    ☐ Next of Kin
    ☐ Whatsapp direct Chat - Follows ussd
    ☐ Deactivate account/ delete account
    ☐ show balances in chama transfer page
    

Settings:
    ☐ Dark mode

chama:
    ☐ Next Beneficiary loads twice

    OneKitty makes Fundraising  Easy, Transparent, and Engaging!

    With onekitty Say goodbye to outdated fundraising methods!
    
    We revolutionize the way you raise funds for your projects, causes, and dreams.
    
    Create a kitty and effortlessly share the kitty link across your network.
    
    Get real time updates of the contributions made through the chosen notification platform.
    
    Contributors have the freedom to send money from various supported channel on onekitty and also send money anonymously.
    
    You can manage your kitty from the app and generate a report of all the transactions.
    
    When the end date arrives, all funds are automatically sent to your chosen beneficiary via the chosed payment channel. No hassle, no worries!
    
    The money is sent through all the supported mobile wallets channels and banks.
