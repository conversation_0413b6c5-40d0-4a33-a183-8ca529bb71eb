# Gradle memory settings
# org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.jvmargs=-Xmx4g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

android.useAndroidX=true
android.enableJetifier=true
# org.gradle.java.home=/home/<USER>/android-studio/jbr

# R8 memory settings
android.enableR8.fullMode=true
android.proguard.enableR8=true
android.enableR8=true

# Network timeout settings
systemProp.org.gradle.internal.http.connectionTimeout=300000
systemProp.org.gradle.internal.http.socketTimeout=300000
org.gradle.daemon.idletimeout=600000