// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/helpers/colors.dart';

class PaymentChannelsBuilder extends StatefulWidget {
  PaymentChannelsBuilder({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  String selectedChannel;
  Function(String?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<PaymentChannelsBuilder> createState() => _PaymentChannelsBuilderState();
}

class _PaymentChannelsBuilderState extends State<PaymentChannelsBuilder> {
  final List channels = ["M-Pesa", "Sasapay", "Airtel Money"];
  final List images = [
    "assets/images/mpesa.png",
    "assets/images/sasapay.png",
    "assets/images/airtel-money.png",
    //"assets/images/credit_card.png",
    //"assets/images/img_rectangle_25.png",
  ];

  @override
  Widget build(BuildContext context) {
    return Wrap(
      alignment: WrapAlignment.spaceEvenly,
      children: List.generate(
        channels.length,
        (index) {
          if (index % widget.itemsPerRow == 0) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                widget.itemsPerRow,
                (innerIndex) {
                  final channelIndex = index + innerIndex;
                  if (channelIndex >= channels.length) {
                    return const SizedBox.shrink();
                  }
                  return _buildChannelItem(channelIndex);
                },
              ),
            );
          }
          return const SizedBox.shrink(); // Empty placeholder for padding
        },
      ),
    );
  }

  Widget _buildChannelItem(int index) {
    return Tooltip(
      message: channels[index].toString(),
      child: Column(
        children: [
          Radio<String>(
            value: channels[index],
            groupValue: widget.selectedChannel,
            onChanged: (value) {
              setState(() {
                widget.selectedChannel = value ?? "none";
              });
              widget.onChange!(value);
            },
          ),
          Image.asset(
            images[index],
            height: 45.spMin,
            width: 60.spMin,
          ),
        ],
      ),
    );
  }
}

class PaymentChannelsBuilder2 extends StatefulWidget {
  PaymentChannelsBuilder2({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  String selectedChannel;
  Function(String?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<PaymentChannelsBuilder2> createState() =>
      _PaymentChannelsBuilder2State();
}

class _PaymentChannelsBuilder2State extends State<PaymentChannelsBuilder2> {
  final List channels = ["M-Pesa", "Sasapay", "Airtel Money"];
  final List images = [
    "assets/images/mpesa.png",
    "assets/images/sasapay.png",
    "assets/images/airtel-money.png",
    //"assets/images/img_rectangle_25.png",
  ];

  @override
  Widget build(BuildContext context) {
    return Wrap(
      alignment: WrapAlignment.spaceEvenly,
      children: List.generate(
        channels.length,
        (index) {
          if (index % widget.itemsPerRow == 0) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                widget.itemsPerRow,
                (innerIndex) {
                  final channelIndex = index + innerIndex;
                  if (channelIndex >= channels.length) {
                    return const SizedBox.shrink();
                  }
                  return _buildChannelItem(channelIndex);
                },
              ),
            );
          }
          return const SizedBox.shrink(); // Empty placeholder for padding
        },
      ),
    );
  }

  Widget _buildChannelItem(int index) {
    return Tooltip(
      message: channels[index].toString(),
      child: Column(
        children: [
          Radio<String>(
            value: channels[index],
            groupValue: widget.selectedChannel,
            onChanged: (value) {
              setState(() {
                widget.selectedChannel = value ?? "none";
              });
              widget.onChange!(value);
            },
          ),
          Image.asset(
            images[index],
            height: 45.spMin,
            width: 60.spMin,
          ),
        ],
      ),
    );
  }
}

class ContributeChannelsBuilder extends StatefulWidget {
  ContributeChannelsBuilder({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  String selectedChannel;
  Function(String?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<ContributeChannelsBuilder> createState() =>
      _ContributeChannelsBuilderState();
}

class _ContributeChannelsBuilderState extends State<ContributeChannelsBuilder> {
  final List channels = ["M-Pesa", /*"Sasapay",*/ "Airtel Money", "Visa"];
  final List images = [
    "assets/images/mpesa.png",
    // "assets/images/sasapay.png",
    "assets/images/airtel-money.png",
    "assets/images/credit_card.png",
    //"assets/images/img_rectangle_25.png",
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      hitTestBehavior: HitTestBehavior.translucent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(
          channels.length,
          (index) {
            if (index % widget.itemsPerRow == 0) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: List.generate(
                  widget.itemsPerRow,
                  (innerIndex) {
                    final channelIndex = index + innerIndex;
                    if (channelIndex >= channels.length) {
                      return const SizedBox.shrink();
                    }
                    return _buildChannelItem(channelIndex);
                  },
                ),
              );
            }
            return const SizedBox.shrink(); // Empty placeholder for padding
          },
        ),
      ),
    );
  }
  Widget _buildChannelItem(int index) {
    return Container(
      height: 117.h,
      width: 100.w,
      margin: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Tooltip(
        message: channels[index].toString(),
        child: Column(
          children: [
            Radio<String>(
              value: channels[index],
              groupValue: widget.selectedChannel,
              onChanged: (value) {
                setState(() {
                  widget.selectedChannel = value ?? "none";
                });
                widget.onChange!(value);
              },
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Image.asset(
                images[index],
                color: images[index] == "assets/images/credit_card.png"
                    ? AppColors.primary
                    : null,
                height: 45.spMin,
                width: 60.spMin,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
