// To parse this JSON data, do
//
//     final SignatoryTransaction = SignatoryTransactionFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/chama/signatory_transactions_model.dart';

List<SignatoryTransaction> SignatoryTransactionFromJson(String str) =>
    List<SignatoryTransaction>.from(
        json.decode(str).map((x) => SignatoryTransaction.fromJson(x)));

String SignatoryTransactionToJson(List<SignatoryTransaction> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SignatoryTransaction {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String status;
  final int? initiatorId;
  final Initiator? initiator;
  final DateTime? initiatedAt;
  final int? kittyId;
  final int? totalSignaturesRequired;
  final dynamic processedById;
  final Initiator? processedBy;
  final String processedByPhone;
  final DateTime? processedAt;
  final String approvalStatus;
  final String response;
  final int? amount;
  final String reason;
  final String receiverAccount;
  final String receiverAccountRef;
  final int? channelCode;
  final String channelName;
  final String transferMode;
  final String transactionType;
  final String entityPayload;
  final MetaData? metaData;
  final int? metadataId;
  final List<Signature>? signatures;
//from chama
  final int? chamaId;
  final ApprovedBy? approvedBy;
  final String? approvedByPhone;
  final DateTime? approvedAt;
  final int? initatorId;
  final ApprovedBy? initator;
  final String? category;
  final List<SignaturesResponse>? signaturesResponse;

  SignatoryTransaction({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.status = '',
    this.initiatorId,
    this.initiator,
    this.initiatedAt,
    this.kittyId,
    this.totalSignaturesRequired,
    this.processedById,
    this.processedBy,
    this.processedByPhone = '',
    this.processedAt,
    this.approvalStatus = '',
    this.response = '',
    this.amount,
    this.reason = '',
    this.receiverAccount = '',
    this.receiverAccountRef = '',
    this.channelCode,
    this.channelName = '',
    this.transferMode = '',
    this.transactionType = '',
    this.entityPayload = '',
    this.metaData,
    this.metadataId,
    this.signatures,
    //from chama
    this.chamaId = 0,
    this.approvedBy,
    this.approvedByPhone = '',
    this.approvedAt,
    this.initatorId,
    this.initator,
    this.category,
    this.signaturesResponse,
  });

  factory SignatoryTransaction.fromJson(Map<String, dynamic> json) =>
      SignatoryTransaction(
          id: json["ID"] ?? 0,
          createdAt: json["CreatedAt"] != null
              ? DateTime.parse(json["CreatedAt"])
              : null,
          updatedAt: json["UpdatedAt"] != null
              ? DateTime.parse(json["UpdatedAt"])
              : null,
          deletedAt: json["DeletedAt"],
          status: json["status"] ?? '',
          initiatorId: json["initiator_id"],
          initiator: json["initiator"] != null
              ? Initiator.fromJson(json["initiator"])
              : null,
          initiatedAt: json["initiated_at"] != null
              ? DateTime.parse(json["initiated_at"])
              : null,
          kittyId: json["kitty_id"] ?? 0,
          totalSignaturesRequired: json["total_signatures_required"],
          processedById: json["processed_by_id"],
          processedBy: json["processed_by"] != null
              ? Initiator.fromJson(json["processed_by"])
              : null,
          processedByPhone: json["processed_by_phone"] ?? '',
          processedAt: json["processed_at"] != null
              ? DateTime.parse(json["processed_at"])
              : null,
          approvalStatus: json["approval_status"] ?? '',
          response: json["response"] ?? '',
          amount: json["amount"],
          reason: json["reason"] ?? '',
          receiverAccount: json["receiver_account"] ?? '',
          receiverAccountRef: json["receiver_account_ref"] ?? '',
          channelCode: json["channel_code"],
          channelName: json["channel_name"] ?? '',
          transferMode: json["transfer_mode"] ?? '',
          transactionType: json["transaction_type"] ?? '',
          entityPayload: json["entity_payload"] ?? '',
          metaData: json["meta_data"] != null
              ? MetaData.fromJson(json["meta_data"])
              : null,
          metadataId: json["metadata_id"],
          signatures: json["signatures"] != null
              ? List<Signature>.from(
                  json["signatures"].map((x) => Signature.fromJson(x)))
              : [],
          //from chama
          chamaId: json["chama_id"] ?? 0,
          initatorId: json["initiator_id"],
          signaturesResponse: json["signatures_res"] != null
              ? List<SignaturesResponse>.from(json["signatures_res"]
                  .map((x) => SignaturesResponse.fromJson(x)))
              : [],
          initator: json["initator"] != null
              ? ApprovedBy.fromJson(json["initator"])
              : null,
          category: json["category"] ?? '',
          approvedBy: json["approved_by"] != null
              ? ApprovedBy.fromJson(json["approved_by"])
              : null,
          approvedByPhone: json["approved_by_phone"] ?? '',
          approvedAt: json["approved_at"] != null
              ? DateTime.parse(json["approved_at"])
              : null);

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "status": status,
        "initiator_id": initiatorId,
        "initiator": initiator?.toJson(),
        "initiated_at": initiatedAt?.toIso8601String() ?? '',
        "kitty_id": kittyId,
        "total_signatures_required": totalSignaturesRequired ?? 0,
        "processed_by_id": processedById,
        "processed_by": processedBy?.toJson(),
        "processed_by_phone": processedByPhone,
        "processed_at": processedAt?.toIso8601String() ?? '',
        "approval_status": approvalStatus,
        "response": response,
        "amount": amount ?? 0,
        "reason": reason,
        "receiver_account": receiverAccount,
        "receiver_account_ref": receiverAccountRef,
        "channel_code": channelCode ?? 0,
        "channel_name": channelName,
        "transfer_mode": transferMode,
        "transaction_type": transactionType,
        "entity_payload": entityPayload,
        "meta_data": metaData?.toJson(),
        "metadata_id": metadataId ?? 0,
        "signatures": signatures != null
            ? List<dynamic>.from(signatures!.map((x) => x.toJson()))
            : [],
        //from chama
        "chama_id": chamaId ?? 0,
        "approved_by": approvedBy?.toJson(),
        "approved_by_phone": approvedByPhone,
        "approved_at": approvedAt?.toIso8601String() ?? '',
        // "initiator_id": initatorId,
        // "initiator": initator?.toJson(),
        "category": category
      };
}

class Initiator {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int? userId;
  final String firstName;
  final String lastName;
  final String role;
  final bool isSignatory;
  final int? kittyId;
  final String status;
  final String phoneNumber;
  final String whatsappNumber;
  final String email;

  Initiator({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.firstName = '',
    this.lastName = '',
    this.role = '',
    this.isSignatory = false,
    this.kittyId,
    this.status = '',
    this.phoneNumber = '',
    this.whatsappNumber = '',
    this.email = '',
  });

  factory Initiator.fromJson(Map<String, dynamic> json) => Initiator(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        firstName: json["first_name"] ?? "" ?? '',
        lastName: json["last_name"] ?? json["second_name"] ?? "" ?? '',
        role: json["role"] ?? '',
        isSignatory: json["is_signatory"] ?? false,
        kittyId: json["kitty_id"],
        status: json["status"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        whatsappNumber: json["whatsapp_number"] ?? '',
        email: json["email"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "user_id": userId ?? 0,
        "first_name": firstName,
        "last_name": lastName,
        "second_name": lastName,
        "role": role,
        "is_signatory": isSignatory,
        "kitty_id": kittyId ?? 0,
        "status": status,
        "phone_number": phoneNumber,
        "whatsapp_number": whatsappNumber,
        "email": email,
      };
}

class MetaData {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String reason;
  final String reqBody;
  final String reference;

  MetaData({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.reason = '',
    this.reqBody = '',
    this.reference = '',
  });

  factory MetaData.fromJson(Map<String, dynamic> json) => MetaData(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        reason: json["reason"] ?? '',
        reqBody: json["req_body"] ?? '',
        reference: json["reference"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "reason": reason,
        "req_body": reqBody,
        "reference": reference,
      };
}

class Signature {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int? signatoryTransactionId;
  final int? userId;
  final int? kittyId;
  final int? delegateId;
  final Initiator? delegate;
  final String delegatePhone;
  final String checkoutRequestId;
  final bool isApproved;
  final String comment;
  final String status;
  final String latitude;
  final String longitude;
  final String deviceId;
  final String deviceModel;

  Signature({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.signatoryTransactionId,
    this.userId,
    this.kittyId,
    this.delegateId,
    this.delegate,
    this.delegatePhone = '',
    this.checkoutRequestId = '',
    this.isApproved = false,
    this.comment = '',
    this.status = '',
    this.latitude = '',
    this.longitude = '',
    this.deviceId = '',
    this.deviceModel = '',
  });

  factory Signature.fromJson(Map<String, dynamic> json) => Signature(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        signatoryTransactionId: json["signatory_transaction_id"],
        userId: json["user_id"],
        kittyId: json["kitty_id"],
        delegateId: json["delegate_id"],
        delegate: json["delegate"] != null
            ? Initiator.fromJson(json["delegate"])
            : null,
        delegatePhone: json["delegate_phone"] ?? '',
        checkoutRequestId: json["checkout_request_id"] ?? '',
        isApproved: json["is_approved"] ?? false,
        comment: json["comment"] ?? '',
        status: json["status"] ?? '',
        latitude: json["latitude"] ?? '',
        longitude: json["longitude"] ?? '',
        deviceId: json["device_id"] ?? '',
        deviceModel: json["device_model"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "signatory_transaction_id": signatoryTransactionId ?? 0,
        "user_id": userId ?? 0,
        "kitty_id": kittyId ?? 0,
        "delegate_id": delegateId ?? 0,
        "delegate": delegate?.toJson(),
        "delegate_phone": delegatePhone,
        "checkout_request_id": checkoutRequestId,
        "is_approved": isApproved,
        "comment": comment,
        "status": status,
        "latitude": latitude,
        "longitude": longitude,
        "device_id": deviceId,
        "device_model": deviceModel,
      };
}

class SignaturesResponse {
  final int memberId;
  final bool isApproved;
  final String comment;
  final String phoneNumber;
  final String fullNames;
  final String checkoutRequestId;

  SignaturesResponse({
    this.memberId = 0,
    this.isApproved = false,
    this.comment = '',
    this.phoneNumber = '',
    this.fullNames = '',
    this.checkoutRequestId = '',
  });

  factory SignaturesResponse.fromJson(Map<String, dynamic> json) =>
      SignaturesResponse(
        memberId: json['member_id'] ?? 0,
        isApproved: json['is_approved'] ?? false,
        comment: json['comment'] ?? '',
        phoneNumber: json['phone_number'] ?? '',
        fullNames: json['full_names'] ?? '',
        checkoutRequestId: json['checkout_request_id'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'member_id': memberId,
        'is_approved': isApproved,
        'comment': comment,
        'phone_number': phoneNumber,
        'full_names': fullNames,
        'checkout_request_id': checkoutRequestId,
      };

  static List<SignaturesResponse> fromJsonList(String jsonString) {
    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList.map((json) => SignaturesResponse.fromJson(json)).toList();
  }
}
