import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/contr_kitty_model.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/widgets/whatsapp_groups/whatsapp_group_type.dart';
import 'package:onekitty/widgets/whatsapp_groups/add_whatsapp_group_dialog.dart';
import 'package:onekitty/widgets/whatsapp_groups/whatsapp_group_tile.dart';

/// Unified WhatsApp Groups Widget that can be used across Events, Chama, and Kitties
class WhatsAppGroupsWidget extends StatefulWidget {
  final WhatsAppGroupType type;
  final int? entityId; // Can be eventId, chamaId, or kittyId
  final String? entityTitle; // Title of the event, chama, or kitty
  final bool canAddGroup; // Whether user can add groups
  final VoidCallback? onRefresh; // Callback for refreshing parent data

  const WhatsAppGroupsWidget({
    super.key,
    required this.type,
    this.entityId,
    this.entityTitle,
    this.canAddGroup = true,
    this.onRefresh,
  });

  @override
  State<WhatsAppGroupsWidget> createState() => _WhatsAppGroupsWidgetState();
}

class _WhatsAppGroupsWidgetState extends State<WhatsAppGroupsWidget> {
  final RxBool isLoading = false.obs;
  final RxList<dynamic> whatsappGroups = <dynamic>[].obs;

  // Controllers
  ContributeController? contributeController;
  ChamaController? chamaController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _fetchWhatsAppGroups();
  }

  void _initializeControllers() {
    switch (widget.type) {
      case WhatsAppGroupType.kitty:
      case WhatsAppGroupType.event:
        contributeController = Get.put(ContributeController());
        break;
      case WhatsAppGroupType.chama:
        chamaController = Get.put(ChamaController());
        break;
    }
  }

  Future<void> _fetchWhatsAppGroups() async {
    if (widget.entityId == null) return;

    try {
      isLoading(true);
      
      switch (widget.type) {
        case WhatsAppGroupType.kitty:
        case WhatsAppGroupType.event:
          if (contributeController != null) {
            await contributeController!.getWhatsapp(id: widget.entityId!);
            whatsappGroups.assignAll(contributeController!.whatsappList);
          }
          break;
        case WhatsAppGroupType.chama:
          if (chamaController != null) {
            await chamaController!.getAllChamaDetails(chamaId: widget.entityId!);
            whatsappGroups.assignAll(chamaController!.notifications);
          }
          break;
      }
    } finally {
      isLoading(false);
    }
  }

  Future<void> _toggleWhatsAppGroup(dynamic group, bool isActive) async {
    if (widget.entityId == null) return;

    try {
      bool result = false;
      
      switch (widget.type) {
        case WhatsAppGroupType.kitty:
        case WhatsAppGroupType.event:
          if (contributeController != null && group is Notifications) {
            result = await contributeController!.toggleWhatsapp(
              id: group.id ?? 0,
              kittyid: widget.entityId!,
              status: isActive ? "ACTIVE" : "INACTIVE",
            );
          }
          break;
        case WhatsAppGroupType.chama:
          if (chamaController != null && group is NotificationCls) {
            result = await chamaController!.toggleWhatsapp(
              nId: group.id ?? 0,
              status: isActive ? "ACTIVE" : "INACTIVE",
            );
          }
          break;
      }

      if (result) {
        await _fetchWhatsAppGroups();
        widget.onRefresh?.call();
      }
    } catch (e) {
      // Handle error
      print('Error toggling WhatsApp group: $e');
    }
  }

  Future<void> _removeWhatsAppGroup(dynamic group) async {
    if (widget.entityId == null) return;

    try {
      bool result = false;
      
      switch (widget.type) {
        case WhatsAppGroupType.kitty:
        case WhatsAppGroupType.event:
          if (contributeController != null && group is Notifications) {
            result = await contributeController!.RmWhatsapp(
              notificationId: group.id ?? 0,
              kittyId: widget.entityId!,
            );
          }
          break;
        case WhatsAppGroupType.chama:
          if (chamaController != null && group is NotificationCls) {
            result = await chamaController!.RmWhatsapp(
              nId: group.id ?? 0,
              chId: group.chamaId ?? widget.entityId ?? 0,
            );
          }
          break;
      }

      if (result) {
        await _fetchWhatsAppGroups();
        widget.onRefresh?.call();
      }
    } catch (e) {
      // Handle error
      print('Error removing WhatsApp group: $e');
    }
  }

  void _showWhatsAppGroupsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'WhatsApp Groups',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (widget.canAddGroup)
                    IconButton(
                      onPressed: () => _showAddWhatsAppGroupDialog(context),
                      icon: Icon(Icons.add_circle, color: Theme.of(context).primaryColor),
                    ),
                ],
              ),
              const Divider(),
              
              // Content
              Flexible(
                child: Obx(() {
                  if (isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (whatsappGroups.isEmpty) {
                    return _buildEmptyState();
                  }

                  return ListView.separated(
                    shrinkWrap: true,
                    itemCount: whatsappGroups.length,
                    separatorBuilder: (_, __) => const Divider(),
                    itemBuilder: (context, index) {
                      final group = whatsappGroups[index];
                      return WhatsAppGroupTile(
                        group: group,
                        type: widget.type,
                        onToggle: (isActive) => _toggleWhatsAppGroup(group, isActive),
                        onRemove: () => _showRemoveConfirmation(context, group),
                      );
                    },
                  );
                }),
              ),
              
              // Close button
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddWhatsAppGroupDialog(BuildContext context) {
    Navigator.of(context).pop(); // Close current dialog
    showDialog(
      context: context,
      builder: (context) => AddWhatsAppGroupDialog(
        type: widget.type,
        entityId: widget.entityId,
        entityTitle: widget.entityTitle,
        onSuccess: () {
          _fetchWhatsAppGroups();
          widget.onRefresh?.call();
        },
      ),
    );
  }

  void _showRemoveConfirmation(BuildContext context, dynamic group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove WhatsApp Group'),
        content: Text(widget.type.removeConfirmationMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _removeWhatsAppGroup(group);
            },
            child: const Text('Remove', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/whatsapp.png',
          height: 48,
          width: 48,
        ),
        const SizedBox(height: 16),
        Text(
          widget.type.emptyStateMessage,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          widget.type.contextDescription,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        if (widget.canAddGroup) ...[
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showAddWhatsAppGroupDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Group'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showWhatsAppGroupsDialog(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            CustomImageView(
              imagePath: AssetUrl.whatsapp,
              height: 32.h,
              width: 32.w,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'WhatsApp Groups',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(() {
                    if (isLoading.value) {
                      return Text(
                        'Loading...',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      );
                    }
                    
                    final count = whatsappGroups.length;
                    return Text(
                      count == 0 
                        ? 'No groups connected'
                        : '$count group${count == 1 ? '' : 's'} connected',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    );
                  }),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }
}