import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:onekitty/helpers/show_toast.dart';

/// Service for handling deferred permissions
/// Requests permissions only when user actually needs them
class PermissionService extends GetxService {
  static PermissionService get instance => Get.find<PermissionService>();

  // Track permission states
  final RxBool _contactsPermissionGranted = false.obs;
  final RxBool _cameraPermissionGranted = false.obs;
  final RxBool _storagePermissionGranted = false.obs;

  bool get contactsPermissionGranted => _contactsPermissionGranted.value;
  bool get cameraPermissionGranted => _cameraPermissionGranted.value;
  bool get storagePermissionGranted => _storagePermissionGranted.value;

  @override
  void onInit() {
    super.onInit();
    _checkExistingPermissions();
  }

  /// Check existing permissions without requesting
  Future<void> _checkExistingPermissions() async {
    _contactsPermissionGranted.value = await Permission.contacts.isGranted;
    _cameraPermissionGranted.value = await Permission.camera.isGranted;
    _storagePermissionGranted.value = await Permission.storage.isGranted;
  }

  /// Request contacts permission with user-friendly dialog
  Future<bool> requestContactsPermission({
    required BuildContext context,
    String? customMessage,
  }) async {
    // Check if already granted
    if (_contactsPermissionGranted.value) return true;

    // Show explanation dialog first
    bool shouldRequest = await _showPermissionExplanationDialog(
      context: context,
      title: 'Contacts Access',
      message: customMessage ?? 
        'We need access to your contacts to help you easily invite friends and family to your groups. This makes it faster to add members without typing phone numbers manually.',
      icon: Icons.contacts,
    );

    if (!shouldRequest) return false;

    // Request permission
    final status = await Permission.contacts.request();
    _contactsPermissionGranted.value = status.isGranted;

    if (!status.isGranted) {
      _handlePermissionDenied(
        context: context,
        permissionName: 'Contacts',
        onOpenSettings: () => openAppSettings(),
      );
    }

    return status.isGranted;
  }

  /// Request camera permission with user-friendly dialog
  Future<bool> requestCameraPermission({
    required BuildContext context,
    String? customMessage,
  }) async {
    if (_cameraPermissionGranted.value) return true;

    bool shouldRequest = await _showPermissionExplanationDialog(
      context: context,
      title: 'Camera Access',
      message: customMessage ?? 
        'We need camera access to capture photos for your profile, ID verification, or event documentation.',
      icon: Icons.camera_alt,
    );

    if (!shouldRequest) return false;

    final status = await Permission.camera.request();
    _cameraPermissionGranted.value = status.isGranted;

    if (!status.isGranted) {
      _handlePermissionDenied(
        context: context,
        permissionName: 'Camera',
        onOpenSettings: () => openAppSettings(),
      );
    }

    return status.isGranted;
  }

  /// Request storage permission with user-friendly dialog
  Future<bool> requestStoragePermission({
    required BuildContext context,
    String? customMessage,
  }) async {
    if (_storagePermissionGranted.value) return true;

    bool shouldRequest = await _showPermissionExplanationDialog(
      context: context,
      title: 'Storage Access',
      message: customMessage ?? 
        'We need storage access to save files, export statements, and manage your documents.',
      icon: Icons.folder,
    );

    if (!shouldRequest) return false;

    final status = await Permission.storage.request();
    _storagePermissionGranted.value = status.isGranted;

    if (!status.isGranted) {
      _handlePermissionDenied(
        context: context,
        permissionName: 'Storage',
        onOpenSettings: () => openAppSettings(),
      );
    }

    return status.isGranted;
  }

  /// Get contacts with permission handling
  Future<List<Contact>> getContactsWithPermission({
    required BuildContext context,
    String? customMessage,
  }) async {
    bool hasPermission = await requestContactsPermission(
      context: context,
      customMessage: customMessage,
    );

    if (!hasPermission) return [];

    try {
      return await FlutterContacts.getContacts(withProperties: true);
    } catch (e) {
      ToastUtils.showErrorToast(
        context,
        'Failed to load contacts: ${e.toString()}',
        'Error',
      );
      return [];
    }
  }

  /// Show permission explanation dialog
  Future<bool> _showPermissionExplanationDialog({
    required BuildContext context,
    required String title,
    required String message,
    required IconData icon,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(width: 12),
              Text(title),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16, height: 1.4),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Allow'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Handle permission denied scenarios
  void _handlePermissionDenied({
    required BuildContext context,
    required String permissionName,
    required VoidCallback onOpenSettings,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text('$permissionName Permission Required'),
          content: Text(
            'To use this feature, please enable $permissionName permission in your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onOpenSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Reset permission states (useful for testing)
  void resetPermissions() {
    _contactsPermissionGranted.value = false;
    _cameraPermissionGranted.value = false;
    _storagePermissionGranted.value = false;
  }
}
