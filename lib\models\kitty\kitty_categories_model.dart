// To parse this JSON data, do
//
//     final kittyCategoriesModel = kittyCategoriesModelFromJson(jsonString);

import 'dart:convert';

List<KittyCategoriesModel> kittyCategoriesModelFromJson(String str) =>
    List<KittyCategoriesModel>.from(
        json.decode(str).map((x) => KittyCategoriesModel.fromJson(x)));

String kittyCategoriesModelToJson(List<KittyCategoriesModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class KittyCategoriesModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? name;
  final String? description;
  final String? type;
  final String? code;

  KittyCategoriesModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name = '',
    this.description = '',
    this.type = '',
    this.code = '',
  });

  factory KittyCategoriesModel.fromJson(Map<String, dynamic> json) =>
      KittyCategoriesModel(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        name: json["name"] ?? '',
        description: json["description"] ?? '',
        type: json["type"] ?? '',
        code: json["code"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "name": name,
        "description": description,
        "type": type,
        "code": code,
      };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KittyCategoriesModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'KittyCategoriesModel(id: $id, name: $name, type: $type)';
}