import 'package:time_since/time_since.dart';

String highPrecisiontimeSince(
  DateTime date, {
  String preffixFutureDate = "ends in",
  String preffixPastDate = "",
  String suffixFutureDate = "",
  String suffixPastDate = "ago",
  int maxPrecision = 1,
}) {
  try {
    const int oneDayMs = 86400000; // 1 day in milliseconds
    const int threeDaysMs = 259200000; // 3 days in milliseconds
    const int oneMonthMs = 2592000000; // 30 days in milliseconds (approximate)
    const int oneYearMs = 31536000000; // 365 days in milliseconds (approximate)
    final int differenceMs =
        DateTime.now().millisecondsSinceEpoch - date.millisecondsSinceEpoch;
    final bool future = differenceMs < 0;

    if (future) {
      maxPrecision = 2;
    }

    if ((differenceMs < oneDayMs)) {
      maxPrecision = 1;
    } else if ((differenceMs >= oneDayMs) && (differenceMs < threeDaysMs)) {
      maxPrecision = 2;
    } else if ((differenceMs >= threeDaysMs) && (differenceMs < oneMonthMs)) {
      maxPrecision = 1;
    } else if ((differenceMs >= oneMonthMs) && (differenceMs < oneYearMs)) {
      maxPrecision = 2;
    } else if ((differenceMs >= oneYearMs)) {
      maxPrecision = 3;
    } else {
      maxPrecision = 1;
    }

    final options = TimeFormatOptions(
      maxPrecision: maxPrecision,
      addAgo: false,
    );

    return "${future ? '$preffixFutureDate ' : preffixPastDate} ${timeSince(date.toLocal().millisecondsSinceEpoch, options)} ${future ? suffixFutureDate : suffixPastDate}";
  } catch (e) {
    print("$e");
    return '';
  }
}


// String timeSince(DateTime date) {


//   final now = DateTime.now();
//   final difference = date.difference(now);

//   if (difference.isNegative) {
//     final daysAgo = difference.inDays.abs();
//     if (daysAgo == 0) {
//       final hoursAgo = difference.inHours.abs();
//       if (hoursAgo == 0) {
//         final minutesAgo = difference.inMinutes.abs();
//         return minutesAgo == 1 ? '1 minute ago' : '$minutesAgo minutes ago';
//       } else {
//         return hoursAgo == 1 ? '1 hour ago' : '$hoursAgo hours ago';
//       }
//     } else if (daysAgo < 7) {
//       return daysAgo == 1 ? '1 day ago' : '$daysAgo days ago';
//     } else {
//       final weeksAgo = (daysAgo / 7).floor();
//       return weeksAgo == 1 ? '1 week ago' : '$weeksAgo weeks ago';
//     }
//   } else {
//     final daysrem = difference.inDays;
//     if (daysrem == 0) {
//       final hoursrem = difference.inHours;
//       if (hoursrem == 0) {
//         final minutesrem = difference.inMinutes;
//         return minutesrem == 1 ? '1 minute rem' : '$minutesrem minutes rem';
//       } else {
//         return hoursrem == 1 ? '1 hour rem' : '$hoursrem hours rem';
//       }
//     } else if (daysrem < 7) {
//       return daysrem == 1 ? '1 day rem' : '$daysrem days rem';
//     } else {
//       final weeksrem = (daysrem / 7).floor();
//       return weeksrem == 1 ? '1 week rem' : '$weeksrem weeks rem';
//     }
//   }
// }
