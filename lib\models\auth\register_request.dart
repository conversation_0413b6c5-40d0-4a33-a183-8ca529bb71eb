// To parse this JSON data, do
//
//     final registerRequest = registerRequestFromJson(jsonString);

import 'dart:convert';

RegisterRequest registerRequestFromJson(String str) => RegisterRequest.fromJson(json.decode(str));

String registerRequestToJson(RegisterRequest data) => json.encode(data.toJson());

class RegisterRequest {
    String? phoneNumber;
    String? countryCode;
    String? latitude;
    String? longitude;
    String? firstName;
    String? secondName;

    RegisterRequest({
        this.phoneNumber,
        this.countryCode,
        this.latitude,
        this.longitude,
        this.firstName,
        this.secondName,
    });

    factory RegisterRequest.fromJson(Map<String, dynamic> json) => RegisterRequest(
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["country_code"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
    );

    Map<String, dynamic> to<PERSON>son() => {
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "latitude": latitude,
        "longitude": longitude,
        "first_name": firstName,
        "second_name": secondName,
    };
}
