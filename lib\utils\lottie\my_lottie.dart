import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:snow_fall_animation/snow_fall_animation.dart';

bool isChristmas() {
  final now = DateTime.now();
  return now.month == 12 || (now.month == 1 && (now.day == 1 || now.day == 2));
}

class MyLottie extends StatelessWidget {
  const MyLottie({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/christmas5.json',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie1 extends StatelessWidget {
  const MyLottie1({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Image.asset(
            'assets/lottie_animations/happy_holidays.png',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie2 extends StatelessWidget {
  const MyLottie2({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/socks.json',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie3 extends StatelessWidget {
  const MyLottie3({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/xmas.json',
            width: 90,
            height: 90,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie4 extends StatelessWidget {
  const MyLottie4({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/anim1.json',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie5 extends StatelessWidget {
  const MyLottie5({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/Christmas1.lottie',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie6 extends StatelessWidget {
  const MyLottie6({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/Christmas2.lottie',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie7 extends StatelessWidget {
  const MyLottie7({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/Christmas3.lottie',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MyLottie8 extends StatelessWidget {
  const MyLottie8({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? Lottie.asset(
            'assets/lottie_animations/Christmas4.lottie',
            width: 200,
            height: 200,
            fit: BoxFit.fill,
          )
        : const SizedBox();
  }
}

class MySnowFall extends StatelessWidget {
  const MySnowFall({super.key});

  @override
  Widget build(BuildContext context) {
    return isChristmas()
        ? const SnowFallAnimation(
            config: SnowfallConfig(
              numberOfSnowflakes: 12,
              speed: 5.0,
              useEmoji: true,
              customEmojis: ['❄️', '❅', '❆'],
            ),
          )
        : const SizedBox();
  }
}
