// To parse this JSON data, do
//
//     final editChamaSettingsRequest = editChamaSettingsRequestFromJson(jsonString);

import 'dart:convert';

EditChamaSettingsRequest editChamaSettingsRequestFromJson(String str) => EditChamaSettingsRequest.fromJson(json.decode(str));

String editChamaSettingsRequestToJson(EditChamaSettingsRequest data) => json.encode(data.toJson());

class EditChamaSettingsRequest {
    int? id;
    int? chamaId;
    int? beneficiariesPerCycle;
    double? beneficiaryPercentage;
    int? signatureThreshold;

    EditChamaSettingsRequest({
        this.id,
        this.chamaId,
        this.beneficiariesPerCycle,
        this.beneficiaryPercentage,
        this.signatureThreshold,
    });

    factory EditChamaSettingsRequest.fromJson(Map<String, dynamic> json) => EditChamaSettingsRequest(
        id: json["ID"],
        chamaId: json["chama_id"],
        beneficiariesPerCycle: json["beneficiaries_per_cycle"],
        beneficiaryPercentage: json["beneficiary_percentage"].toDouble(),
        signatureThreshold: json["signature_threshold"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "chama_id": chamaId,
        "beneficiaries_per_cycle": beneficiariesPerCycle,
        "beneficiary_percentage": beneficiaryPercentage,
        "signature_threshold": signatureThreshold,
    };
}
