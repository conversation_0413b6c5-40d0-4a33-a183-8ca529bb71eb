// To parse this JSON data, do
//
//      beneficiaryModel = beneficiaryModelFromJson(jsonString);

import 'dart:convert';

List<BeneficiaryModel> beneficiaryModelFromJson(String str) =>
    List<BeneficiaryModel>.from(
        json.decode(str).map((x) => BeneficiaryModel.fromJson(x)));

String beneficiaryModelToJson(List<BeneficiaryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BeneficiaryModel {
  int id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? accountName;
  int? userId;
  int kittyId;
  String transferMode;
  double? percentage;
  num? amount;
  String channelName;
  int channel;
  String status;
  String accountNumber;
  String accountNumberRef;
  String? splitConfig;
  String phoneNumber;
  String role;
  DateTime? endDate;

  BeneficiaryModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.accountName = '',
    this.userId = 0,
    this.kittyId = 0,
    this.transferMode = '',
    this.percentage = 0.0,
    this.amount = 0.0,
    this.channelName = '',
    this.channel = 0,
    this.status = '',
    this.accountNumber = '',
    this.accountNumberRef = '',
    this.splitConfig = '',
    this.phoneNumber = '',
    this.role = '',
    this.endDate,
  });

  factory BeneficiaryModel.fromJson(Map<String, dynamic> json) =>
      BeneficiaryModel(
        id: json["ID"] ?? 0,
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        accountName: json["account_name"] ?? '',
        userId: json["user_id"] ?? 0,
        kittyId: json["kitty_id"] ?? 0,
        transferMode: json["transfer_mode"] ?? '',
        percentage: double.tryParse(json["percentage"].toString()) ?? 0.0,
        amount: json["amount"] ?? 0,
        channelName: json["channel_name"] ?? '',
        channel: json["channel"] ?? 0,
        status: json["status"] ?? '',
        accountNumber: json["account_number"] ?? '',
        accountNumberRef: json["account_number_ref"] ?? '',
        splitConfig: json["split_config"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        role: json["role"] ?? '',
        endDate:
            json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "account_name": accountName,
        "user_id": userId,
        "kitty_id": kittyId,
        "transfer_mode": transferMode,
        "percentage": percentage,
        "amount": amount,
        "channel_name": channelName,
        "channel": channel,
        "status": status,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
        "split_config": splitConfig,
        "phone_number": phoneNumber,
        "role": role,
        "end_date": endDate?.toIso8601String(),
      };
}
