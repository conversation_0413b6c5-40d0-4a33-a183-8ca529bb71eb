// To parse this JSON data, do
//
//     final penaltyModel = penaltyModelFromJson(jsonString);

import 'dart:convert';

List<PenaltyModel> penaltyModelFromJson(String str) => List<PenaltyModel>.from(json.decode(str).map((x) => PenaltyModel.fromJson(x)));

String penaltyModelToJson(List<PenaltyModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PenaltyModel {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    int? chamaId;
    String? title;
    String? description;
    int? amount;
    int? severity;
    String? status;

    PenaltyModel({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.chamaId,
        this.title,
        this.description,
        this.amount,
        this.severity,
        this.status,
    });

    factory PenaltyModel.fromJson(Map<String, dynamic> json) => PenaltyModel(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        severity: json["severity"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "title": title,
        "description": description,
        "amount": amount,
        "severity": severity,
        "status": status,
    };
}
