class ConfigModel {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? organisationId;
  String? tawkKey;
  String? geminiKey;
  String? gptKey;
  String? phoneNumbers;
  String? whatsappNumbers;
  String? storeUrl;

  ConfigModel(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.organisationId,
      this.tawkKey,
      this.geminiKey,
      this.gptKey,
      this.phoneNumbers,
      this.whatsappNumbers,
      this.storeUrl});

  ConfigModel.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    organisationId = json['organisation_id'];
    tawkKey = json['tawk_key'];
    geminiKey = json['gemini_key'];
    gptKey = json['gpt_key'];
    phoneNumbers = json['phone_numbers'];
    whatsappNumbers = json['whatsapp_numbers'];
    storeUrl = json['app_store_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['organisation_id'] = organisationId;
    data['tawk_key'] = tawkKey;
    data['gemini_key'] = geminiKey;
    data['gpt_key'] = gptKey;
    data['phone_numbers'] = phoneNumbers;
    data['whatsapp_numbers'] = whatsappNumbers;
    data['app_store_url'] = storeUrl;
    return data;
  }
}

class SmsSubscription {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? description;
  String? product;
  int? quantity;
  double? thirdpartyPrice;
  double? pricePerUnit;
  int? charactersPerUnit;
  String? status;

  SmsSubscription(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.description,
      this.product,
      this.quantity,
      this.thirdpartyPrice,
      this.pricePerUnit,
      this.charactersPerUnit,
      this.status});

  SmsSubscription.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    description = json['description'];
    product = json['product'];
    quantity = json['quantity'];
    thirdpartyPrice = json['thirdparty_price'];
    pricePerUnit = json['price_per_unit'];
    charactersPerUnit = json['characters_per_unit'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['description'] = description;
    data['product'] = product;
    data['quantity'] = quantity;
    data['thirdparty_price'] = thirdpartyPrice;
    data['price_per_unit'] = pricePerUnit;
    data['characters_per_unit'] = charactersPerUnit;
    data['status'] = status;
    return data;
  }
}
