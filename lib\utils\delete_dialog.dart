import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'utils_exports.dart';

void showDeleteDialog(
    index, context, bool isLoading, Function() onPress, String text) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          actions: [
            OutlinedButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("No")),
            CustomKtButton(
                width: 55.w,
                height: 35.h,
                isLoading: isLoading,
                onPress: onPress,
                btnText: "Yes")
          ],
          content: Text("Are you sure you want to delete this $text?"),
        );
      });
}
