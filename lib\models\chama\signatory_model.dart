import 'dart:convert';

SignatoryModel signatoryModelFromJson(String str) => SignatoryModel.fromJson(json.decode(str));

String signatoryModelToJson(SignatoryModel data) => json.encode(data.toJson());

class SignatoryModel {
    bool? status;
    String? message;
    List<Datum>? data;

    SignatoryModel({
        this.status,
        this.message,
        this.data,
    });

    factory SignatoryModel.fromJson(Map<String, dynamic> json) => SignatoryModel(
        status: json["status"],
        message: json["message"],
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
    };
}

class Datum {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    int? chamaId;
    int? memberId;
    Member? member;
    String? phoneNumber;
    String? notificationType;
    String? email;
    String? whatsAppNumber;

    Datum({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.chamaId,
        this.memberId,
        this.member,
        this.phoneNumber,
        this.notificationType,
        this.email,
        this.whatsAppNumber,
    });

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        member: Member.fromJson(json["member"]),
        phoneNumber: json["phone_number"] ?? "",
        notificationType: json["notification_type"],
        email: json["email"],
        whatsAppNumber: json["whats_app_number"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "member_id": memberId,
        "member": member!.toJson(),
        "phone_number": phoneNumber,
        "notification_type": notificationType,
        "email": email,
        "whats_app_number": whatsAppNumber,
    };
}

class Member {
    int? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    int? userId;
    String? phoneNumber;
    String? countryCode;
    String? firstName;
    String? secondName;
    String? role;
    dynamic beneficiary;
    int? chamaId;
    int? receivingOrder;
    String? status;

    Member({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.userId,
        this.phoneNumber,
        this.countryCode,
        this.firstName,
        this.secondName,
        this.role,
        this.beneficiary,
        this.chamaId,
        this.receivingOrder,
        this.status,
    });

    factory Member.fromJson(Map<String, dynamic> json) => Member(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["country_code"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        role: json["role"],
        beneficiary: json["beneficiary"],
        chamaId: json["chama_id"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "user_id": userId,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "beneficiary": beneficiary,
        "chama_id": chamaId,
        "receiving_order": receivingOrder,
        "status": status,
    };
}
