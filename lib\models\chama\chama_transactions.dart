// To parse this JSON data, do
//
//     final chamaTransactions = chamaTransactionsFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/transaction_model.dart';

ChamaTransactions chamaTransactionsFromJson(String str) =>
    ChamaTransactions.fromJson(json.decode(str));

String chamaTransactionsToJson(ChamaTransactions data) =>
    json.encode(data.toJson());

class ChamaTransactions {
  bool? status;
  String? message;
  TransDts? data;

  ChamaTransactions({
    this.status,
    this.message,
    this.data,
  });

  factory ChamaTransactions.fromJson(Map<String, dynamic> json) =>
      ChamaTransactions(
        status: json["status"],
        message: json["message"],
        data: TransDts.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
      };
}

class TransDts {
  List<TransactionModel>? transactions;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  TransDts({
    this.transactions,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory TransDts.fromJson(Map<String, dynamic> json) => TransDts(
        transactions: List<TransactionModel>.from(
            json["items"].map((x) => TransactionModel.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(transactions!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}