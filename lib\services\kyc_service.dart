import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/controllers/events/events_controller.dart';

/// Enhanced KYC service with comprehensive debugging and error handling
class KYCDebugService extends GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  // Debug states
  final RxBool isDebugging = false.obs;
  final RxList<String> debugLogs = <String>[].obs;
  final RxMap<String, dynamic> lastApiResponse = <String, dynamic>{}.obs;
  final RxString lastError = ''.obs;

  // Upload states
  final RxBool isUploading = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final RxString currentStep = ''.obs;

  /// Enhanced KYC submission with comprehensive debugging
  Future<bool> submitKYCWithDebug({
    required BuildContext context,
    required File frontImage,
    required File backImage,
    required File selfieImage,
    required String idNumber,
    String identityType = "NATIONAL_ID",
  }) async {
    try {
      isUploading(true);
      isDebugging(true);
      debugLogs.clear();
      uploadProgress.value = 0.0;

      _addDebugLog('Starting KYC submission process');
      _addDebugLog('Identity Type: $identityType');
      _addDebugLog('ID Number: ${idNumber.replaceRange(4, idNumber.length - 2, '****')}');

      // Step 1: Validate user session
      currentStep.value = 'Validating user session...';
      final user = Get.find<Eventcontroller>().getLocalUser();
      if (user == null) {
        throw Exception('User session not found. Please login again.');
      }
      _addDebugLog('User validated: ID ${user.id}, Phone: ${user.phoneNumber}');
      uploadProgress.value = 0.1;

      // Step 2: Validate images
      currentStep.value = 'Validating images...';
      await _validateImages(frontImage, backImage, selfieImage);
      uploadProgress.value = 0.2;

      // Step 3: Upload images (simulated - in real implementation would upload to Firebase/server)
      currentStep.value = 'Uploading front ID image...';
      final frontUrl = await _simulateImageUpload(frontImage, 'front', user.phoneNumber ?? 'unknown');
      uploadProgress.value = 0.4;

      currentStep.value = 'Uploading back ID image...';
      final backUrl = await _simulateImageUpload(backImage, 'back', user.phoneNumber ?? 'unknown');
      uploadProgress.value = 0.6;

      currentStep.value = 'Uploading selfie image...';
      final selfieUrl = await _simulateImageUpload(selfieImage, 'selfie', user.phoneNumber ?? 'unknown');
      uploadProgress.value = 0.8;

      // Step 4: Submit to API
      currentStep.value = 'Submitting KYC data to server...';
      final success = await _submitToAPI(
        user: user,
        frontUrl: frontUrl,
        backUrl: backUrl,
        selfieUrl: selfieUrl,
        idNumber: idNumber,
        identityType: identityType,
      );
      uploadProgress.value = 1.0;

      if (success) {
        _addDebugLog('KYC submission completed successfully');
        ToastUtils.showSuccessToast(
          context,
          'KYC submitted successfully! Please wait for verification.',
          'Success',
        );
        return true;
      } else {
        throw Exception('API submission failed');
      }

    } catch (e) {
      logger.e('KYC submission error: $e');
      lastError.value = e.toString();
      _addDebugLog('ERROR: ${e.toString()}');
      
      ToastUtils.showErrorToast(
        context,
        'KYC submission failed: ${e.toString()}',
        'Error',
      );
      return false;
    } finally {
      isUploading(false);
      currentStep.value = '';
    }
  }

  /// Validate all images before upload
  Future<void> _validateImages(File front, File back, File selfie) async {
    final images = [
      {'file': front, 'name': 'Front ID'},
      {'file': back, 'name': 'Back ID'},
      {'file': selfie, 'name': 'Selfie'},
    ];

    for (final imageData in images) {
      final file = imageData['file'] as File;
      final name = imageData['name'] as String;

      _addDebugLog('Validating $name...');

      // Check if file exists
      if (!await file.exists()) {
        throw Exception('$name image file not found');
      }

      // Check file size
      final sizeInBytes = await file.length();
      final sizeInMB = sizeInBytes / (1024 * 1024);
      _addDebugLog('$name size: ${sizeInMB.toStringAsFixed(2)} MB');

      if (sizeInBytes == 0) {
        throw Exception('$name image is empty');
      }

      if (sizeInMB > 10) {
        throw Exception('$name image is too large (${sizeInMB.toStringAsFixed(1)} MB). Maximum size is 10 MB.');
      }

      // Check if it's a valid image (basic check)
      final extension = file.path.toLowerCase().split('.').last;
      if (!['jpg', 'jpeg', 'png'].contains(extension)) {
        throw Exception('$name must be a JPG or PNG image');
      }

      _addDebugLog('$name validation passed');
    }
  }

  /// Simulate image upload (replace with actual Firebase/server upload)
  Future<String> _simulateImageUpload(File image, String type, String userPhone) async {
    _addDebugLog('Uploading $type image for user $userPhone');
    
    // Simulate upload delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Generate mock URL (in real implementation, this would be the actual uploaded URL)
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final mockUrl = 'https://storage.example.com/kyc/$userPhone/${type}_$timestamp.jpg';
    
    _addDebugLog('$type image uploaded successfully: $mockUrl');
    return mockUrl;
  }

  /// Submit KYC data to API with enhanced error handling
  Future<bool> _submitToAPI({
    required dynamic user,
    required String frontUrl,
    required String backUrl,
    required String selfieUrl,
    required String idNumber,
    required String identityType,
  }) async {
    try {
      _addDebugLog('Preparing API request...');
      
      final kycStatus = user.kycStatus ?? 0;
      final method = kycStatus == 2 ? Method.PUT : Method.POST;
      
      _addDebugLog('Using HTTP method: ${method.name}');
      _addDebugLog('KYC Status: $kycStatus');

      final requestData = {
        'userId': user.id ?? 0,
        'front': frontUrl,
        'back': backUrl,
        'selfie': selfieUrl,
        'idNumber': idNumber,
        'identityType': identityType,
      };

      _addDebugLog('Request payload prepared (${requestData.length} fields)');

      final response = await apiProvider.request(
        url: ApiUrls.updateKYC,
        method: method,
        params: requestData,
      );

      // Store response for debugging
      lastApiResponse.value = response.data ?? {};
      
      _addDebugLog('API Response received');
      _addDebugLog('Response status: ${response.data?["status"]}');
      _addDebugLog('Response message: ${response.data?["message"]}');

      if (response.data?["status"] == true) {
        _addDebugLog('API submission successful');
        return true;
      } else {
        final errorMessage = response.data?["message"] ?? 'Unknown API error';
        _addDebugLog('API submission failed: $errorMessage');
        throw Exception(errorMessage);
      }

    } catch (e) {
      _addDebugLog('API submission exception: ${e.toString()}');
      rethrow;
    }
  }

  /// Add debug log entry
  void _addDebugLog(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] $message';
    debugLogs.add(logEntry);
    logger.d(logEntry);
  }

  /// Get debug report
  String getDebugReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== KYC Debug Report ===');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln('');
    
    if (lastError.value.isNotEmpty) {
      buffer.writeln('Last Error: ${lastError.value}');
      buffer.writeln('');
    }

    if (lastApiResponse.isNotEmpty) {
      buffer.writeln('Last API Response:');
      buffer.writeln(lastApiResponse.toString());
      buffer.writeln('');
    }

    buffer.writeln('Debug Logs:');
    for (final log in debugLogs) {
      buffer.writeln(log);
    }

    return buffer.toString();
  }

  /// Show debug dialog
  void showDebugDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('KYC Debug Information'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Obx(() => Text(
              getDebugReport(),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            )),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              // Copy to clipboard or share debug report
              // Implementation depends on available packages
            },
            child: const Text('Share Report'),
          ),
        ],
      ),
    );
  }

  /// Clear debug data
  void clearDebugData() {
    debugLogs.clear();
    lastApiResponse.clear();
    lastError.value = '';
    isDebugging.value = false;
  }

  /// Test KYC API connectivity
  Future<bool> testKYCConnectivity() async {
    try {
      _addDebugLog('Testing KYC API connectivity...');
      
      // Make a simple request to check if the API is reachable
      final response = await apiProvider.request(
        url: ApiUrls.updateKYC.replaceAll('update-kyc/', 'test-connection/'),
        method: Method.GET,
      );

      _addDebugLog('Connectivity test response: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      _addDebugLog('Connectivity test failed: ${e.toString()}');
      return false;
    }
  }
}
